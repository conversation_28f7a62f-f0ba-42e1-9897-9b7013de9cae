#!/bin/bash
set -e

echo "Setting up Admin Monorepo development environment..."

# Update system packages
sudo apt-get update

# Install Node.js 18+ (required by package.json engines)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm versions
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Navigate to workspace directory
cd /mnt/persist/workspace

# Fix ESLint configuration import path in apps/web/eslint.config.js
echo "Fixing ESLint configuration import path..."
sed -i 's/@repo\/eslint-config\/next-js/@admin\/eslint-config\/next-js/g' apps/web/eslint.config.js

# Fix ESLint configuration import path in packages/ui/eslint.config.mjs
echo "Fixing UI package ESLint configuration import path..."
sed -i 's/@repo\/eslint-config\/react-internal/@admin\/eslint-config\/react-internal/g' packages/ui/eslint.config.mjs

# Install dependencies using npm (as specified in package.json packageManager)
echo "Installing dependencies..."
npm install

# Build all packages using Turborepo
echo "Building all packages..."
npm run build

# Run type checking
echo "Running type checks..."
npm run check-types

# Run linting
echo "Running linting..."
npm run lint

echo "Setup completed successfully!"
echo "Available commands:"
echo "  npm run dev           - Start all development servers"
echo "  npm run build         - Build all packages"
echo "  npm run lint          - Lint all packages"
echo "  npm run check-types   - Type check all packages"
echo "  npm run storybook     - Start Storybook for UI components"