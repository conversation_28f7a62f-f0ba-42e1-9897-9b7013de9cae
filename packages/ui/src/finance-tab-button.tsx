"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface FinanceTabButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Tab title */
  title?: string;
  /** Whether the tab is active/selected */
  isActive?: boolean;
}

const FinanceTabButton = React.forwardRef<HTMLButtonElement, FinanceTabButtonProps>(
  ({
    className,
    title = "Activos",
    isActive = false,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const tabBorderColor = 'var(--color-stroke)';
    const tabBackgroundColor = isActive ? 'var(--color-frame-primary)' : 'var(--color-background-primary)';
    const tabTextColor = isActive ? 'var(--color-text-inverse)' : 'var(--color-text-secondary)';

    return (
      <button
        ref={ref}
        className={cn(
          "px-[var(--spacing-16)] py-1.5 border rounded-[var(--radius-8)] text-xs font-inter transition-button hover-scale",
          isActive ? "font-semibold" : "font-medium",
          className
        )}
        style={{
          backgroundColor: tabBackgroundColor,
          borderColor: tabBorderColor,
          color: tabTextColor,
        }}
        {...props}
      >
        {title}
      </button>
    );
  }
);

FinanceTabButton.displayName = "FinanceTabButton";

export { FinanceTabButton };
