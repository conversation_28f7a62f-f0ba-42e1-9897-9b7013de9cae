import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { FilterDropdown, type FilterOption } from './filter-dropdown';
import { useState } from 'react';

const meta: Meta<typeof FilterDropdown> = {
  title: 'UI/FilterDropdown',
  component: FilterDropdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    options: {
      control: 'object',
      description: 'Available filter options',
    },
    selectedOptions: {
      control: 'object',
      description: 'Currently selected filter options',
    },
    isOpen: {
      control: 'boolean',
      description: 'Whether the dropdown is open',
    },
    onOptionsChange: {
      action: 'options-changed',
      description: 'Callback when filter options change',
    },
    onFilterClick: {
      action: 'filter-clicked',
      description: 'Callback when filter button is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const teamFilterOptions: FilterOption[] = [
  { id: 'active', label: 'Activos', value: 'active' },
  { id: 'inactive', label: 'Inactivos', value: 'inactive' },
  { id: 'supervisor', label: 'Supervisores', value: 'supervisor' },
  { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
  { id: 'designer', label: 'Diseñadores', value: 'designer' },
  { id: 'technician', label: 'Técnicos', value: 'technician' },
];

const catalogFilterOptions: FilterOption[] = [
  { id: 'product', label: 'Productos', value: 'product' },
  { id: 'service', label: 'Servicios', value: 'service' },
  { id: 'steel', label: 'Acero', value: 'steel' },
  { id: 'aluminum', label: 'Aluminio', value: 'aluminum' },
  { id: 'plastic', label: 'Plástico', value: 'plastic' },
];

export const Default: Story = {
  args: {
    options: teamFilterOptions,
    selectedOptions: [],
    isOpen: false,
  },
};

export const WithSelectedOptions: Story = {
  args: {
    options: teamFilterOptions,
    selectedOptions: [
      { id: 'active', label: 'Activos', value: 'active' },
      { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
    ],
    isOpen: false,
  },
};

export const Interactive: Story = {
  render: () => {
    const [selectedOptions, setSelectedOptions] = useState<FilterOption[]>([]);
    
    return (
      <div className="w-[400px] p-4">
        <h3 className="text-lg font-semibold mb-4">Team Filter Example</h3>
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Buscar miembro del equipo..."
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <FilterDropdown
            options={teamFilterOptions}
            selectedOptions={selectedOptions}
            onOptionsChange={setSelectedOptions}
          />
        </div>
      </div>
    );
  },
};

export const CatalogExample: Story = {
  render: () => {
    const [selectedOptions, setSelectedOptions] = useState<FilterOption[]>([]);
    
    return (
      <div className="w-[400px] p-4">
        <h3 className="text-lg font-semibold mb-4">Catalog Filter Example</h3>
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Buscar en catálogo..."
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <FilterDropdown
            options={catalogFilterOptions}
            selectedOptions={selectedOptions}
            onOptionsChange={setSelectedOptions}
          />
        </div>
      </div>
    );
  },
};

export const MultipleSelections: Story = {
  render: () => {
    const [selectedOptions, setSelectedOptions] = useState<FilterOption[]>([
      { id: 'active', label: 'Activos', value: 'active' },
      { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
      { id: 'designer', label: 'Diseñadores', value: 'designer' },
    ]);
    
    return (
      <div className="w-[400px] p-4">
        <h3 className="text-lg font-semibold mb-4">Multiple Selections</h3>
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Buscar..."
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <FilterDropdown
            options={teamFilterOptions}
            selectedOptions={selectedOptions}
            onOptionsChange={setSelectedOptions}
          />
        </div>
      </div>
    );
  },
};
