import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { 
  Table, 
  TableHeader, 
  TableHeaderRow, 
  TableHeaderCell, 
  TableBody, 
  TableBodyRow, 
  TableBodyCell 
} from './table';

const meta: Meta<typeof Table> = {
  title: 'UI/Table',
  component: Table,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Table>
      <TableHeader>
        <TableHeaderRow>
          <TableHeaderCell variant="first">Product Name</TableHeaderCell>
          <TableHeaderCell variant="middle">Category</TableHeaderCell>
          <TableHeaderCell variant="middle">Quantity</TableHeaderCell>
          <TableHeaderCell variant="last">Price</TableHeaderCell>
        </TableHeaderRow>
      </TableHeader>
      <TableBody>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="first">Lámina Acero</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">Material</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">15 pcs</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="first">$250.00</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="middle">Tubo PVC</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="middle">Material</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="middle">8 pcs</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="middle">$120.00</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="last">Silla 467</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="last">Producto</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="last">3 pcs</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="last">$450.00</TableBodyCell>
        </TableBodyRow>
      </TableBody>
    </Table>
  ),
};

export const TwoColumns: Story = {
  render: () => (
    <Table>
      <TableHeader>
        <TableHeaderRow>
          <TableHeaderCell variant="first">Material</TableHeaderCell>
          <TableHeaderCell variant="last">Quantity</TableHeaderCell>
        </TableHeaderRow>
      </TableHeader>
      <TableBody>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="first">Perfil 1 x 2</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="first">3 metros</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="middle">Perfil 2 x 2</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="middle">3 metros</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="last">Acero Inoxidable 304</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="last">1 plancha</TableBodyCell>
        </TableBodyRow>
      </TableBody>
    </Table>
  ),
};

export const ServiceSpecifications: Story = {
  render: () => (
    <Table>
      <TableHeader>
        <TableHeaderRow>
          <TableHeaderCell variant="first">Material</TableHeaderCell>
          <TableHeaderCell variant="middle">Espesor<div className="text-xs text-[var(--color-text-secondary)] font-normal">(mm)</div></TableHeaderCell>
          <TableHeaderCell variant="middle">Dimensiones</TableHeaderCell>
          <TableHeaderCell variant="middle">Cantidad<div className="text-xs text-[var(--color-text-secondary)] font-normal">(piezas)</div></TableHeaderCell>
          <TableHeaderCell variant="middle">Acabado</TableHeaderCell>
          <TableHeaderCell variant="last">Tolerancia</TableHeaderCell>
        </TableHeaderRow>
      </TableHeader>
      <TableBody>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="first">Acero inoxidable</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">3</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">500x300mm</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">25</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">Desbarbado</TableBodyCell>
          <TableBodyCell variant="last" rowPosition="first">±0.2mm</TableBodyCell>
        </TableBodyRow>
      </TableBody>
    </Table>
  ),
};

export const SingleColumn: Story = {
  render: () => (
    <Table>
      <TableHeader>
        <TableHeaderRow>
          <TableHeaderCell>Document Name</TableHeaderCell>
        </TableHeaderRow>
      </TableHeader>
      <TableBody>
        <TableBodyRow>
          <TableBodyCell variant="first">Especificaciones técnicas - Lámina Acero.pdf</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first">Planos de fabricación - Silla 467.pdf</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first">Manual de instalación.pdf</TableBodyCell>
        </TableBodyRow>
      </TableBody>
    </Table>
  ),
};

export const LargeTable: Story = {
  render: () => (
    <Table>
      <TableHeader>
        <TableHeaderRow>
          <TableHeaderCell variant="first">ID</TableHeaderCell>
          <TableHeaderCell variant="middle">Product</TableHeaderCell>
          <TableHeaderCell variant="middle">Category</TableHeaderCell>
          <TableHeaderCell variant="middle">Material</TableHeaderCell>
          <TableHeaderCell variant="middle">Quantity</TableHeaderCell>
          <TableHeaderCell variant="middle">Unit</TableHeaderCell>
          <TableHeaderCell variant="last">Price</TableHeaderCell>
        </TableHeaderRow>
      </TableHeader>
      <TableBody>
        <TableBodyRow>
          <TableBodyCell variant="first" rowPosition="first">001</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">Lámina Acero</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">Material</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">Acero Inox</TableBodyCell>
          <TableBodyCell variant="middle" rowPosition="first">15</TableBodyCell>
          <TableBodyCell variant="middle">pcs</TableBodyCell>
          <TableBodyCell variant="last">$250.00</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first">002</TableBodyCell>
          <TableBodyCell variant="middle">Tubo PVC</TableBodyCell>
          <TableBodyCell variant="middle">Material</TableBodyCell>
          <TableBodyCell variant="middle">PVC</TableBodyCell>
          <TableBodyCell variant="middle">8</TableBodyCell>
          <TableBodyCell variant="middle">pcs</TableBodyCell>
          <TableBodyCell variant="last">$120.00</TableBodyCell>
        </TableBodyRow>
        <TableBodyRow>
          <TableBodyCell variant="first">003</TableBodyCell>
          <TableBodyCell variant="middle">Silla 467</TableBodyCell>
          <TableBodyCell variant="middle">Producto</TableBodyCell>
          <TableBodyCell variant="middle">Madera/Acero</TableBodyCell>
          <TableBodyCell variant="middle">3</TableBodyCell>
          <TableBodyCell variant="middle">pcs</TableBodyCell>
          <TableBodyCell variant="last">$450.00</TableBodyCell>
        </TableBodyRow>
      </TableBody>
    </Table>
  ),
};
