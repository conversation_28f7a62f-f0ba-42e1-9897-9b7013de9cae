"use client";

import * as React from "react";
import { ChevronLeft } from "lucide-react";

interface DataCreationModalHeaderProps {
  /** Entity type being created */
  entityType: string;
  /** Whether this is a guided conversation flow */
  isGuidedFlow?: boolean;
  /** Current step in guided conversation */
  currentStep?: string;
  /** All conversation steps */
  conversationSteps?: Array<{ type: string; title: string; isComplete: boolean }>;
  /** Callback to close the modal */
  onClose: () => void;
}

export const DataCreationModalHeader: React.FC<DataCreationModalHeaderProps> = ({
  entityType,
  isGuidedFlow,
  currentStep,
  conversationSteps,
  onClose,
}) => {
  const getTitle = () => {
    if (isGuidedFlow && currentStep && conversationSteps) {
      const step = conversationSteps.find(s => s.type === currentStep);
      return step ? step.title : `Create ${entityType}`;
    }
    return `Create ${entityType}`;
  };

  return (
    <div className="flex justify-between items-center mb-[var(--spacing-16)]">
      <div className="flex items-center space-x-[var(--spacing-8)]">
        <button
          onClick={onClose}
          className="p-[var(--spacing-4)] hover:bg-[var(--color-background-secondary)] rounded-[var(--radius-4)] transition-colors"
        >
          <ChevronLeft className="w-5 h-5 text-[var(--color-text-secondary)]" />
        </button>
        <h2 className="text-[var(--typography-h2-size)] font-[var(--typography-h2-weight)] text-[var(--color-text-primary)]">
          {getTitle()}
        </h2>
      </div>
      
      {/* Progress indicator for guided flows */}
      {isGuidedFlow && conversationSteps && (
        <div className="flex items-center space-x-[var(--spacing-4)]">
          {conversationSteps.map((step, index) => (
            <div
              key={step.type}
              className={`w-2 h-2 rounded-full ${
                step.isComplete
                  ? 'bg-[var(--color-primary)]'
                  : step.type === currentStep
                  ? 'bg-[var(--color-primary)] opacity-50'
                  : 'bg-[var(--color-stroke)]'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
