"use client";

import * as React from "react";
import { CloseIcon } from "./icons";
import { cn } from "./lib/utils";

export interface FilterOption {
  id: string;
  label: string;
  value: string;
}

export interface FilterPillsProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Currently selected filter options */
  selectedOptions?: FilterOption[];
  /** Callback when an option is removed */
  onRemoveOption?: (optionId: string) => void;
}

const FilterPills = React.forwardRef<HTMLDivElement, FilterPillsProps>(
  ({
    className,
    selectedOptions = [],
    onRemoveOption,
    ...props
  }, ref) => {
    if (selectedOptions.length === 0) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn("flex flex-wrap gap-2", className)}
        {...props}
      >
        {selectedOptions.map((option) => (
          <div
            key={option.id}
            className="inline-flex items-center gap-2 px-3 py-1 bg-[var(--color-background-primary)] text-[var(--color-text-primary)] text-sm rounded-full border border-[var(--color-stroke)]"
          >
            <button
              onClick={() => onRemoveOption?.(option.id)}
              className="hover:opacity-70 transition-opacity flex items-center justify-center"
            >
              <CloseIcon size={14} />
            </button>
            <span>{option.label}</span>
          </div>
        ))}
      </div>
    );
  }
);

FilterPills.displayName = "FilterPills";

export { FilterPills };
