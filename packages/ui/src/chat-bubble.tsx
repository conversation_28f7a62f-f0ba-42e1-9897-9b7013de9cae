"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface ChatBubbleProps {
  /** Message content */
  message: string;
  /** Whether this is a user message (right side) or AI message (left side) */
  isUser?: boolean;
  /** Timestamp for the message */
  timestamp?: Date;
  /** Whether to show timestamp */
  showTimestamp?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const ChatBubble = React.forwardRef<HTMLDivElement, ChatBubbleProps>(
  ({
    className,
    message,
    isUser = false,
    timestamp,
    showTimestamp = false
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex w-full mb-[var(--spacing-12)]",
          isUser ? "justify-end" : "justify-start",
          className
        )}
      >
        <div className={cn(
          "max-w-[80%] px-[var(--spacing-16)] py-[var(--spacing-12)] rounded-[8px]",
          "text-sm leading-relaxed",
          isUser
            ? "bg-[var(--color-frame-primary)] text-white rounded-br-[2px]"
            : "bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] rounded-bl-[2px]",
          "shadow-sm"
        )}>
          <p className="whitespace-pre-wrap">{message}</p>
          {showTimestamp && timestamp && (
            <div className={cn(
              "text-xs mt-[var(--spacing-4)] opacity-70",
              isUser ? "text-white" : "text-[var(--color-text-secondary)]"
            )}>
              {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
          )}
        </div>
      </div>
    );
  }
);

ChatBubble.displayName = "ChatBubble";

export { ChatBubble };
