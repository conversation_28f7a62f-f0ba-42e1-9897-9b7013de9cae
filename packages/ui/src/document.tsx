"use client";

import * as React from "react";
import { FileText } from "lucide-react";
import { cn } from "./lib/utils";

export interface DocumentProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Document name/title */
  name: string;
  /** Custom document icon */
  icon?: React.ReactNode;
  /** Callback when document is clicked */
  onClick?: () => void;
}

const Document = React.forwardRef<HTMLDivElement, DocumentProps>(
  ({ className, name, icon, onClick, ...props }, ref) => {
    const defaultIcon = icon || (
      <FileText className="w-3 h-3 text-[var(--color-icon-secondary)]" />
    );

    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex items-center gap-[var(--spacing-4)] px-[var(--spacing-8)] py-[var(--spacing-6)] rounded-[var(--radius-4)] bg-[var(--color-background-secondary)]",
          onClick && "cursor-pointer hover:opacity-80 transition-opacity",
          className
        )}
        onClick={onClick}
        {...props}
      >
        <div className="flex-shrink-0">
          {defaultIcon}
        </div>
        <span className="text-xs font-normal text-[var(--color-text-primary)] font-sans">
          {name}
        </span>
      </div>
    );
  }
);

Document.displayName = "Document";

export { Document };
