"use client";

import * as React from "react";
import { cn } from "./lib/utils";
import { TaskForm, type TaskFormData, type TeamMember } from "./task-form";
import { TaskList, type TaskItem } from "./task-list";

export interface TaskManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  projectName: string;
  tasks: TaskItem[];
  teamMembers: TeamMember[];
  onTaskCreate: (data: TaskFormData) => Promise<void>;
  onTaskUpdate: (taskId: string, data: Partial<TaskFormData>) => Promise<void>;
  onTaskDelete: (taskId: string) => Promise<void>;
  onTaskToggle: (taskId: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

type ModalView = 'list' | 'create' | 'edit';

export function TaskManagementModal({
  isOpen,
  onClose,
  projectId,
  projectName,
  tasks,
  teamMembers,
  onTaskCreate,
  onTaskUpdate,
  onTaskDelete,
  onTaskToggle,
  isLoading = false,
  className
}: TaskManagementModalProps) {
  const [currentView, setCurrentView] = React.useState<ModalView>('list');
  const [editingTask, setEditingTask] = React.useState<TaskItem | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Reset view when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setCurrentView('list');
      setEditingTask(null);
    }
  }, [isOpen]);

  const handleTaskCreate = async (data: TaskFormData) => {
    setIsSubmitting(true);
    try {
      await onTaskCreate(data);
      setCurrentView('list');
    } catch (error) {
      console.error('Error creating task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTaskUpdate = async (data: TaskFormData) => {
    if (!editingTask) return;
    
    setIsSubmitting(true);
    try {
      await onTaskUpdate(editingTask.id, data);
      setCurrentView('list');
      setEditingTask(null);
    } catch (error) {
      console.error('Error updating task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTaskEdit = (task: TaskItem) => {
    setEditingTask(task);
    setCurrentView('edit');
  };

  const handleTaskDelete = async (taskId: string) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      try {
        await onTaskDelete(taskId);
      } catch (error) {
        console.error('Error deleting task:', error);
      }
    }
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setEditingTask(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className={cn(
        "relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            {currentView !== 'list' && (
              <button
                onClick={handleBackToList}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
            )}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {currentView === 'list' && 'Task Management'}
                {currentView === 'create' && 'Create New Task'}
                {currentView === 'edit' && 'Edit Task'}
              </h2>
              <p className="text-sm text-gray-500">{projectName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {currentView === 'list' && (
            <TaskList
              tasks={tasks}
              teamMembers={teamMembers}
              onTaskToggle={onTaskToggle}
              onTaskEdit={handleTaskEdit}
              onTaskDelete={handleTaskDelete}
              onTaskCreate={() => setCurrentView('create')}
              isLoading={isLoading}
            />
          )}

          {currentView === 'create' && (
            <TaskForm
              teamMembers={teamMembers}
              onSubmit={handleTaskCreate}
              onCancel={handleBackToList}
              isLoading={isSubmitting}
              submitLabel="Create Task"
            />
          )}

          {currentView === 'edit' && editingTask && (
            <TaskForm
              initialData={{
                name: editingTask.name,
                description: editingTask.description,
                startDate: editingTask.startDate,
                endDate: editingTask.endDate,
                assignedTo: editingTask.assignedTo,
                priority: editingTask.priority,
                estimatedHours: editingTask.estimatedHours,
                tags: editingTask.tags,
              }}
              teamMembers={teamMembers}
              onSubmit={handleTaskUpdate}
              onCancel={handleBackToList}
              isLoading={isSubmitting}
              submitLabel="Update Task"
            />
          )}
        </div>
      </div>
    </div>
  );
}
