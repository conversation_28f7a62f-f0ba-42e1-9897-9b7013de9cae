import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ContextSelection } from './context-selection';
import { ContextDataResult, SelectableItem } from './context-selection';

const meta: Meta<typeof ContextSelection> = {
  title: 'Components/ContextSelection',
  component: ContextSelection,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    editType: {
      control: 'select',
      options: ['add_team_member', 'add_product', 'add_service', 'add_material', 'add_task'],
      description: 'Type of items being selected',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the component is in loading state',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock team members data
const mockTeamMembers: SelectableItem[] = [
  {
    id: 'emp-1',
    name: '<PERSON>',
    description: 'Ingeniero',
    type: 'team_member',
    category: 'Engineering',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 45
    },
    isSelected: false
  },
  {
    id: 'emp-2',
    name: 'Carlos Mendoza',
    description: 'Supervisor de Producción',
    type: 'team_member',
    category: 'Production',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 50
    },
    isSelected: true
  },
  {
    id: 'emp-3',
    name: 'Ana García',
    description: 'Ingeniera de Diseño',
    type: 'team_member',
    category: 'Design',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 48
    },
    isSelected: false
  },
  {
    id: 'emp-4',
    name: 'Roberto Silva',
    description: 'Técnico Especialista',
    type: 'team_member',
    category: 'Technical',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 42
    },
    isSelected: false
  },
  {
    id: 'emp-5',
    name: 'Laura Martínez',
    description: 'Coordinadora de Calidad',
    type: 'team_member',
    category: 'Quality',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 46
    },
    isSelected: false
  },
  {
    id: 'emp-6',
    name: 'Diego Ramírez',
    description: 'Soldador Certificado',
    type: 'team_member',
    category: 'Welding',
    metadata: {
      email: '<EMAIL>',
      hourlyRate: 40
    },
    isSelected: false
  }
];

// Mock catalog items data
const mockCatalogItems: SelectableItem[] = [
  {
    id: 'cat-1',
    name: 'Steel Beam 10m',
    description: 'High-grade structural steel beam',
    type: 'catalog_item',
    category: 'Materials',
    metadata: { price: 250 },
    isSelected: false,
    quantity: 1
  },
  {
    id: 'cat-2',
    name: 'Welding Service',
    description: 'Professional welding service',
    type: 'catalog_item',
    category: 'Services',
    metadata: { price: 80 },
    isSelected: true,
    quantity: 3
  }
];

const teamMembersContextData: ContextDataResult = {
  items: mockTeamMembers,
  totalCount: mockTeamMembers.length,
  categories: ['Engineering', 'Production', 'Design', 'Technical', 'Quality', 'Welding']
};

const catalogItemsContextData: ContextDataResult = {
  items: mockCatalogItems,
  totalCount: mockCatalogItems.length,
  categories: ['Materials', 'Services']
};

export const TeamMemberSelection: Story = {
  args: {
    contextData: teamMembersContextData,
    editType: 'add_team_member',
    loading: false,
    onSelectionChange: (itemId: string, isSelected: boolean) => {
      console.log('Selection changed:', itemId, isSelected);
    },
    onConfirmSelection: () => {
      console.log('Selection confirmed');
    },
  },
};

export const CatalogItemSelection: Story = {
  args: {
    contextData: catalogItemsContextData,
    editType: 'add_product',
    loading: false,
    onSelectionChange: (itemId: string, isSelected: boolean, quantity?: number) => {
      console.log('Selection changed:', itemId, isSelected, quantity);
    },
    onConfirmSelection: () => {
      console.log('Selection confirmed');
    },
  },
};

export const LoadingState: Story = {
  args: {
    contextData: { items: [], totalCount: 0 },
    editType: 'add_team_member',
    loading: true,
    onSelectionChange: () => {},
    onConfirmSelection: () => {},
  },
};

export const EmptyState: Story = {
  args: {
    contextData: { items: [], totalCount: 0 },
    editType: 'add_team_member',
    loading: false,
    onSelectionChange: () => {},
    onConfirmSelection: () => {},
  },
};

// Constrained height story to test scrolling
export const ConstrainedHeight: Story = {
  args: {
    contextData: teamMembersContextData,
    editType: 'add_team_member',
    loading: false,
    onSelectionChange: (itemId: string, isSelected: boolean) => {
      console.log('Selection changed:', itemId, isSelected);
    },
    onConfirmSelection: () => {
      console.log('Selection confirmed');
    },
  },
  decorators: [
    (Story) => (
      <div style={{ height: '400px', border: '1px solid #ccc', padding: '16px' }}>
        <Story />
      </div>
    ),
  ],
};
