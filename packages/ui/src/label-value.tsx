"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface LabelValueProps extends React.HTMLAttributes<HTMLDivElement> {
  /** The label text (will be automatically uppercased) */
  label: string;
  /** The value text to display below the label */
  value: string | number;
  /** Optional custom styling for the label */
  labelClassName?: string;
  /** Optional custom styling for the value */
  valueClassName?: string;
}

const LabelValue = React.forwardRef<HTMLDivElement, LabelValueProps>(
  ({
    className,
    label,
    value,
    labelClassName,
    valueClassName,
    ...props
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex flex-col gap-[2px]", className)}
        {...props}
      >
        {/* Label - Roboto Medium 12px, uppercase */}
        <div className={cn(
          "font-sans font-medium text-xs text-[var(--color-text-secondary)] uppercase",
          labelClassName
        )}>
          {label}
        </div>

        {/* Value - Inter 14px Medium, 2px spacing from label */}
        <div className={cn(
          "font-sans font-medium text-sm text-[var(--color-text-primary)] leading-none",
          valueClassName
        )}>
          {value}
        </div>
      </div>
    );
  }
);

LabelValue.displayName = "LabelValue";

export { LabelValue };
