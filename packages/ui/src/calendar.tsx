"use client";

import * as React from "react";
import { cn } from "./lib/utils";
import { DateNumber } from "./date-number";
import { EventCard } from "./event-card";
import { DayContainerCard } from "./day-container-card";

export interface CalendarEvent {
  text: string;
  state?: 'default' | 'active';
}

export interface CalendarDay {
  number: number;
  state?: 'default' | 'inverse' | 'active';
  containerState?: 'default' | 'past';
  events?: CalendarEvent[];
}

export interface CalendarProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Calendar title */
  title?: string;
  /** Calendar subtitle (month/year) */
  subtitle?: string;
  /** Array of calendar days */
  days?: CalendarDay[];
  /** Week day headers */
  weekHeaders?: string[];
}

const Calendar = React.forwardRef<HTMLDivElement, CalendarProps>(
  ({
    className,
    title = "Calendario",
    subtitle = "Junio 2025",
    days = [],
    weekHeaders = ["Dom", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>áb"],
    ...props
  }, ref) => {
    // Generate default calendar days if none provided
    const defaultDays: CalendarDay[] = Array.from({ length: 30 }, (_, i) => {
      const dayNumber = i + 1;
      const isActive = dayNumber === 3; // Day 3 is active (red circle)
      const isPast = dayNumber < 3; // Days before active day are past

      return {
        number: dayNumber,
        state: isActive ? 'active' : isPast ? 'inverse' : 'default',
        containerState: isPast ? 'past' : 'default',
        events: [4, 12, 19].includes(dayNumber) ? [{ text: "Pago de sueldos", state: 'default' }] :
                dayNumber === 6 ? [{ text: "Reporte mensual", state: 'default' }] : []
      };
    });

    const calendarDays = days.length > 0 ? days : defaultDays;

    return (
      <div
        ref={ref}
        className={cn("h-full", className)}
        {...props}
      >
        {/* Calendar Header */}
        <div className="mb-6">
          <h3 className="modal-sub-header">{title}</h3>
          <h2 className="font-semibold text-lg text-[var(--color-text-primary)] font-sans">
            {subtitle}
          </h2>
        </div>

        {/* Calendar Grid - Fill container height completely */}
        <div className="h-full flex flex-col">
          {/* Week Headers - Fixed height */}
          <div className="grid grid-cols-7 gap-1 flex-shrink-0" style={{ height: '24px' }}>
            {weekHeaders.map((header, index) => (
              <div key={index} className="text-center text-xs font-medium text-[var(--color-text-secondary)] flex items-center justify-center">
                {header}
              </div>
            ))}
          </div>

          {/* Calendar Days Grid - Fill remaining height */}
          <div
            className="grid grid-cols-7 gap-1"
            style={{
              height: 'calc(100% - 24px)',
              gridTemplateRows: 'repeat(5, 1fr)',
              gridTemplateColumns: 'repeat(7, 1fr)'
            }}
          >
            {calendarDays.map((day, index) => (
              <div key={index} className="h-full w-full">
                <DayContainerCard
                  state={day.containerState || 'default'}
                  compact
                  className="h-full w-full flex flex-col"
                >
                  {/* Events - constrained to available space */}
                  {day.events && day.events.length > 0 && (
                    <div className="flex flex-col gap-1 flex-1 overflow-hidden">
                      {day.events.slice(0, 1).map((event, eventIndex) => (
                        <div key={eventIndex} className="flex-shrink-0">
                          <EventCard
                            text={event.text}
                            state={event.state || 'default'}
                          />
                        </div>
                      ))}
                      {day.events.length > 1 && (
                        <div className="text-xs text-[var(--color-text-secondary)] text-center flex-shrink-0">
                          +{day.events.length - 1}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Date number - absolutely positioned in bottom right */}
                  <DateNumber
                    number={day.number}
                    state={day.state || 'default'}
                    positioned
                  />
                </DayContainerCard>
              </div>
            ))}

            {/* Fill remaining grid cells if needed */}
            {Array.from({ length: Math.max(0, 35 - calendarDays.length) }, (_, i) => (
              <div key={`empty-${i}`} className="h-full w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }
);

Calendar.displayName = "Calendar";

export { Calendar };
