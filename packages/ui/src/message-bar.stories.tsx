import type { Meta, StoryObj } from '@storybook/react';
import { MessageBar } from './message-bar';

const meta: Meta<typeof MessageBar> = {
  title: 'Components/MessageBar',
  component: MessageBar,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    message: {
      control: 'text',
      description: 'Message text to display',
    },
    arrowIconSrc: {
      control: 'text',
      description: 'Arrow icon source for the circular button',
    },
    onArrowClick: {
      action: 'arrow-clicked',
      description: 'Callback when arrow button is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    message: 'Pregunta lo que sea',
  },
};

export const CustomMessage: Story = {
  args: {
    message: 'Ask me anything you want',
  },
};

export const WithCustomArrowIcon: Story = {
  args: {
    message: 'Custom message with arrow icon',
    arrowIconSrc: 'https://via.placeholder.com/18x18/333/fff?text=↑',
  },
};

export const Interactive: Story = {
  args: {
    message: 'Click the arrow button',
    onArrowClick: () => alert('Arrow clicked!'),
  },
};
