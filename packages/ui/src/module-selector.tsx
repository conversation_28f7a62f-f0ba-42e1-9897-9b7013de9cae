"use client";

import * as React from "react";
import { cn } from "./lib/utils";

// Module types
export type ProjectModuleType = 
  | 'finance'      // Always required - financial tracking and budgeting
  | 'catalog'      // Products and/or services catalog
  | 'team'         // Team management and resource allocation
  | 'timeline'     // Project timeline and task management
  | 'inventory'    // Materials and inventory management
  | 'logistics';   // Logistics and supply chain management

// Module information
export interface ModuleInfo {
  id: ProjectModuleType;
  name: string;
  description: string;
  icon: string;
  required?: boolean;
  category: 'core' | 'business' | 'operations';
}

// Module recommendation
export interface ModuleRecommendation {
  module: ProjectModuleType;
  confidence: number;
  reasoning: string;
  recommended: boolean;
}

export interface ModuleSelectorProps {
  /** Available modules to select from */
  modules: ModuleInfo[];
  /** Currently selected modules */
  selectedModules: ProjectModuleType[];
  /** AI recommendations for modules */
  recommendations?: ModuleRecommendation[];
  /** Selection method: manual, ai-recommended, or hybrid */
  selectionMethod: 'manual' | 'ai-recommended' | 'hybrid';
  /** Whether to show AI recommendations */
  showRecommendations?: boolean;
  /** Callback when module selection changes */
  onModuleToggle: (module: ProjectModuleType, enabled: boolean) => void;
  /** Callback when selection method changes */
  onSelectionMethodChange: (method: 'manual' | 'ai-recommended' | 'hybrid') => void;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
}

// Default module information
export const DEFAULT_MODULES: ModuleInfo[] = [
  {
    id: 'finance',
    name: 'Finance',
    description: 'Financial tracking, budgeting, and cost management',
    icon: '💰',
    required: true,
    category: 'core'
  },
  {
    id: 'catalog',
    name: 'Catalog',
    description: 'Product and service catalog management',
    icon: '📋',
    category: 'business'
  },
  {
    id: 'team',
    name: 'Team',
    description: 'Team member management and resource allocation',
    icon: '👥',
    category: 'business'
  },
  {
    id: 'timeline',
    name: 'Timeline',
    description: 'Project timeline, tasks, and milestone tracking',
    icon: '📅',
    category: 'business'
  },
  {
    id: 'inventory',
    name: 'Inventory',
    description: 'Materials management and Bill of Materials (BOM)',
    icon: '📦',
    category: 'operations'
  },
  {
    id: 'logistics',
    name: 'Logistics',
    description: 'Supply chain, shipments, and delivery coordination',
    icon: '🚚',
    category: 'operations'
  }
];

const ModuleSelector = React.forwardRef<HTMLDivElement, ModuleSelectorProps>(
  ({
    className,
    modules = DEFAULT_MODULES,
    selectedModules,
    recommendations = [],
    selectionMethod,
    showRecommendations = true,
    onModuleToggle,
    onSelectionMethodChange,
    disabled = false
  }, ref) => {
    
    // Get recommendation for a specific module
    const getRecommendation = (moduleId: ProjectModuleType) => {
      return recommendations.find(r => r.module === moduleId);
    };

    // Check if module is recommended by AI
    const isRecommended = (moduleId: ProjectModuleType) => {
      const rec = getRecommendation(moduleId);
      return rec?.recommended || false;
    };

    // Get confidence score for module
    const getConfidence = (moduleId: ProjectModuleType) => {
      const rec = getRecommendation(moduleId);
      return rec?.confidence || 0;
    };

    return (
      <div
        ref={ref}
        className={cn(
          "space-y-[var(--spacing-16)]",
          className
        )}
      >
        {/* Selection Method */}
        <div className="space-y-[var(--spacing-8)]">
          <h4 className="text-sm font-medium text-[var(--color-text-primary)]">
            Module Selection Method
          </h4>
          <div className="flex gap-[var(--spacing-8)]">
            {[
              { id: 'ai-recommended', label: 'AI Recommended' },
              { id: 'manual', label: 'Manual Selection' },
              { id: 'hybrid', label: 'Hybrid (AI + Manual)' }
            ].map((method) => (
              <button
                key={method.id}
                type="button"
                disabled={disabled}
                onClick={() => onSelectionMethodChange(method.id as any)}
                className={cn(
                  "px-[var(--spacing-12)] py-[var(--spacing-8)] rounded-[var(--radius-6)] text-xs font-medium transition-colors",
                  "border border-[var(--color-stroke)]",
                  selectionMethod === method.id
                    ? "bg-[var(--color-frame-primary)] text-white border-[var(--color-frame-primary)]"
                    : "bg-[var(--color-background-primary)] text-[var(--color-text-secondary)] hover:bg-[var(--color-background-secondary)]",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                {method.label}
              </button>
            ))}
          </div>
        </div>

        {/* AI Recommendations Summary */}
        {showRecommendations && recommendations.length > 0 && (
          <div className="p-[var(--spacing-12)] bg-[var(--color-background-secondary)] rounded-[var(--radius-8)]">
            <h4 className="text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-8)]">
              AI Recommendations
            </h4>
            <div className="text-xs text-[var(--color-text-secondary)]">
              Based on your project description, we recommend {recommendations.filter(r => r.recommended).length} modules
              for optimal project management.
            </div>
          </div>
        )}

        {/* Module Selection Grid */}
        <div className="space-y-[var(--spacing-12)]">
          <h4 className="text-sm font-medium text-[var(--color-text-primary)]">
            Select Project Modules
          </h4>
          
          {/* Group modules by category */}
          {['core', 'business', 'operations'].map((category) => {
            const categoryModules = modules.filter(m => m.category === category);
            if (categoryModules.length === 0) return null;

            return (
              <div key={category} className="space-y-[var(--spacing-8)]">
                <h5 className="text-xs font-medium text-[var(--color-text-secondary)] uppercase tracking-wide">
                  {category === 'core' ? 'Core Modules' : 
                   category === 'business' ? 'Business Modules' : 
                   'Operations Modules'}
                </h5>
                
                <div className="grid grid-cols-1 gap-[var(--spacing-8)]">
                  {categoryModules.map((module) => {
                    const isSelected = selectedModules.includes(module.id);
                    const recommended = isRecommended(module.id);
                    const confidence = getConfidence(module.id);
                    const recommendation = getRecommendation(module.id);

                    return (
                      <div
                        key={module.id}
                        className={cn(
                          "p-[var(--spacing-12)] border rounded-[var(--radius-8)] transition-colors",
                          isSelected
                            ? "border-[var(--color-frame-primary)] bg-[var(--color-frame-primary)]/5"
                            : "border-[var(--color-stroke)] bg-[var(--color-background-primary)]",
                          recommended && "ring-1 ring-[var(--color-frame-primary)]/30",
                          !module.required && !disabled && "cursor-pointer hover:bg-[var(--color-background-secondary)]",
                          module.required && "opacity-75"
                        )}
                        onClick={() => {
                          if (!module.required && !disabled) {
                            onModuleToggle(module.id, !isSelected);
                          }
                        }}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-[var(--spacing-12)] flex-1">
                            <div className="text-lg">{module.icon}</div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-[var(--spacing-8)]">
                                <h6 className="text-sm font-medium text-[var(--color-text-primary)]">
                                  {module.name}
                                </h6>
                                {module.required && (
                                  <span className="text-xs text-[var(--color-text-secondary)] bg-[var(--color-background-secondary)] px-[var(--spacing-6)] py-[var(--spacing-2)] rounded-[var(--radius-4)]">
                                    Required
                                  </span>
                                )}
                                {recommended && (
                                  <span className="text-xs text-[var(--color-frame-primary)] bg-[var(--color-frame-primary)]/10 px-[var(--spacing-6)] py-[var(--spacing-2)] rounded-[var(--radius-4)]">
                                    AI Recommended ({Math.round(confidence * 100)}%)
                                  </span>
                                )}
                              </div>
                              <p className="text-xs text-[var(--color-text-secondary)] mt-[var(--spacing-4)]">
                                {module.description}
                              </p>
                              {recommendation?.reasoning && (
                                <p className="text-xs text-[var(--color-text-secondary)] mt-[var(--spacing-4)] italic">
                                  AI: {recommendation.reasoning}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          {/* Toggle Switch */}
                          <div className="flex-shrink-0">
                            <div
                              className={cn(
                                "w-10 h-5 rounded-full transition-colors relative",
                                isSelected
                                  ? "bg-[var(--color-frame-primary)]"
                                  : "bg-[var(--color-stroke)]",
                                module.required && "opacity-50"
                              )}
                            >
                              <div
                                className={cn(
                                  "w-4 h-4 bg-white rounded-full absolute top-0.5 transition-transform",
                                  isSelected ? "translate-x-5" : "translate-x-0.5"
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }
);

ModuleSelector.displayName = "ModuleSelector";

export { ModuleSelector };
