"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface StreamingChatBubbleProps {
  /** Message content to stream */
  message: string;
  /** Whether this is a user message (right side) or AI message (left side) */
  isUser?: boolean;
  /** Whether to show streaming animation */
  isStreaming?: boolean;
  /** Streaming speed in milliseconds per character */
  streamingSpeed?: number;
  /** Callback when streaming is complete */
  onStreamingComplete?: () => void;
  /** Timestamp for the message */
  timestamp?: Date;
  /** Whether to show timestamp */
  showTimestamp?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const StreamingChatBubble = React.forwardRef<HTMLDivElement, StreamingChatBubbleProps>(
  ({
    className,
    message,
    isUser = false,
    isStreaming = false,
    streamingSpeed = 30,
    onStreamingComplete,
    timestamp,
    showTimestamp = false
  }, ref) => {
    const [displayedMessage, setDisplayedMessage] = React.useState('');
    const [isComplete, setIsComplete] = React.useState(false);
    const streamingRef = React.useRef<NodeJS.Timeout | null>(null);

    // Streaming effect
    React.useEffect(() => {
      if (!isStreaming || isUser) {
        setDisplayedMessage(message);
        setIsComplete(true);
        return;
      }

      setDisplayedMessage('');
      setIsComplete(false);
      let currentIndex = 0;

      const streamText = () => {
        if (currentIndex < message.length) {
          setDisplayedMessage(prev => prev + message[currentIndex]);
          currentIndex++;
          streamingRef.current = setTimeout(streamText, streamingSpeed);
        } else {
          setIsComplete(true);
          onStreamingComplete?.();
        }
      };

      // Start streaming after a small delay
      streamingRef.current = setTimeout(streamText, 100);

      return () => {
        if (streamingRef.current) {
          clearTimeout(streamingRef.current);
        }
      };
    }, [message, isStreaming, isUser, streamingSpeed, onStreamingComplete]);

    // Clean up on unmount
    React.useEffect(() => {
      return () => {
        if (streamingRef.current) {
          clearTimeout(streamingRef.current);
        }
      };
    }, []);

    return (
      <div
        ref={ref}
        className={cn(
          "flex w-full mb-[var(--spacing-12)]",
          isUser ? "justify-end" : "justify-start",
          className
        )}
      >
        <div className={cn(
          "max-w-[80%] px-[var(--spacing-16)] py-[var(--spacing-12)] rounded-[8px]",
          "text-sm leading-relaxed",
          isUser
            ? "bg-[var(--color-frame-primary)] text-white rounded-br-[2px]"
            : "bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] rounded-bl-[2px]",
          "shadow-sm"
        )}>
          <p className="whitespace-pre-wrap">
            {displayedMessage}
            {/* Blinking cursor while streaming */}
            {isStreaming && !isUser && !isComplete && (
              <span className="inline-block w-[2px] h-[1em] bg-current ml-[2px] animate-pulse" />
            )}
          </p>
          {showTimestamp && timestamp && (
            <div className={cn(
              "text-xs mt-[var(--spacing-4)] opacity-70",
              isUser ? "text-white" : "text-[var(--color-text-secondary)]"
            )}>
              {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
          )}
        </div>
      </div>
    );
  }
);

StreamingChatBubble.displayName = "StreamingChatBubble";

export { StreamingChatBubble };
