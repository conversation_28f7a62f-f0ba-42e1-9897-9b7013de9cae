import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Header } from './header';

const meta: Meta<typeof Header> = {
  title: 'Components/Header',
  component: Header,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    logoText: {
      control: 'text',
      description: 'Text to display as the logo',
    },
    addButtonText: {
      control: 'text',
      description: 'Text to display in the add button',
    },
    profileImageSrc: {
      control: 'text',
      description: 'Source URL for the profile image',
    },
    addIconSrc: {
      control: 'text',
      description: 'Source URL for the add icon',
    },
    onAddClick: {
      action: 'add-clicked',
      description: 'Callback when add button is clicked',
    },
    onProfileClick: {
      action: 'profile-clicked',
      description: 'Callback when profile is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    logoText: 'LOGO',
    addButtonText: 'Añadir',
    profileImageSrc: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
  },
};

export const CustomLogo: Story = {
  args: {
    ...Default.args,
    logoText: 'Admin Dashboard',
  },
};

export const DifferentLanguage: Story = {
  args: {
    ...Default.args,
    addButtonText: 'Add',
  },
};

export const WithCustomStyling: Story = {
  args: {
    ...Default.args,
    className: 'bg-[var(--color-background-secondary-background)] border-b border-[var(--color-stroke)]',
  },
};

export const Interactive: Story = {
  args: {
    ...Default.args,
  },
  play: async ({ canvasElement }) => {
    // This story demonstrates the interactive nature of the component
    // In a real app, you would handle the click events
  },
};
