"use client";

import * as React from "react";
import { Icon, IconProps } from "./icon";

// Common icon components with predefined names
export const ExpandIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="fullscreen" {...props} />
);
ExpandIcon.displayName = "ExpandIcon";

export const CollapseIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="fullscreen_exit" {...props} />
);
CollapseIcon.displayName = "CollapseIcon";

export const ArrowForwardIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="arrow_forward" {...props} />
);
ArrowForwardIcon.displayName = "ArrowForwardIcon";

export const ArrowBackIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="arrow_back" {...props} />
);
ArrowBackIcon.displayName = "ArrowBackIcon";

export const ChevronRightIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="chevron_right" {...props} />
);
ChevronRightIcon.displayName = "ChevronRightIcon";

export const ChevronLeftIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="chevron_left" {...props} />
);
ChevronLeftIcon.displayName = "ChevronLeftIcon";

export const SearchIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="search" {...props} />
);
SearchIcon.displayName = "SearchIcon";

export const FilterIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="filter_list" {...props} />
);
FilterIcon.displayName = "FilterIcon";

export const DiscoverTuneIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="discover_tune" {...props} />
);
DiscoverTuneIcon.displayName = "DiscoverTuneIcon";

export const CloseIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="close" {...props} />
);
CloseIcon.displayName = "CloseIcon";

export const MenuIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="menu" {...props} />
);
MenuIcon.displayName = "MenuIcon";

export const MoreVertIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="more_vert" {...props} />
);
MoreVertIcon.displayName = "MoreVertIcon";

export const AddIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="add" {...props} />
);
AddIcon.displayName = "AddIcon";

export const EditIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="edit" {...props} />
);
EditIcon.displayName = "EditIcon";

export const DeleteIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="delete" {...props} />
);
DeleteIcon.displayName = "DeleteIcon";

export const CheckIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="check" {...props} />
);
CheckIcon.displayName = "CheckIcon";

export const WarningIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="warning" {...props} />
);
WarningIcon.displayName = "WarningIcon";

export const InfoIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="info" {...props} />
);
InfoIcon.displayName = "InfoIcon";

export const DownloadIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="download" {...props} />
);
DownloadIcon.displayName = "DownloadIcon";

export const UploadIcon = React.forwardRef<HTMLSpanElement, Omit<IconProps, 'name'>>(
  (props, ref) => <Icon ref={ref} name="upload" {...props} />
);
UploadIcon.displayName = "UploadIcon";
