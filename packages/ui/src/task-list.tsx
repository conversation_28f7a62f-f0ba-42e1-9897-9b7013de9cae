"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface TaskItem {
  id: string;
  name: string;
  description?: string;
  startDate: number; // Day of month (1-31)
  endDate: number; // Day of month (1-31)
  completed: boolean;
  assignedTo: string[]; // Team member IDs
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
}

export interface TaskListProps {
  tasks: TaskItem[];
  teamMembers: TeamMember[];
  onTaskToggle: (taskId: string) => void;
  onTaskEdit: (task: TaskItem) => void;
  onTaskDelete: (taskId: string) => void;
  onTaskCreate: () => void;
  isLoading?: boolean;
  className?: string;
}

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
};

export function TaskList({
  tasks,
  teamMembers,
  onTaskToggle,
  onTaskEdit,
  onTaskDelete,
  onTaskCreate,
  isLoading = false,
  className
}: TaskListProps) {
  const getTeamMembersByIds = (memberIds: string[]) => {
    return teamMembers.filter(member => memberIds.includes(member.id));
  };

  const formatDateRange = (startDate: number, endDate: number) => {
    if (startDate === endDate) {
      return `Day ${startDate}`;
    }
    return `Days ${startDate}-${endDate}`;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Tasks ({tasks.length})
        </h3>
        <button
          onClick={onTaskCreate}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          Add Task
        </button>
      </div>

      {/* Task List */}
      {tasks.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No tasks yet. Create your first task to get started.</p>
        </div>
      ) : (
        <div className="space-y-3">
          {tasks.map((task) => {
            const assignedMembers = getTeamMembersByIds(task.assignedTo);
            
            return (
              <div
                key={task.id}
                className={cn(
                  "p-4 border rounded-lg transition-colors",
                  task.completed 
                    ? "bg-gray-50 border-gray-200" 
                    : "bg-white border-gray-300 hover:border-gray-400"
                )}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    {/* Completion Checkbox */}
                    <button
                      onClick={() => onTaskToggle(task.id)}
                      className="mt-1 flex-shrink-0"
                    >
                      <div className={cn(
                        "w-5 h-5 rounded border-2 flex items-center justify-center",
                        task.completed
                          ? "bg-green-500 border-green-500 text-white"
                          : "border-gray-300 hover:border-gray-400"
                      )}>
                        {task.completed && (
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                    </button>

                    {/* Task Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className={cn(
                          "font-medium",
                          task.completed ? "text-gray-500 line-through" : "text-gray-900"
                        )}>
                          {task.name}
                        </h4>
                        {task.priority && (
                          <span className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            priorityColors[task.priority]
                          )}>
                            {task.priority}
                          </span>
                        )}
                      </div>

                      {task.description && (
                        <p className={cn(
                          "text-sm mb-2",
                          task.completed ? "text-gray-400" : "text-gray-600"
                        )}>
                          {task.description}
                        </p>
                      )}

                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{formatDateRange(task.startDate, task.endDate)}</span>
                        {task.estimatedHours && (
                          <span>{task.estimatedHours}h estimated</span>
                        )}
                        {task.actualHours && (
                          <span>{task.actualHours}h actual</span>
                        )}
                      </div>

                      {/* Assigned Team Members */}
                      {assignedMembers.length > 0 && (
                        <div className="flex items-center space-x-2 mt-2">
                          <span className="text-xs text-gray-500">Assigned to:</span>
                          <div className="flex items-center space-x-1">
                            {assignedMembers.slice(0, 3).map((member) => (
                              <div key={member.id} className="flex items-center space-x-1">
                                {member.avatar ? (
                                  <img
                                    src={member.avatar}
                                    alt={member.name}
                                    className="w-6 h-6 rounded-full"
                                  />
                                ) : (
                                  <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600">
                                    {member.name.charAt(0)}
                                  </div>
                                )}
                                <span className="text-xs text-gray-600">{member.name}</span>
                              </div>
                            ))}
                            {assignedMembers.length > 3 && (
                              <span className="text-xs text-gray-500">
                                +{assignedMembers.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Tags */}
                      {task.tags && task.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {task.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => onTaskEdit(task)}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      title="Edit task"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => onTaskDelete(task.id)}
                      className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100"
                      title="Delete task"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
