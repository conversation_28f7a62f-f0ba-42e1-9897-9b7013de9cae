import type { Meta, StoryObj } from '@storybook/react';
import { CatalogItem } from './catalog-item';

const meta: Meta<typeof CatalogItem> = {
  title: 'UI/CatalogItem',
  component: CatalogItem,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    productName: {
      control: 'text',
      description: 'The name of the product',
    },
    productDescription: {
      control: 'text',
      description: 'The description of the product',
    },
    categoryLabel: {
      control: 'text',
      description: 'The category label for the product',
    },
    imageSrc: {
      control: 'text',
      description: 'The image source URL for the product',
    },
    isActive: {
      control: 'boolean',
      description: 'Whether the item is in active/selected state',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    productName: 'Lámina Acero',
    productDescription: '4x8 | Calibre 12 | Inoxidable',
    categoryLabel: 'Producto',
  },
};

export const WithImage: Story = {
  args: {
    productName: 'Lámina Acero',
    productDescription: '4x8 | Calibre 12 | Inoxidable',
    categoryLabel: 'Producto',
    imageSrc: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=100&h=100&fit=crop',
  },
};

export const DifferentProduct: Story = {
  args: {
    productName: 'Tubo PVC',
    productDescription: '2" | 6m | Presión',
    categoryLabel: 'Material',
  },
};

export const LongDescription: Story = {
  args: {
    productName: 'Cable Eléctrico',
    productDescription: 'Cable eléctrico de cobre calibre 12 AWG para instalaciones residenciales',
    categoryLabel: 'Eléctrico',
  },
};

export const Active: Story = {
  args: {
    productName: 'Lámina Acero',
    productDescription: '4x8 | Calibre 12 | Inoxidable',
    categoryLabel: 'Producto',
    isActive: true,
  },
};

export const ActiveWithImage: Story = {
  args: {
    productName: 'Lámina Acero',
    productDescription: '4x8 | Calibre 12 | Inoxidable',
    categoryLabel: 'Producto',
    imageSrc: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=100&h=100&fit=crop',
    isActive: true,
  },
};
