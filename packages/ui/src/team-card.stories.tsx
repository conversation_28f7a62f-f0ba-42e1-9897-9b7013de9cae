import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { TeamCard } from './team-card';

const meta: Meta<typeof TeamCard> = {
  title: 'Components/TeamCard',
  component: TeamCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Employee name',
    },
    role: {
      control: 'text',
      description: 'Employee role/position',
    },
    avatar: {
      control: 'text',
      description: 'Employee avatar image URL',
    },
    isActive: {
      control: 'boolean',
      description: 'Whether the card is in active/selected state',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: '<PERSON>',
    role: '<PERSON><PERSON>ña<PERSON>',
    isActive: false,
  },
};

export const WithAvatar: Story = {
  args: {
    name: '<PERSON>',
    role: 'Ingeniera de Diseño',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    isActive: false,
  },
};

export const Active: Story = {
  args: {
    name: '<PERSON>',
    role: 'Supervisor de Producción',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    isActive: true,
  },
};

export const LongText: Story = {
  args: {
    name: 'María González Fernández',
    role: 'Coordinadora de Calidad y Procesos de Manufactura',
    isActive: false,
  },
};

export const Grid: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-3 w-[320px]">
      <TeamCard
        name="Mark Márquez"
        role="Diseñador"
        isActive={false}
      />
      <TeamCard
        name="Ana García"
        role="Ingeniera"
        avatar="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
        isActive={true}
      />
      <TeamCard
        name="Carlos Mendoza"
        role="Supervisor"
        avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
        isActive={false}
      />
      <TeamCard
        name="María González Fernández"
        role="Coordinadora de Calidad y Procesos"
        isActive={false}
      />
    </div>
  ),
};

export const ConsistentHeight: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-3 w-[480px]">
      <TeamCard
        name="Ana"
        role="Dev"
        isActive={false}
      />
      <TeamCard
        name="Carlos Mendoza Silva"
        role="Supervisor de Producción y Calidad"
        avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
        isActive={false}
      />
      <TeamCard
        name="María González Fernández de la Torre"
        role="Coordinadora Principal de Calidad y Procesos de Manufactura Avanzada"
        isActive={false}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All cards maintain consistent 110px height regardless of content length. Long text is truncated with ellipsis.',
      },
    },
  },
};
