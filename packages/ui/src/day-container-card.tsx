"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface DayContainerCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** State of the day container */
  state?: 'default' | 'past';
  /** Whether to show minimal padding for compact layout */
  compact?: boolean;
}

const DayContainerCard = React.forwardRef<HTMLDivElement, DayContainerCardProps>(
  ({
    className,
    state = 'default',
    compact = false,
    children,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const getBackgroundColor = () => {
      switch (state) {
        case 'past':
          return 'var(--color-background-secondary)';
        default:
          return 'var(--color-background-primary)';
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "border border-[var(--color-stroke)] rounded-[var(--radius-8)] flex flex-col min-h-0 relative",
          compact ? "p-2 gap-1" : "p-3 gap-2",
          className
        )}
        style={{
          backgroundColor: getBackgroundColor(),
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

DayContainerCard.displayName = "DayContainerCard";

export { DayContainerCard };
