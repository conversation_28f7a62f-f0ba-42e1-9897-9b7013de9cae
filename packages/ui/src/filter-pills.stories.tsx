import type { <PERSON>a, StoryObj } from '@storybook/react';
import { FilterPills, type FilterOption } from './filter-pills';
import { useState } from 'react';

const meta: Meta<typeof FilterPills> = {
  title: 'UI/FilterPills',
  component: FilterPills,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    selectedOptions: {
      control: 'object',
      description: 'Currently selected filter options',
    },
    onRemoveOption: {
      action: 'option-removed',
      description: 'Callback when an option is removed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleOptions: FilterOption[] = [
  { id: 'active', label: 'Activos', value: 'active' },
  { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
  { id: 'designer', label: 'Diseñador<PERSON>', value: 'designer' },
];

export const Default: Story = {
  args: {
    selectedOptions: sampleOptions,
  },
};

export const SingleOption: Story = {
  args: {
    selectedOptions: [
      { id: 'active', label: 'Activos', value: 'active' },
    ],
  },
};

export const LongLabels: Story = {
  args: {
    selectedOptions: [
      { id: 'coordinator', label: 'Coordinadores de Calidad', value: 'coordinator' },
      { id: 'supervisor', label: 'Supervisores de Producción', value: 'supervisor' },
      { id: 'analyst', label: 'Analistas de Procesos Avanzados', value: 'analyst' },
    ],
  },
};

export const Empty: Story = {
  args: {
    selectedOptions: [],
  },
};

export const Interactive: Story = {
  render: () => {
    const [selectedOptions, setSelectedOptions] = useState<FilterOption[]>([
      { id: 'active', label: 'Activos', value: 'active' },
      { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
      { id: 'designer', label: 'Diseñadores', value: 'designer' },
    ]);

    const handleRemoveOption = (optionId: string) => {
      setSelectedOptions(prev => prev.filter(option => option.id !== optionId));
    };

    return (
      <div className="w-[400px] p-4">
        <h3 className="text-lg font-semibold mb-4">Interactive Filter Pills</h3>
        <p className="text-sm text-gray-600 mb-4">
          Click the X button to remove filter options
        </p>
        <FilterPills
          selectedOptions={selectedOptions}
          onRemoveOption={handleRemoveOption}
        />
        {selectedOptions.length === 0 && (
          <p className="text-sm text-gray-500 mt-4">No filters selected</p>
        )}
      </div>
    );
  },
};

export const WithSearchBar: Story = {
  render: () => {
    const [selectedOptions, setSelectedOptions] = useState<FilterOption[]>([
      { id: 'active', label: 'Activos', value: 'active' },
      { id: 'engineer', label: 'Ingenieros', value: 'engineer' },
    ]);

    const handleRemoveOption = (optionId: string) => {
      setSelectedOptions(prev => prev.filter(option => option.id !== optionId));
    };

    return (
      <div className="w-[400px] p-4">
        <h3 className="text-lg font-semibold mb-4">With Search Bar</h3>
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <input
              type="text"
              placeholder="Buscar miembro del equipo..."
              className="flex-1 px-3 py-2 border rounded-lg"
            />
            <button className="px-3 py-2 bg-gray-200 rounded-lg">
              Filter
            </button>
          </div>
          <FilterPills
            selectedOptions={selectedOptions}
            onRemoveOption={handleRemoveOption}
          />
        </div>
      </div>
    );
  },
};
