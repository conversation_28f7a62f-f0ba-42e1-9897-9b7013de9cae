"use client";

import * as React from "react";
import { cn } from "./lib/utils";
import "./calendar-timeline.css";

export interface CalendarTimelineTask {
  id: string;
  name: string;
  startDate: number; // Day of month (1-31)
  endDate: number; // Day of month (1-31)
  completed?: boolean; // Whether the task is completed
  employees: {
    id: string;
    name: string;
    avatar?: string;
  }[];
}

export interface CalendarTimelineProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Month name to display */
  month?: string;
  /** Next month name to display */
  nextMonth?: string;
  /** Number of days in the month */
  daysInMonth?: number;
  /** Array of tasks to display */
  tasks?: CalendarTimelineTask[];
  /** Callback when a task is clicked */
  onTaskClick?: (task: CalendarTimelineTask) => void;
}

const CalendarTimeline = React.forwardRef<HTMLDivElement, CalendarTimelineProps>(
  ({
    className,
    month = "Junio",
    nextMonth = "Julio",
    daysInMonth = 30,
    tasks = [],
    onTaskClick,
    ...props
  }, ref) => {
    const [hoveredTaskId, setHoveredTaskId] = React.useState<string | null>(null);

    // Generate array of days for the month
    const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

    // Helper function to check if a day is within a task's date range
    const isDayInTask = (day: number, task: CalendarTimelineTask): boolean => {
      return day >= task.startDate && day <= task.endDate;
    };

    // Helper function to get highlighted days for hovered task
    const getHighlightedDays = (): number[] => {
      if (!hoveredTaskId) return [];
      const hoveredTask = tasks.find(task => task.id === hoveredTaskId);
      if (!hoveredTask) return [];

      const highlightedDays: number[] = [];
      for (let day = hoveredTask.startDate; day <= hoveredTask.endDate; day++) {
        if (day <= daysInMonth) {
          highlightedDays.push(day);
        }
      }
      return highlightedDays;
    };

    const highlightedDays = getHighlightedDays();

    return (
      <div
        ref={ref}
        className={cn("calendar-timeline w-full", className)}
        {...props}
      >
        {/* Month header */}
        <div className="mb-2">
          <h3 className="text-sm font-medium text-[var(--color-text-primary)]">
            {month} - {tasks.length} tasks
          </h3>
        </div>

        {/* Days header */}
        <div
          className="day-header grid gap-1 mb-2 pb-2 relative border-b border-[var(--color-stroke)]"
          style={{
            gridTemplateColumns: `repeat(${daysInMonth}, minmax(24px, 1fr))`
          }}
        >
          {/* Highlighted date ranges for each task (background layer) */}
          {tasks.map((task) => (
            <div
              key={`highlight-${task.id}`}
              className="absolute inset-0 grid gap-1 pointer-events-none z-0"
              style={{
                gridTemplateColumns: `repeat(${daysInMonth}, minmax(24px, 1fr))`
              }}
            >
              {/* Empty cells before task start */}
              {Array.from({ length: task.startDate - 1 }, (_, i) => (
                <div key={`empty-before-${i}`} className="h-full" />
              ))}

              {/* Single highlighted container spanning task duration */}
              <div
                className={cn(
                  "bg-[var(--color-background-secondary)] rounded-[var(--radius-4)] transition-all duration-200 opacity-0 h-8",
                  hoveredTaskId === task.id && "opacity-100"
                )}
                style={{
                  gridColumn: `span ${task.endDate - task.startDate + 1}`
                }}
              />

              {/* Empty cells after task end */}
              {Array.from({ length: daysInMonth - task.endDate }, (_, i) => (
                <div key={`empty-after-${i}`} className="h-full" />
              ))}
            </div>
          ))}

          {/* Day numbers (foreground layer) */}
          {days.map((day) => (
            <div
              key={day}
              className="flex items-center justify-center text-xs text-[var(--color-text-secondary)] h-8 transition-all duration-200 relative z-10"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Tasks - Each task in its own grid row */}
        <div className="space-y-1">
          {tasks.map((task) => (
            <div
              key={task.id}
              className="task-row grid gap-1 h-9 relative mb-2"
              style={{
                gridTemplateColumns: `repeat(${daysInMonth}, minmax(24px, 1fr))`
              }}
              onMouseEnter={() => setHoveredTaskId(task.id)}
              onMouseLeave={() => setHoveredTaskId(null)}
            >
              {/* Empty cells before task start */}
              {Array.from({ length: task.startDate - 1 }, (_, i) => (
                <div key={`empty-before-${i}`} className="h-full" />
              ))}

              {/* Single task container spanning multiple columns */}
              <div
                className={cn(
                  "task-container bg-[var(--color-background-secondary)] rounded-[4px] shadow-md transition-all duration-200 cursor-pointer flex items-center px-3 py-2 relative",
                  task.completed && "opacity-75"
                )}
                style={{
                  gridColumn: `span ${task.endDate - task.startDate + 1}`
                }}
                onClick={() => onTaskClick?.(task)}
                onMouseEnter={() => setHoveredTaskId(task.id)}
                onMouseLeave={() => setHoveredTaskId(null)}
              >
                {/* Completion line overlay */}
                {task.completed && (
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="w-full h-0.5 bg-[var(--color-text-secondary)] opacity-60"></div>
                  </div>
                )}

                {/* Task content */}
                <div className="flex items-center gap-2 min-w-0 flex-1 overflow-hidden">
                  <span className={cn(
                    "text-xs font-semibold font-inter whitespace-nowrap overflow-hidden text-ellipsis",
                    task.completed
                      ? "text-[var(--color-text-secondary)]"
                      : "text-[var(--color-text-primary)]"
                  )}>
                    {task.name}
                  </span>

                  {/* Employee avatar - only show first employee */}
                  {task.employees.length > 0 && task.employees[0] && (
                    <div className="avatar-container w-6 h-6 rounded-full overflow-hidden bg-[var(--color-background-secondary)] flex items-center justify-center flex-shrink-0">
                      {task.employees[0].avatar ? (
                        <img
                          src={task.employees[0].avatar}
                          alt={task.employees[0].name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-[var(--color-text-secondary)] flex items-center justify-center text-white text-[10px] font-medium">
                          {task.employees[0].name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Empty cells after task end */}
              {Array.from({ length: daysInMonth - task.endDate }, (_, i) => (
                <div key={`empty-after-${i}`} className="h-full" />
              ))}
            </div>
          ))}
        </div>

        {/* Empty state */}
        {tasks.length === 0 && (
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            <p className="text-sm">No hay tareas programadas</p>
          </div>
        )}
      </div>
    );
  }
);

CalendarTimeline.displayName = "CalendarTimeline";

export { CalendarTimeline };
