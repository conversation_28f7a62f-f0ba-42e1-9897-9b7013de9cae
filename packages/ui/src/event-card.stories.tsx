import type { Meta, StoryObj } from '@storybook/react';
import { EventCard } from './event-card';

const meta: Meta<typeof EventCard> = {
  title: 'Components/EventCard',
  component: EventCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    text: {
      control: 'text',
      description: 'The event text to display',
    },
    state: {
      control: { type: 'select' },
      options: ['default', 'active'],
      description: 'State of the event card',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    text: 'Meeting',
    state: 'default',
  },
};

export const Active: Story = {
  args: {
    text: 'Important',
    state: 'active',
  },
};

export const AllStates: Story = {
  render: () => (
    <div className="flex gap-4 items-center">
      <div className="text-center">
        <EventCard text="Meeting" state="default" />
        <p className="text-xs mt-2">Default</p>
      </div>
      <div className="text-center">
        <EventCard text="Important" state="active" />
        <p className="text-xs mt-2">Active</p>
      </div>
    </div>
  ),
};

export const EventList: Story = {
  render: () => (
    <div className="flex flex-col gap-1 w-32">
      <EventCard text="Team Meeting" state="default" />
      <EventCard text="Deadline" state="active" />
      <EventCard text="Review" state="default" />
      <EventCard text="Urgent" state="active" />
    </div>
  ),
};

export const LongText: Story = {
  render: () => (
    <div className="w-40">
      <EventCard text="Very Long Event Name That Might Wrap" state="default" />
    </div>
  ),
};
