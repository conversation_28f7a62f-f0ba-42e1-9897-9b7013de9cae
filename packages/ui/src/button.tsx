"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "./lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-button focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-[var(--color-frame-primary)] text-[var(--color-text-inverse)] hover:opacity-90 rounded-[var(--radius-8)]",
        destructive: "bg-[var(--color-red)] text-[var(--color-text-inverse)] hover:opacity-90 rounded-[var(--radius-8)]",
        outline: "border border-[var(--color-stroke)] bg-[var(--color-background-primary)] hover:bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] rounded-[var(--radius-8)]",
        secondary: "bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] hover:opacity-80 rounded-[var(--radius-8)]",
        ghost: "hover:bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] rounded-[var(--radius-8)]",
        link: "text-[var(--color-text-primary)] underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-[var(--spacing-16)] py-[var(--spacing-8)]",
        sm: "h-9 px-[var(--spacing-12)] py-[var(--spacing-6)]",
        lg: "h-11 px-[var(--spacing-24)] py-[var(--spacing-12)]",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
