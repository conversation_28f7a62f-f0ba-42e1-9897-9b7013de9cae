import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { Calendar } from './calendar';

const meta: Meta<typeof Calendar> = {
  title: 'Components/Calendar',
  component: Calendar,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Calendar title',
    },
    subtitle: {
      control: 'text',
      description: 'Calendar subtitle (month/year)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: 'Calendario',
    subtitle: 'Junio 2025',
  },
  render: (args) => (
    <div className="h-screen p-8">
      <Calendar {...args} />
    </div>
  ),
};

export const CustomEvents: Story = {
  args: {
    title: 'Calendario',
    subtitle: 'Julio 2025',
    days: [
      { number: 1, state: 'default', containerState: 'default' },
      { number: 2, state: 'default', containerState: 'past', events: [{ text: 'Completado', state: 'default' }] },
      { number: 3, state: 'active', containerState: 'default', events: [{ text: 'Urgente', state: 'active' }] },
      { number: 4, state: 'default', containerState: 'default', events: [{ text: 'Reunión', state: 'default' }, { text: 'Llamada', state: 'active' }] },
      { number: 5, state: 'inverse', containerState: 'default' },
      ...Array.from({ length: 25 }, (_, i) => ({
        number: i + 6,
        state: 'default' as const,
        containerState: 'default' as const,
      }))
    ]
  },
  render: (args) => (
    <div className="h-screen p-8">
      <Calendar {...args} />
    </div>
  ),
};

export const CompactView: Story = {
  args: {
    title: 'Calendario',
    subtitle: 'Agosto 2025',
  },
  render: (args) => (
    <div className="h-96 p-4 border rounded-lg">
      <Calendar {...args} />
    </div>
  ),
};

export const SpanishWeekHeaders: Story = {
  args: {
    title: 'Calendario Financiero',
    subtitle: 'Septiembre 2025',
    weekHeaders: ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'],
  },
  render: (args) => (
    <div className="h-screen p-8">
      <Calendar {...args} />
    </div>
  ),
};
