"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface FinanceCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Finance section title */
  title?: string;
  /** Whether the card is in active/selected state */
  isActive?: boolean;
}

const FinanceCard = React.forwardRef<HTMLDivElement, FinanceCardProps>(
  ({
    className,
    title = "Activos",
    isActive = false,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const financeCardBorderColor = 'var(--color-stroke)';
    const financeCardBackgroundColor = isActive ? 'var(--color-background-secondary)' : 'var(--color-background-primary)';
    const financeCardTextColor = isActive ? 'var(--color-text-primary)' : 'var(--color-text-primary)';

    return (
      <div
        ref={ref}
        className={cn(
          "w-full h-full flex items-center justify-center rounded-[var(--radius-8)] border cursor-pointer hover-bg",
          className
        )}
        style={{
          backgroundColor: financeCardBackgroundColor,
          borderColor: financeCardBorderColor,
        }}
        {...props}
      >
        <div 
          className="font-bold text-base font-inter"
          style={{ color: financeCardTextColor }}
        >
          {title}
        </div>
      </div>
    );
  }
);

FinanceCard.displayName = "FinanceCard";

export { FinanceCard };
