import type { <PERSON>a, StoryObj } from '@storybook/react';
import { SearchBarSet } from './search-bar-set';

const meta: Meta<typeof SearchBarSet> = {
  title: 'UI/SearchBarSet',
  component: SearchBarSet,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'Placeholder text for the search input',
    },
    exampleText: {
      control: 'text',
      description: 'Example search text to show in the second bar',
    },
    showExample: {
      control: 'boolean',
      description: 'Whether to show the example search bar',
    },
    onSearchChange: {
      action: 'search-changed',
      description: 'Callback when search input changes',
    },
    onExampleClick: {
      action: 'example-clicked',
      description: 'Callback when example is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Buscar',
    exampleText: 'Lamina de acero al carbon',
    showExample: true,
  },
};

export const WithoutExample: Story = {
  args: {
    placeholder: 'Buscar productos...',
    showExample: false,
  },
};

export const CustomPlaceholder: Story = {
  args: {
    placeholder: 'Search for materials...',
    exampleText: 'Steel carbon sheet',
    showExample: true,
  },
};

export const CustomExample: Story = {
  args: {
    placeholder: 'Buscar',
    exampleText: 'Tornillos de acero inoxidable M8',
    showExample: true,
  },
};

export const Interactive: Story = {
  args: {
    placeholder: 'Type to search...',
    exampleText: 'Click me to fill the search',
    showExample: true,
  },
  render: (args) => {
    return (
      <div className="w-[500px]">
        <SearchBarSet 
          {...args}
          onSearchChange={(value) => console.log('Search changed:', value)}
          onExampleClick={(text) => console.log('Example clicked:', text)}
        />
      </div>
    );
  },
};
