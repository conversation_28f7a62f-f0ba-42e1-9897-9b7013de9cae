"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface AnimatedModalProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Whether the modal should be visible */
  show?: boolean;
  /** Duration of the modal transition */
  duration?: 'fast' | 'normal' | 'slow';
  /** Delay before the transition starts */
  delay?: 'none' | 'short' | 'medium' | 'long';
  /** Custom transition timing function */
  easing?: 'ease' | 'easeIn' | 'easeOut' | 'easeInOut' | 'smooth';
  /** Whether to include backdrop fade */
  showBackdrop?: boolean;
  /** Backdrop click handler */
  onBackdropClick?: () => void;
  /** Scale animation type */
  scaleType?: 'scale' | 'scaleY' | 'scaleX' | 'none';
  /** Initial scale value when hidden */
  initialScale?: number;
  /** Callback fired when transition completes */
  onTransitionEnd?: () => void;
}

const AnimatedModal = React.forwardRef<HTMLDivElement, AnimatedModalProps>(
  ({
    className,
    show = false,
    duration = 'normal',
    delay = 'none',
    easing = 'smooth',
    showBackdrop = true,
    onBackdropClick,
    scaleType = 'scale',
    initialScale = 0.95,
    onTransitionEnd,
    children,
    style,
    ...props
  }, ref) => {
    const [shouldRender, setShouldRender] = React.useState(show);
    const [isVisible, setIsVisible] = React.useState(false);

    React.useEffect(() => {
      if (show) {
        setShouldRender(true);
        // Small delay to ensure the element is rendered before making it visible
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
      } else {
        setIsVisible(false);
        // Wait for transition to complete before unmounting
        const durationMs = duration === 'fast' ? 150 : duration === 'slow' ? 350 : 250;
        const timer = setTimeout(() => setShouldRender(false), durationMs);
        return () => clearTimeout(timer);
      }
    }, [show, duration]);

    const handleTransitionEnd = React.useCallback((e: React.TransitionEvent) => {
      if (e.propertyName === 'opacity' && e.target === e.currentTarget) {
        onTransitionEnd?.();
      }
    }, [onTransitionEnd]);

    const handleBackdropClick = React.useCallback((e: React.MouseEvent) => {
      if (e.target === e.currentTarget) {
        onBackdropClick?.();
      }
    }, [onBackdropClick]);

    if (!shouldRender) {
      return null;
    }

    const durationValue = `var(--transition-duration-${duration})`;
    const delayValue = `var(--transition-delay-${delay})`;
    const easingValue = `var(--transition-easing-${easing})`;

    const getTransform = () => {
      if (scaleType === 'none') return undefined;
      if (!isVisible) {
        switch (scaleType) {
          case 'scaleX':
            return `scaleX(${initialScale})`;
          case 'scaleY':
            return `scaleY(${initialScale})`;
          default:
            return `scale(${initialScale})`;
        }
      }
      return 'scale(1)';
    };

    return (
      <div
        className={cn(
          "fixed inset-0 z-50 flex items-center justify-center",
          showBackdrop && "bg-black/20"
        )}
        style={{
          opacity: isVisible ? 1 : 0,
          transitionProperty: 'opacity',
          transitionDuration: durationValue,
          transitionDelay: delayValue,
          transitionTimingFunction: easingValue,
        }}
        onTransitionEnd={handleTransitionEnd}
        onClick={handleBackdropClick}
      >
        <div
          ref={ref}
          className={cn(
            "relative",
            scaleType !== 'none' && "transition-transform",
            className
          )}
          style={{
            transform: getTransform(),
            transitionDuration: scaleType !== 'none' ? durationValue : undefined,
            transitionDelay: scaleType !== 'none' ? delayValue : undefined,
            transitionTimingFunction: scaleType !== 'none' ? easingValue : undefined,
            ...style,
          }}
          {...props}
        >
          {children}
        </div>
      </div>
    );
  }
);

AnimatedModal.displayName = "AnimatedModal";

export { AnimatedModal };
