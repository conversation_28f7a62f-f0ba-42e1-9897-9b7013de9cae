import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ProjectCard } from './project-card';

const meta: Meta<typeof ProjectCard> = {
  title: 'Components/ProjectCard',
  component: ProjectCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    progressPercentage: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
    },
    currentProgress: {
      control: { type: 'number', min: 0 },
    },
    totalTasks: {
      control: { type: 'number', min: 1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    projectName: '<PERSON>uer<PERSON>',
    initials: 'TH',
    startDate: '08 Ago, 2024',
    endDate: '31 Ago, 2024',
    currentProgress: 4,
    totalTasks: 12,
    status: 'En proceso',
    progressPercentage: 33,
  },
};

export const EarlyStage: Story = {
  args: {
    projectName: 'Nuevo Centro Comercial',
    initials: 'NC',
    startDate: '15 Sep, 2024',
    endDate: '20 Dic, 2024',
    currentProgress: 1,
    totalTasks: 8,
    status: 'Iniciado',
    progressPercentage: 12,
  },
};

export const MidProgress: Story = {
  args: {
    projectName: 'Renovación Oficinas Corporativas',
    initials: 'RO',
    startDate: '01 Jul, 2024',
    endDate: '15 Oct, 2024',
    currentProgress: 6,
    totalTasks: 10,
    status: 'En proceso',
    progressPercentage: 60,
  },
};

export const NearCompletion: Story = {
  args: {
    projectName: 'Ampliación Planta Industrial',
    initials: 'AP',
    startDate: '10 Jun, 2024',
    endDate: '25 Ago, 2024',
    currentProgress: 9,
    totalTasks: 10,
    status: 'Finalizando',
    progressPercentage: 90,
  },
};

export const Completed: Story = {
  args: {
    projectName: 'Construcción Almacén Norte',
    initials: 'CA',
    startDate: '01 May, 2024',
    endDate: '30 Jul, 2024',
    currentProgress: 12,
    totalTasks: 12,
    status: 'Completado',
    progressPercentage: 100,
  },
};

export const LongProjectName: Story = {
  args: {
    projectName: 'Proyecto de Construcción y Desarrollo Integral de Complejo Residencial y Comercial',
    initials: 'PC',
    startDate: '01 Ene, 2024',
    endDate: '31 Dic, 2024',
    currentProgress: 15,
    totalTasks: 25,
    status: 'En proceso',
    progressPercentage: 60,
  },
};

export const Interactive: Story = {
  render: () => {
    const handleClick = (projectName: string) => {
      alert(`Clicked on project: ${projectName}`);
    };

    return (
      <div className="w-[400px] space-y-4">
        <h3 className="text-lg font-semibold">Active Projects</h3>
        <ProjectCard
          projectName="Tommy Hilfiger Cuernavaca"
          initials="TH"
          startDate="08 Ago, 2024"
          endDate="31 Ago, 2024"
          currentProgress={4}
          totalTasks={12}
          status="En proceso"
          progressPercentage={33}
          onCardClick={() => handleClick('Tommy Hilfiger Cuernavaca')}
        />
        <ProjectCard
          projectName="Centro Comercial Plaza Norte"
          initials="CP"
          startDate="15 Sep, 2024"
          endDate="20 Dic, 2024"
          currentProgress={2}
          totalTasks={8}
          status="Iniciado"
          progressPercentage={25}
          onCardClick={() => handleClick('Centro Comercial Plaza Norte')}
        />
        <ProjectCard
          projectName="Renovación Oficinas"
          initials="RO"
          startDate="01 Jul, 2024"
          endDate="15 Oct, 2024"
          currentProgress={7}
          totalTasks={10}
          status="Avanzado"
          progressPercentage={70}
          onCardClick={() => handleClick('Renovación Oficinas')}
        />
      </div>
    );
  },
};

export const Grid: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 w-[800px]">
      <ProjectCard
        projectName="Tommy Hilfiger Cuernavaca"
        initials="TH"
        startDate="08 Ago, 2024"
        endDate="31 Ago, 2024"
        currentProgress={4}
        totalTasks={12}
        status="En proceso"
        progressPercentage={33}
      />
      <ProjectCard
        projectName="Centro Comercial"
        initials="CC"
        startDate="15 Sep, 2024"
        endDate="20 Dic, 2024"
        currentProgress={1}
        totalTasks={8}
        status="Iniciado"
        progressPercentage={12}
      />
      <ProjectCard
        projectName="Renovación Oficinas"
        initials="RO"
        startDate="01 Jul, 2024"
        endDate="15 Oct, 2024"
        currentProgress={6}
        totalTasks={10}
        status="En proceso"
        progressPercentage={60}
      />
      <ProjectCard
        projectName="Planta Industrial"
        initials="PI"
        startDate="10 Jun, 2024"
        endDate="25 Ago, 2024"
        currentProgress={9}
        totalTasks={10}
        status="Finalizando"
        progressPercentage={90}
      />
    </div>
  ),
};
