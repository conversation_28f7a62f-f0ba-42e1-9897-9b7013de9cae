"use client";

import * as React from "react";
import { ArrowUp, Loader2 } from "lucide-react";
import { cn } from "./lib/utils";

export interface MessageBarProps {
  /** Placeholder text for the input */
  placeholder?: string;
  /** Arrow icon source for the circular button */
  arrowIconSrc?: string;
  /** Callback when command is submitted */
  onSubmit?: (command: string) => void;
  /** Whether the input is in loading state */
  loading?: boolean;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const MessageBar = React.forwardRef<HTMLDivElement, MessageBarProps>(
  ({
    className,
    placeholder = "Ask me to create something...",
    arrowIconSrc,
    onSubmit,
    loading = false,
    disabled = false
  }, ref) => {
    const [command, setCommand] = React.useState('');

    // Component-specific variables from global design tokens
    const messageBarArrowButtonBg = 'var(--color-frame-primary)';

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (command.trim() && !loading && !disabled && onSubmit) {
        onSubmit(command.trim());
        setCommand(''); // Clear input after submission
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSubmit(e);
      }
    };

    return (
      <form onSubmit={handleSubmit}>
        <div
          ref={ref}
          className={cn(
            "w-full flex items-center justify-between bg-[var(--color-background-primary)] text-[var(--color-text-primary)] px-[var(--spacing-16)] py-[var(--spacing-12)] rounded-[var(--radius-8)]",
            className
          )}
          style={{ boxShadow: 'var(--shadow-outer1)' }}
        >
          {/* Left side - Input */}
          <div className="flex-1 mr-[var(--spacing-12)]">
            <input
              type="text"
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled || loading}
              className={cn(
                "w-full bg-transparent text-sm font-medium",
                "text-[var(--color-text-primary)] placeholder:text-[var(--color-text-placeholder)]",
                "focus:outline-none",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
            />
          </div>

          {/* Right side - Circular arrow button */}
          <div className="flex items-center">
            <button
              type="submit"
              disabled={!command.trim() || loading || disabled}
              className="w-20 h-20 rounded-full text-white flex items-center justify-center hover-scale disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ backgroundColor: messageBarArrowButtonBg }}
            >
              {loading ? (
                <Loader2 size={16} className="animate-spin" />
              ) : arrowIconSrc ? (
                <img
                  className="w-[20px] h-[20px]"
                  width={20}
                  height={20}
                  alt="Arrow icon"
                  src={arrowIconSrc}
                />
              ) : (
                <ArrowUp size={16} strokeWidth={2.5} />
              )}
            </button>
          </div>
        </div>
      </form>
    );
  }
);

MessageBar.displayName = "MessageBar";

export { MessageBar };
