"use client";

import * as React from "react";
import { Search } from "lucide-react";
import { cn } from "./lib/utils";

export interface SearchBarSetProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Placeholder text for the search input */
  placeholder?: string;
  /** Example search text to show in the second bar */
  exampleText?: string;
  /** Whether to show the example search bar */
  showExample?: boolean;
  /** Custom search icon */
  searchIcon?: React.ReactNode;
  /** Callback when search input changes */
  onSearchChange?: (value: string) => void;
  /** Callback when example is clicked */
  onExampleClick?: (text: string) => void;
}

const SearchBarSet = React.forwardRef<HTMLDivElement, SearchBarSetProps>(
  ({
    className,
    placeholder = "Buscar",
    exampleText = "Lamina de acero al carbon",
    showExample = true,
    searchIcon,
    onSearchChange,
    onExampleClick,
    ...props
  }, ref) => {
    const [searchValue, setSearchValue] = React.useState("");

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);
      onSearchChange?.(value);
    };

    const handleExampleClick = () => {
      setSearchValue(exampleText);
      onSearchChange?.(exampleText);
      onExampleClick?.(exampleText);
    };

    const defaultSearchIcon = searchIcon || (
      <Search className="w-[18px] h-[18px] text-[var(--color-text-placeholder)]" />
    );

    return (
      <div
        ref={ref}
        className={cn(
          "w-full relative",
          showExample ? "rounded-[var(--radius-8)] border-dashed border-[1px] border-[#9747ff] box-border h-[111px] overflow-hidden" : "",
          className
        )}
        {...props}
      >
        {/* Main Search Bar */}
        <div className={cn(
          "rounded-[var(--radius-8)] bg-[var(--color-background-secondary)] flex flex-row items-center justify-start py-[var(--spacing-6)] px-[var(--spacing-12)] box-border gap-[var(--spacing-6)]",
          showExample ? "absolute top-[var(--spacing-16)] left-[var(--spacing-20)] w-[433px]" : "w-full"
        )}>
          <div className="w-[18px] relative h-[18px] flex items-center justify-center">
            {defaultSearchIcon}
          </div>
          <input
            type="text"
            value={searchValue}
            onChange={handleSearchChange}
            placeholder={placeholder}
            className="flex-1 bg-transparent border-none outline-none text-sm text-[var(--color-text-primary)] placeholder:text-[var(--color-text-placeholder)] font-medium"
          />
        </div>

        {/* Example Search Bar */}
        {showExample && (
          <div
            className="absolute top-[67px] left-[var(--spacing-20)] rounded-[var(--radius-8)] bg-[var(--color-background-secondary)] w-[433px] flex flex-row items-center justify-start py-[var(--spacing-6)] px-[var(--spacing-12)] box-border gap-[var(--spacing-6)] cursor-pointer hover:opacity-80 transition-opacity"
            onClick={handleExampleClick}
          >
            <div className="w-[18px] relative h-[18px] flex items-center justify-center">
              {defaultSearchIcon}
            </div>
            <div className="relative font-medium text-sm text-[var(--color-text-secondary)]">
              {exampleText}
            </div>
          </div>
        )}
      </div>
    );
  }
);

SearchBarSet.displayName = "SearchBarSet";

export { SearchBarSet };
