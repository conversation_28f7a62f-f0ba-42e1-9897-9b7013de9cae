"use client";

import * as React from "react";
import { cn } from "./lib/utils";

// Form field types
export interface FormField {
  id: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'select' | 'date' | 'currency';
  required: boolean;
  options?: string[];
  placeholder?: string;
  value: any;
  error?: string;
}

export interface DataFormProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Form fields to render */
  fields: FormField[];
  /** Entity type being created */
  entityType: string;
  /** Callback when field value changes */
  onFieldChange: (fieldId: string, value: any) => void;
  /** Callback when form is submitted */
  onSubmit?: () => void;
  /** Submit button text */
  submitText?: string;
  /** Whether form is in loading state */
  loading?: boolean;
  /** Whether submit button should be disabled */
  submitDisabled?: boolean;
}

const DataForm = React.forwardRef<HTMLDivElement, DataFormProps>(
  ({
    className,
    fields,
    entityType,
    onFieldChange,
    onSubmit,
    submitText = "Create",
    loading = false,
    submitDisabled = false,
    ...props
  }, ref) => {
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit?.();
    };

    const renderField = (field: FormField) => {
      const baseInputClasses = cn(
        "w-full px-[var(--spacing-12)] py-[var(--spacing-8)] rounded-[var(--radius-8)]",
        "border border-[var(--color-stroke)] bg-[var(--color-background-primary)]",
        "font-sans font-medium text-sm text-[var(--color-text-primary)] leading-none",
        "focus:outline-none focus:ring-2 focus:ring-[var(--color-frame-primary)] focus:border-transparent",
        "transition-all duration-200",
        field.error && "border-red-500 focus:ring-red-500"
      );

      const labelClasses = cn(
        "block font-sans font-medium text-xs text-[var(--color-text-secondary)] uppercase mb-[2px]",
        field.required && "after:content-['*'] after:text-red-500 after:ml-1"
      );

      switch (field.type) {
        case 'select':
          // Helper function to get display label for options
          const getOptionLabel = (option: string, fieldId: string) => {
            if (fieldId === 'paymentMethod') {
              const labels: Record<string, string> = {
                'cash': 'Efectivo',
                'credit_card': 'Tarjeta de Crédito',
                'loan': 'Préstamo',
                'bank_transfer': 'Transferencia Bancaria',
                'check': 'Cheque'
              };
              return labels[option] || option;
            }
            if (fieldId === 'isRecurring') {
              return option === 'yes' ? 'Sí' : 'No';
            }
            return option;
          };

          return (
            <div key={field.id} className="flex flex-col gap-[2px]">
              <label htmlFor={field.id} className={labelClasses}>
                {field.label}
              </label>
              <select
                id={field.id}
                value={field.value || ''}
                onChange={(e) => onFieldChange(field.id, e.target.value)}
                className={cn(
                  baseInputClasses,
                  "appearance-none bg-[url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23666\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>')] bg-no-repeat bg-right-3 bg-center pr-[var(--spacing-32)]"
                )}
              >
                <option value="" disabled className="text-[var(--color-text-placeholder)]">
                  Select {field.label}
                </option>
                {field.options?.map((option) => (
                  <option key={option} value={option} className="text-[var(--color-text-primary)]">
                    {getOptionLabel(option, field.id)}
                  </option>
                ))}
              </select>
              {field.error && (
                <p className="text-xs text-red-500 mt-[2px]">{field.error}</p>
              )}
            </div>
          );

        case 'date':
          return (
            <div key={field.id} className="flex flex-col gap-[2px]">
              <label htmlFor={field.id} className={labelClasses}>
                {field.label}
              </label>
              <input
                id={field.id}
                type="date"
                value={field.value || ''}
                onChange={(e) => onFieldChange(field.id, e.target.value)}
                className={baseInputClasses}
              />
              {field.error && (
                <p className="text-xs text-red-500 mt-[2px]">{field.error}</p>
              )}
            </div>
          );

        case 'number':
        case 'currency':
          return (
            <div key={field.id} className="flex flex-col gap-[2px]">
              <label htmlFor={field.id} className={labelClasses}>
                {field.label}
              </label>
              <div className="relative">
                {field.type === 'currency' && (
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--color-text-secondary)] font-sans font-medium text-sm">
                    $
                  </span>
                )}
                <input
                  id={field.id}
                  type="number"
                  value={field.value || ''}
                  onChange={(e) => onFieldChange(field.id, e.target.value)}
                  placeholder={field.placeholder}
                  className={cn(
                    baseInputClasses,
                    field.type === 'currency' && "pl-[var(--spacing-24)]"
                  )}
                />
              </div>
              {field.error && (
                <p className="text-xs text-red-500 mt-[2px]">{field.error}</p>
              )}
            </div>
          );

        default:
          return (
            <div key={field.id} className="flex flex-col gap-[2px]">
              <label htmlFor={field.id} className={labelClasses}>
                {field.label}
              </label>
              <input
                id={field.id}
                type={field.type}
                value={field.value || ''}
                onChange={(e) => onFieldChange(field.id, e.target.value)}
                placeholder={field.placeholder}
                className={baseInputClasses}
              />
              {field.error && (
                <p className="text-xs text-red-500 mt-[2px]">{field.error}</p>
              )}
            </div>
          );
      }
    };

    return (
      <div
        ref={ref}
        className={cn("space-y-[var(--spacing-16)]", className)}
        {...props}
      >
        {/* Form Header */}
        <div>
          <h3 className="text-lg font-semibold text-[var(--color-text-primary)]">
            Create New {entityType}
          </h3>
        </div>

        {/* Form Fields */}
        <form onSubmit={handleSubmit} className="space-y-[var(--spacing-16)]">
          {entityType.toLowerCase().includes('inventory') ? (
            // Special layout for inventory items with sections
            <div className="space-y-[var(--spacing-20)]">
              {/* Inventory Details Section */}
              <div className="space-y-[var(--spacing-20)]">
                <h4 className="text-sm font-semibold text-[var(--color-text-primary)] border-b border-[var(--color-stroke)] pb-[var(--spacing-8)]">
                  Item Details
                </h4>
                {fields.filter(field => !['paymentMethod', 'isRecurring', 'monthlyAmount', 'totalMonths', 'interestRate', 'dueDate'].includes(field.id)).map(renderField)}
              </div>

              {/* Financial Registry Section */}
              <div className="space-y-[var(--spacing-20)]">
                <h4 className="text-sm font-semibold text-[var(--color-text-primary)] border-b border-[var(--color-stroke)] pb-[var(--spacing-8)]">
                  Financial Registry
                </h4>
                {(() => {
                  // Always show payment method and recurring payment toggle
                  const paymentMethodField = fields.find(f => f.id === 'paymentMethod');
                  const isRecurringField = fields.find(f => f.id === 'isRecurring');
                  const isRecurringEnabled = isRecurringField?.value === 'yes';

                  // Get recurring payment detail fields
                  const recurringFields = fields.filter(f =>
                    ['monthlyAmount', 'totalMonths', 'interestRate', 'dueDate'].includes(f.id)
                  );

                  const fieldsToShow = [];

                  // Always show payment method
                  if (paymentMethodField) fieldsToShow.push(paymentMethodField);

                  // Always show recurring payment toggle
                  if (isRecurringField) fieldsToShow.push(isRecurringField);

                  // Show recurring detail fields only if recurring is enabled
                  if (isRecurringEnabled) {
                    fieldsToShow.push(...recurringFields);
                  }

                  return fieldsToShow.map(renderField);
                })()}
              </div>
            </div>
          ) : (
            // Standard layout for other entity types
            <div className="space-y-[var(--spacing-20)]">
              {fields.map((field) => {
                // Show recurring payment fields only if isRecurring is 'yes'
                const isRecurringField = ['monthlyAmount', 'totalMonths', 'interestRate', 'dueDate'].includes(field.id);
                const isRecurringEnabled = fields.find(f => f.id === 'isRecurring')?.value === 'yes';

                if (isRecurringField && !isRecurringEnabled) {
                  return null; // Hide recurring fields when not needed
                }

                return renderField(field);
              })}
            </div>
          )}

          {/* Submit Button */}
          {onSubmit && (
            <div className="flex justify-end pt-[var(--spacing-16)] border-t border-[var(--color-stroke)]">
              <button
                type="submit"
                disabled={submitDisabled || loading}
                className={cn(
                  "px-[var(--spacing-16)] py-[var(--spacing-8)] rounded-[var(--radius-4)]",
                  "bg-[var(--color-frame-primary)] text-[var(--color-text-inverse)]",
                  "text-sm font-medium",
                  "hover:opacity-90 transition-opacity duration-200",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  "focus:outline-none focus:ring-2 focus:ring-[var(--color-frame-primary)] focus:ring-offset-2"
                )}
              >
                {loading ? 'Creating...' : submitText}
              </button>
            </div>
          )}
        </form>
      </div>
    );
  }
);

DataForm.displayName = "DataForm";

export { DataForm };
