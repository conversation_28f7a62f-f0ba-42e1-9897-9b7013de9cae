import type { <PERSON>a, StoryObj } from '@storybook/react';
import { DayContainerCard } from './day-container-card';
import { DateNumber } from './date-number';
import { EventCard } from './event-card';

const meta: Meta<typeof DayContainerCard> = {
  title: 'Components/DayContainerCard',
  component: DayContainerCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    state: {
      control: { type: 'select' },
      options: ['default', 'past'],
      description: 'State of the day container',
    },
    compact: {
      control: 'boolean',
      description: 'Whether to show minimal padding for compact layout',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    state: 'default',
    compact: false,
  },
  render: (args) => (
    <DayContainerCard {...args}>
      <DateNumber number={15} state="default" />
      <div className="flex flex-col gap-1">
        <EventCard text="Meeting" state="default" />
        <EventCard text="Review" state="active" />
      </div>
    </DayContainerCard>
  ),
};

export const Past: Story = {
  args: {
    state: 'past',
    compact: false,
  },
  render: (args) => (
    <DayContainerCard {...args}>
      <DateNumber number={10} state="inverse" />
      <div className="flex flex-col gap-1">
        <EventCard text="Completed" state="default" />
      </div>
    </DayContainerCard>
  ),
};

export const Compact: Story = {
  args: {
    state: 'default',
    compact: true,
  },
  render: (args) => (
    <DayContainerCard {...args}>
      <DateNumber number={20} state="active" />
      <EventCard text="Urgent" state="active" />
    </DayContainerCard>
  ),
};

export const CalendarWeek: Story = {
  render: () => (
    <div className="grid grid-cols-7 gap-2 w-[600px]">
      {/* Week days */}
      <DayContainerCard state="past" compact>
        <DateNumber number={1} state="inverse" />
        <EventCard text="Done" state="default" />
      </DayContainerCard>
      
      <DayContainerCard state="past" compact>
        <DateNumber number={2} state="inverse" />
      </DayContainerCard>
      
      <DayContainerCard state="default" compact>
        <DateNumber number={3} state="default" />
        <div className="flex flex-col gap-1">
          <EventCard text="Meeting" state="default" />
          <EventCard text="Call" state="active" />
        </div>
      </DayContainerCard>
      
      <DayContainerCard state="default" compact>
        <DateNumber number={4} state="default" />
        <EventCard text="Review" state="default" />
      </DayContainerCard>
      
      <DayContainerCard state="default" compact>
        <DateNumber number={5} state="active" />
        <div className="flex flex-col gap-1">
          <EventCard text="Deadline" state="active" />
          <EventCard text="Demo" state="default" />
        </div>
      </DayContainerCard>
      
      <DayContainerCard state="default" compact>
        <DateNumber number={6} state="default" />
      </DayContainerCard>
      
      <DayContainerCard state="default" compact>
        <DateNumber number={7} state="default" />
        <EventCard text="Planning" state="default" />
      </DayContainerCard>
    </div>
  ),
};
