"use client";

import * as React from "react";
import { Send, Loader2 } from "lucide-react";
import { cn } from "./lib/utils";
import { ChatBubble } from "./chat-bubble";
import { StreamingChatBubble } from "./streaming-chat-bubble";

export interface ConversationStep {
  type: string;
  title: string;
  isComplete: boolean;
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export interface GuidedConversationProps {
  /** Current step in the conversation */
  currentStep?: string;
  /** All conversation steps */
  steps: ConversationStep[];
  /** Conversation history */
  messages: ConversationMessage[];
  /** Whether AI is currently typing */
  isTyping?: boolean;
  /** Whether AI is streaming a response */
  isStreaming?: boolean;
  /** AI message being streamed */
  streamingMessage?: string;
  /** Callback when streaming is complete */
  onStreamingComplete?: () => void;
  /** Callback when user sends a message */
  onSendMessage: (message: string) => void;
  /** Whether the conversation is in loading state */
  loading?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const GuidedConversation = React.forwardRef<HTMLDivElement, GuidedConversationProps>(
  ({
    className,
    currentStep,
    steps,
    messages,
    isTyping = false,
    isStreaming = false,
    streamingMessage,
    onStreamingComplete,
    onSendMessage,
    loading = false
  }, ref) => {
    const [inputMessage, setInputMessage] = React.useState('');
    const messagesEndRef = React.useRef<HTMLDivElement>(null);
    const messagesContainerRef = React.useRef<HTMLDivElement>(null);

    // Auto-scroll to bottom when new messages arrive
    React.useEffect(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }, [messages, isTyping, isStreaming]);

    const handleSendMessage = (e: React.FormEvent) => {
      e.preventDefault();
      if (inputMessage.trim() && !loading && !isTyping && onSendMessage) {
        onSendMessage(inputMessage.trim());
        setInputMessage('');
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSendMessage(e);
      }
    };

    // Get current step info
    const currentStepInfo = steps.find(step => step.type === currentStep);
    const completedSteps = steps.filter(step => step.isComplete).length;

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col h-full bg-[var(--color-background)]",
          className
        )}
      >
        {/* Progress Header */}
        <div className="flex-shrink-0 p-[var(--spacing-16)] border-b border-[var(--color-stroke)]">
          <div className="flex items-center justify-between mb-[var(--spacing-8)]">
            <h3 className="text-[var(--typography-h3-size)] font-[var(--typography-h3-weight)] text-[var(--color-text-primary)]">
              {currentStepInfo?.title || 'Project Creation'}
            </h3>
            <span className="text-[var(--typography-body-size)] text-[var(--color-text-secondary)]">
              Step {completedSteps + 1} of {steps.length}
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-[var(--color-background-secondary)] rounded-full h-2">
            <div 
              className="bg-[var(--color-primary)] h-2 rounded-full transition-all duration-300"
              style={{ width: `${(completedSteps / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Messages Area */}
        <div
          ref={messagesContainerRef}
          className="flex-1 overflow-y-auto p-[var(--spacing-16)] space-y-[var(--spacing-12)]"
        >
          {messages.map((message) => (
            <ChatBubble
              key={message.id}
              message={message.content}
              isUser={message.role === 'user'}
              timestamp={message.timestamp}
              showTimestamp={false}
            />
          ))}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex items-center space-x-[var(--spacing-8)]">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-[var(--color-text-secondary)] rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-[var(--color-text-secondary)] rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-[var(--color-text-secondary)] rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
              <span className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)]">
                AI is thinking...
              </span>
            </div>
          )}

          {/* Streaming Response */}
          {isStreaming && streamingMessage && (
            <StreamingChatBubble
              message={streamingMessage}
              isUser={false}
              isStreaming={isStreaming}
              onStreamingComplete={onStreamingComplete}
            />
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="flex-shrink-0 p-[var(--spacing-16)] border-t border-[var(--color-stroke)]">
          <form onSubmit={handleSendMessage} className="flex items-end space-x-[var(--spacing-12)]">
            <div className="flex-1">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your response..."
                disabled={loading || isTyping}
                className={cn(
                  "w-full min-h-[44px] max-h-[120px] px-[var(--spacing-12)] py-[var(--spacing-8)]",
                  "bg-[var(--color-background)] border border-[var(--color-stroke)] rounded-[var(--radius-md)]",
                  "text-[var(--typography-body-size)] text-[var(--color-text-primary)]",
                  "placeholder:text-[var(--color-text-placeholder)]",
                  "focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  "resize-none"
                )}
                rows={1}
                style={{
                  height: 'auto',
                  minHeight: '44px'
                }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = `${Math.min(target.scrollHeight, 120)}px`;
                }}
              />
            </div>
            
            <button
              type="submit"
              disabled={!inputMessage.trim() || loading || isTyping}
              className={cn(
                "flex-shrink-0 w-[44px] h-[44px] rounded-[var(--radius-md)]",
                "bg-[var(--color-primary)] text-[var(--color-text-on-primary)]",
                "flex items-center justify-center",
                "hover:bg-[var(--color-primary-hover)] transition-colors duration-200",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-[var(--color-primary)]",
                "focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-offset-2"
              )}
            >
              {loading || isTyping ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          </form>
        </div>
      </div>
    );
  }
);

GuidedConversation.displayName = "GuidedConversation";

export { GuidedConversation };
