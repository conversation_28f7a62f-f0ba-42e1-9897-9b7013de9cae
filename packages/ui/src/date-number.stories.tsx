import type { <PERSON>a, StoryObj } from '@storybook/react';
import { DateNumber } from './date-number';

const meta: Meta<typeof DateNumber> = {
  title: 'Components/DateNumber',
  component: DateNumber,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    number: {
      control: { type: 'text' },
      description: 'The date number to display',
    },
    state: {
      control: { type: 'select' },
      options: ['default', 'inverse', 'active'],
      description: 'State of the date number',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    number: 15,
    state: 'default',
  },
};

export const Inverse: Story = {
  args: {
    number: 20,
    state: 'inverse',
  },
};

export const Active: Story = {
  args: {
    number: 25,
    state: 'active',
  },
};

export const AllStates: Story = {
  render: () => (
    <div className="flex gap-4 items-center">
      <div className="text-center">
        <DateNumber number={15} state="default" />
        <p className="text-xs mt-2">Default</p>
      </div>
      <div className="text-center">
        <DateNumber number={20} state="inverse" />
        <p className="text-xs mt-2">Inverse</p>
      </div>
      <div className="text-center">
        <DateNumber number={25} state="active" />
        <p className="text-xs mt-2">Active</p>
      </div>
    </div>
  ),
};

export const CalendarRow: Story = {
  render: () => (
    <div className="flex gap-2">
      <DateNumber number={1} state="default" />
      <DateNumber number={2} state="default" />
      <DateNumber number={3} state="inverse" />
      <DateNumber number={4} state="default" />
      <DateNumber number={5} state="active" />
      <DateNumber number={6} state="default" />
      <DateNumber number={7} state="default" />
    </div>
  ),
};
