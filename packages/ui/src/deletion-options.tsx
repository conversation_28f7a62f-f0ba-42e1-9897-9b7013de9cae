"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface DeletionOptionsProps {
  /** Entity type being deleted */
  entityType: 'inventory' | 'employee' | 'project' | 'financial' | 'catalog';
  /** Current deletion context */
  deletionContext: {
    reason?: 'sold' | 'disposed' | 'transferred' | 'expired' | 'damaged' | 'other';
    notes?: string;
    transferTo?: string;
    salePrice?: number;
  };
  /** Callback when deletion context changes */
  onContextChange: (context: DeletionOptionsProps['deletionContext']) => void;
  /** Additional CSS classes */
  className?: string;
}

const DeletionOptions = React.forwardRef<HTMLDivElement, DeletionOptionsProps>(
  ({
    className,
    entityType,
    deletionContext,
    onContextChange,
    ...props
  }, ref) => {
    const handleReasonChange = (reason: DeletionOptionsProps['deletionContext']['reason']) => {
      onContextChange({
        ...deletionContext,
        reason,
        // Clear context-specific fields when reason changes
        transferTo: reason === 'transferred' ? deletionContext.transferTo : undefined,
        salePrice: reason === 'sold' ? deletionContext.salePrice : undefined,
      });
    };

    const handleFieldChange = (field: string, value: string | number) => {
      onContextChange({
        ...deletionContext,
        [field]: value
      });
    };

    // Only show deletion options for inventory items
    if (entityType !== 'inventory') {
      return (
        <div ref={ref} className={cn("space-y-[var(--spacing-12)]", className)} {...props}>
          <div>
            <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
              Notes (optional)
            </label>
            <textarea
              value={deletionContext.notes || ''}
              onChange={(e) => handleFieldChange('notes', e.target.value)}
              placeholder="Add any notes about this deletion..."
              className="w-full px-[var(--spacing-12)] py-[var(--spacing-8)] text-sm border border-[var(--color-stroke)] rounded-[var(--radius-4)] bg-[var(--color-background-primary)] text-[var(--color-text-primary)] placeholder-[var(--color-text-placeholder)] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
            />
          </div>
        </div>
      );
    }

    return (
      <div ref={ref} className={cn("space-y-[var(--spacing-12)]", className)} {...props}>
        {/* Deletion Reason */}
        <div>
          <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-8)]">
            Reason for deletion
          </label>
          <div className="grid grid-cols-2 gap-[var(--spacing-8)]">
            {[
              { value: 'sold', label: 'Sold' },
              { value: 'disposed', label: 'Disposed' },
              { value: 'transferred', label: 'Transferred' },
              { value: 'expired', label: 'Expired' },
              { value: 'damaged', label: 'Damaged' },
              { value: 'other', label: 'Other' }
            ].map((option) => (
              <label key={option.value} className="flex items-center space-x-[var(--spacing-8)] cursor-pointer">
                <input
                  type="radio"
                  name="deletionReason"
                  value={option.value}
                  checked={deletionContext.reason === option.value}
                  onChange={() => handleReasonChange(option.value as any)}
                  className="w-4 h-4 text-blue-600 border-[var(--color-stroke)] focus:ring-blue-500"
                />
                <span className="text-sm text-[var(--color-text-primary)]">{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Sale Price (if sold) */}
        {deletionContext.reason === 'sold' && (
          <div>
            <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
              Sale Price
            </label>
            <div className="relative">
              <span className="absolute left-[var(--spacing-12)] top-1/2 transform -translate-y-1/2 text-sm text-[var(--color-text-secondary)]">
                $
              </span>
              <input
                type="number"
                value={deletionContext.salePrice || ''}
                onChange={(e) => handleFieldChange('salePrice', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="w-full pl-[var(--spacing-24)] pr-[var(--spacing-12)] py-[var(--spacing-8)] text-sm border border-[var(--color-stroke)] rounded-[var(--radius-4)] bg-[var(--color-background-primary)] text-[var(--color-text-primary)] placeholder-[var(--color-text-placeholder)] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Transfer Destination (if transferred) */}
        {deletionContext.reason === 'transferred' && (
          <div>
            <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
              Transfer To
            </label>
            <input
              type="text"
              value={deletionContext.transferTo || ''}
              onChange={(e) => handleFieldChange('transferTo', e.target.value)}
              placeholder="Destination location or department"
              className="w-full px-[var(--spacing-12)] py-[var(--spacing-8)] text-sm border border-[var(--color-stroke)] rounded-[var(--radius-4)] bg-[var(--color-background-primary)] text-[var(--color-text-primary)] placeholder-[var(--color-text-placeholder)] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        )}

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
            Notes (optional)
          </label>
          <textarea
            value={deletionContext.notes || ''}
            onChange={(e) => handleFieldChange('notes', e.target.value)}
            placeholder="Add any additional notes about this deletion..."
            className="w-full px-[var(--spacing-12)] py-[var(--spacing-8)] text-sm border border-[var(--color-stroke)] rounded-[var(--radius-4)] bg-[var(--color-background-primary)] text-[var(--color-text-primary)] placeholder-[var(--color-text-placeholder)] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={3}
          />
        </div>
      </div>
    );
  }
);

DeletionOptions.displayName = "DeletionOptions";

export { DeletionOptions };
