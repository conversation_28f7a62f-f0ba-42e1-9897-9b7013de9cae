import type { Meta, StoryObj } from '@storybook/react';
import { FinanceCard } from './finance-card';

const meta: Meta<typeof FinanceCard> = {
  title: 'Components/FinanceCard',
  component: FinanceCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Finance section title',
    },
    isActive: {
      control: 'boolean',
      description: 'Whether the card is in active/selected state',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: 'Activos',
  },
  render: (args) => (
    <div className="w-48 h-32">
      <FinanceCard {...args} />
    </div>
  ),
};

export const Active: Story = {
  args: {
    title: 'Activos',
    isActive: true,
  },
  render: (args) => (
    <div className="w-48 h-32">
      <FinanceCard {...args} />
    </div>
  ),
};

export const AllFinanceSections: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 w-[600px]">
      <div className="h-32">
        <FinanceCard title="Activos" />
      </div>
      <div className="h-32">
        <FinanceCard title="Pasivos" />
      </div>
      <div className="h-32">
        <FinanceCard title="Pronósticos" />
      </div>
      <div className="h-32">
        <FinanceCard title="Movimientos" isActive />
      </div>
      <div className="h-32">
        <FinanceCard title="Estados Financieros" />
      </div>
    </div>
  ),
};

export const TwoColumnLayout: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 w-[400px]">
      <div className="h-32">
        <FinanceCard title="Movimientos" />
      </div>
      <div className="h-32">
        <FinanceCard title="Activos" isActive />
      </div>
    </div>
  ),
};
