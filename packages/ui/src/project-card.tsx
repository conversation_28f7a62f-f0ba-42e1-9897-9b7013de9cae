"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface ProjectCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Project name */
  projectName?: string;
  /** Project initials for the avatar */
  initials?: string;
  /** Start date */
  startDate?: string;
  /** End date */
  endDate?: string;
  /** Current progress (completed tasks) */
  currentProgress?: number;
  /** Total tasks */
  totalTasks?: number;
  /** Project status */
  status?: string;
  /** Progress percentage (0-100) for the progress bar */
  progressPercentage?: number;
  /** Click handler for the card */
  onCardClick?: () => void;
  /** Whether the card is in active state */
  isActive?: boolean;
}

const ProjectCard = React.forwardRef<HTMLDivElement, ProjectCardProps>(
  ({
    className,
    projectName = "Tommy Hilfiger Cuernavaca",
    initials = "TH",
    startDate = "08 Ago, 2024",
    endDate = "31 Ago, 2024",
    currentProgress = 4,
    totalTasks = 12,
    status = "En proceso",
    progressPercentage = 33,
    onCardClick,
    isActive = false,
    ...props
  }, ref) => {
    return (
      <div
        ref={ref}
        onClick={onCardClick}
        className={cn(
          "project-card w-full border border-[var(--color-stroke)] rounded-[var(--radius-8)] p-[var(--spacing-12)] cursor-pointer hover-bg flex flex-col gap-4",
          isActive
            ? "bg-[var(--color-background-secondary)]"
            : "bg-[var(--color-background-primary)]",
          className
        )}
        {...props}
      >
        {/* Header - Initials and Project Name */}
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-[var(--color-blue)] rounded flex items-center justify-center flex-shrink-0">
            <span className="font-inter font-bold text-xs text-white">
              {initials}
            </span>
          </div>
          <h3 className="font-inter font-semibold text-base text-[var(--color-text-primary)] flex-1 min-w-0">
            {projectName}
          </h3>
        </div>

        {/* Timeline Section */}
        <div className="flex flex-col gap-3">
          {/* Dates */}
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <span className="font-inter font-semibold text-xs text-[var(--color-text-primary)]">
                Comienzo
              </span>
              <span className="font-inter text-xs text-[var(--color-text-secondary)]">
                {startDate}
              </span>
            </div>
            <div className="flex flex-col text-right">
              <span className="font-inter font-semibold text-xs text-[var(--color-text-primary)]">
                Entrega
              </span>
              <span className="font-inter text-xs text-[var(--color-text-secondary)]">
                {endDate}
              </span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="relative w-full h-1 bg-[var(--color-background-secondary)] rounded-full">
            <div 
              className="absolute top-0 left-0 h-full bg-[var(--color-text-secondary)] rounded-full transition-all duration-300"
              style={{ width: `${Math.min(Math.max(progressPercentage, 0), 100)}%` }}
            />
            {/* Progress indicator dot */}
            <div 
              className="absolute top-1/2 w-3 h-3 bg-[var(--color-background-primary)] border-2 border-[var(--color-text-secondary)] rounded-full transform -translate-y-1/2 transition-all duration-300"
              style={{ left: `calc(${Math.min(Math.max(progressPercentage, 0), 100)}% - 6px)` }}
            />
          </div>
        </div>

        {/* Footer - Progress and Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <span className="text-[var(--color-text-secondary)]">✓</span>
            <span className="font-inter font-semibold text-sm text-[var(--color-text-primary)]">
              {currentProgress}/{totalTasks}
            </span>
          </div>
          <div className="bg-[var(--color-blue-light)] px-3 py-1 rounded">
            <span className="font-inter font-semibold text-xs text-[var(--color-blue)]">
              {status}
            </span>
          </div>
        </div>
      </div>
    );
  }
);

ProjectCard.displayName = "ProjectCard";

export { ProjectCard };
