import type { Meta, StoryObj } from '@storybook/react';
import { FinanceTabButton } from './finance-tab-button';

const meta: Meta<typeof FinanceTabButton> = {
  title: 'Components/FinanceTabButton',
  component: FinanceTabButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Tab title',
    },
    isActive: {
      control: 'boolean',
      description: 'Whether the tab is active/selected',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: 'Activos',
  },
};

export const Active: Story = {
  args: {
    title: 'Forecast',
    isActive: true,
  },
};

export const AllFinanceTabs: Story = {
  render: () => (
    <div className="flex gap-2">
      <FinanceTabButton title="Activos" />
      <FinanceTabButton title="Pasivos" />
      <FinanceTabButton title="Forecast" isActive />
      <FinanceTabButton title="Movimientos" />
      <FinanceTabButton title="Estados Financieros" />
    </div>
  ),
};

export const LongTitles: Story = {
  render: () => (
    <div className="flex gap-2">
      <FinanceTabButton title="Estados Financieros" isActive />
      <FinanceTabButton title="Pronósticos Anuales" />
      <FinanceTabButton title="Movimientos Detallados" />
    </div>
  ),
};
