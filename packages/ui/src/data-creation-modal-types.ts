import { <PERSON>Field } from "./data-form";
import { ProjectModuleType, ModuleRecommendation } from "./module-selector";
import { ConversationStep, ConversationMessage } from "./guided-conversation";
import { SelectedCatalogItem } from "./catalog-selection";
import { ContextDataResult } from "./context-selection";

export interface DataCreationModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback to close the modal */
  onClose: () => void;
  /** Entity type being created */
  entityType: string;
  /** Form fields to display */
  fields: FormField[];
  /** Whether the form is in loading state */
  loading?: boolean;
  /** Whether the form submission is disabled */
  submitDisabled?: boolean;
  /** Callback when field value changes */
  onFieldChange: (fieldId: string, value: any) => void;
  /** Callback when form is submitted */
  onSubmit: () => void;
  /** Original command that started this creation */
  originalCommand?: string;
  /** Unknown category notification message */
  unknownCategoryMessage?: string;
  /** AI conversation response message */
  aiMessage?: string;
  /** Whether to show the form or keep in conversation mode */
  showForm?: boolean;
  /** Conversation history for displaying all messages */
  conversationHistory?: ConversationMessage[];
  /** Callback for quick form submission */
  onQuickFormSubmit?: (message: string) => void;
  /** Whether AI is waiting for more information */
  aiNeedsMoreInfo?: boolean;
  /** Whether AI is currently typing/generating response */
  aiIsTyping?: boolean;
  /** Whether to stream the AI response */
  aiIsStreaming?: boolean;
  /** Callback when AI streaming is complete */
  onAiStreamingComplete?: () => void;
  /** Module recommendations for project creation */
  moduleRecommendations?: ModuleRecommendation[];
  /** Selected modules for project creation */
  selectedModules?: ProjectModuleType[];
  /** Module selection method */
  moduleSelectionMethod?: 'manual' | 'ai-recommended' | 'hybrid';
  /** Callback when module selection changes */
  onModuleToggle?: (module: ProjectModuleType, enabled: boolean) => void;
  /** Callback when module selection method changes */
  onModuleSelectionMethodChange?: (method: 'manual' | 'ai-recommended' | 'hybrid') => void;
  /** Whether this is a guided conversation flow */
  isGuidedFlow?: boolean;
  /** Current step in guided conversation */
  currentStep?: string;
  /** All conversation steps */
  conversationSteps?: Array<{ type: string; title: string; isComplete: boolean }>;
  /** Callback when user sends a message in guided flow */
  onGuidedMessage?: (message: string) => void;
  /** Whether to show catalog selection interface */
  showCatalogSelection?: boolean;
  /** Available catalog items for selection */
  catalogItems?: Array<{
    id: string;
    productName: string;
    productDescription: string;
    categoryLabel: 'Product' | 'Service';
    imageSrc?: string;
  }>;
  /** Currently selected catalog items */
  selectedCatalogItems?: SelectedCatalogItem[];
  /** Callback when catalog selection changes */
  onCatalogSelectionChange?: (selectedItems: SelectedCatalogItem[]) => void;
  /** Whether catalog items are loading */
  catalogLoading?: boolean;
  /** Whether to show context selection interface */
  showContextSelection?: boolean;
  /** Context data for selection */
  contextData?: ContextDataResult;
  /** Callback when context selection changes */
  onContextSelectionChange?: (itemId: string, isSelected: boolean, quantity?: number) => void;
  /** Callback when context selection is confirmed */
  onContextSelectionConfirm?: () => void;
  /** Additional CSS classes */
  className?: string;
}
