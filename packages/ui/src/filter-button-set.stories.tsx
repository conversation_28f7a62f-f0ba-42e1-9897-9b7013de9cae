import type { Meta, StoryObj } from '@storybook/react';
import { FilterButtonSet } from './filter-button-set';

const meta: Meta<typeof FilterButtonSet> = {
  title: 'UI/FilterButtonSet',
  component: FilterButtonSet,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isActive: {
      control: 'boolean',
      description: 'Whether the filter is active/selected',
    },
    showBothStates: {
      control: 'boolean',
      description: 'Show both active and inactive states for demo',
    },
    onFilterClick: {
      action: 'filter-clicked',
      description: 'Callback when filter button is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    isActive: false,
    showBothStates: false,
  },
};

export const Active: Story = {
  args: {
    isActive: true,
    showBothStates: false,
  },
};

export const BothStates: Story = {
  args: {
    showBothStates: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows both inactive (top) and active (bottom) states for comparison.',
      },
    },
  },
};

export const Interactive: Story = {
  args: {
    isActive: false,
    showBothStates: false,
  },
  render: (args) => {
    return (
      <div className="flex gap-4">
        <FilterButtonSet 
          {...args}
          onFilterClick={() => console.log('Filter clicked')}
        />
        <FilterButtonSet 
          {...args}
          isActive={true}
          onFilterClick={() => console.log('Active filter clicked')}
        />
      </div>
    );
  },
};
