import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { GanttTimeline, type GanttTimelineTask } from "./gantt-timeline";

const meta: Meta<typeof GanttTimeline> = {
  title: "Components/GanttTimeline",
  component: GanttTimeline,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample tasks for the stories
const sampleTasks: GanttTimelineTask[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON> financiero",
    start: new Date(2024, 5, 2), // June 2, 2024
    end: new Date(2024, 5, 5),   // June 5, 2024
    progress: 100,
    type: "task",
    employee: {
      id: "ana",
      name: "<PERSON> <PERSON>",
      avatar: "AG"
    }
  },
  {
    id: "2",
    name: "<PERSON><PERSON> plan<PERSON>",
    start: new Date(2024, 5, 6),  // June 6, 2024
    end: new Date(2024, 5, 10),   // June 10, 2024
    progress: 75,
    type: "task",
    dependencies: ["1"],
    employee: {
      id: "carlos",
      name: "<PERSON>",
      avatar: "C<PERSON>"
    }
  },
  {
    id: "3",
    name: "<PERSON><PERSON><PERSON> de material<PERSON>",
    start: new Date(2024, 5, 12), // June 12, 2024
    end: new Date(2024, 5, 16),   // June 16, 2024
    progress: 50,
    type: "task",
    dependencies: ["2"],
    employee: {
      id: "maria",
      name: "<PERSON> López",
      avatar: "ML"
    }
  },
  {
    id: "4",
    name: "Fabricación",
    start: new Date(2024, 5, 18), // June 18, 2024
    end: new Date(2024, 5, 25),   // June 25, 2024
    progress: 25,
    type: "task",
    dependencies: ["3"],
    employee: {
      id: "roberto",
      name: "Roberto Silva",
      avatar: "RS"
    }
  },
  {
    id: "5",
    name: "Control de calidad",
    start: new Date(2024, 5, 26), // June 26, 2024
    end: new Date(2024, 5, 30),   // June 30, 2024
    progress: 0,
    type: "task",
    dependencies: ["4"],
    employee: {
      id: "laura",
      name: "Laura Martínez",
      avatar: "LM"
    }
  },
  {
    id: "6",
    name: "Entrega del proyecto",
    start: new Date(2024, 5, 30), // June 30, 2024
    end: new Date(2024, 5, 30),   // June 30, 2024
    progress: 0,
    type: "milestone",
    dependencies: ["5"],
  }
];

export const Default: Story = {
  args: {
    tasks: sampleTasks,
  },
};

export const EmptyState: Story = {
  args: {
    tasks: [],
  },
};

export const WithInteractions: Story = {
  args: {
    tasks: sampleTasks,
    onTaskClick: (task) => {
      alert(`Clicked on task: ${task.name}`);
    },
    onTaskUpdate: (task) => {
      console.log("Task updated:", task);
    },
  },
};
