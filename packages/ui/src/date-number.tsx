"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface DateNumberProps extends React.HTMLAttributes<HTMLDivElement> {
  /** The date number to display */
  number?: number | string;
  /** State of the date number */
  state?: 'default' | 'inverse' | 'active';
  /** Whether to position in bottom right corner */
  positioned?: boolean;
}

const DateNumber = React.forwardRef<HTMLDivElement, DateNumberProps>(
  ({
    className,
    number = 1,
    state = 'default',
    positioned = false,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const getBackgroundColor = () => {
      switch (state) {
        case 'inverse':
          return 'var(--color-background-primary)';
        case 'active':
          return 'var(--color-red)';
        default:
          return 'var(--color-background-secondary)';
      }
    };

    const getTextColor = () => {
      switch (state) {
        case 'active':
          return 'var(--color-text-inverse)';
        default:
          return 'var(--color-text-primary)';
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "w-7 h-7 rounded-full flex items-center justify-center font-inter font-medium text-xs",
          positioned ? "absolute bottom-2 right-2" : "",
          className
        )}
        style={{
          backgroundColor: getBackgroundColor(),
          color: getTextColor(),
        }}
        {...props}
      >
        {number}
      </div>
    );
  }
);

DateNumber.displayName = "DateNumber";

export { DateNumber };
