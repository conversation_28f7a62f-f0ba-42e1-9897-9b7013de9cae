import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { CalendarTimeline, type CalendarTimelineTask } from './calendar-timeline';

const meta: Meta<typeof CalendarTimeline> = {
  title: 'Components/CalendarTimeline',
  component: CalendarTimeline,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    month: {
      control: 'text',
      description: 'Current month name',
    },
    nextMonth: {
      control: 'text',
      description: 'Next month name',
    },
    daysInMonth: {
      control: { type: 'number', min: 28, max: 31 },
      description: 'Number of days in the current month',
    },
    onTaskClick: {
      action: 'task-clicked',
      description: 'Callback when a task is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleTasks: CalendarTimelineTask[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON> financiero',
    startDate: 2,
    endDate: 5,
    employees: [
      {
        id: 'emp1',
        name: '<PERSON> <PERSON>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
  {
    id: '2',
    name: 'Hacer planos',
    startDate: 3,
    endDate: 8,
    employees: [
      {
        id: 'emp2',
        name: 'Carlos Mendoza',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp3',
        name: 'María López',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
  {
    id: '3',
    name: 'Compra de materiales',
    startDate: 10,
    endDate: 15,
    employees: [
      {
        id: 'emp4',
        name: 'Roberto Silva',
      },
      {
        id: 'emp5',
        name: 'Laura Martínez',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp6',
        name: 'Diego Ramírez',
      },
    ],
  },
  {
    id: '4',
    name: 'Instalación eléctrica',
    startDate: 18,
    endDate: 25,
    employees: [
      {
        id: 'emp7',
        name: 'Fernando Torres',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp8',
        name: 'Isabel Moreno',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
  {
    id: '5',
    name: 'Acabados finales',
    startDate: 26,
    endDate: 31,
    employees: [
      {
        id: 'emp9',
        name: 'Alejandro Ruiz',
      },
      {
        id: 'emp10',
        name: 'Patricia Vega',
        avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp11',
        name: 'Miguel Herrera',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp12',
        name: 'Carmen Jiménez',
      },
    ],
  },
];

export const Default: Story = {
  args: {
    month: 'Junio',
    nextMonth: 'Julio',
    daysInMonth: 30,
    tasks: sampleTasks,
  },
};

export const February: Story = {
  args: {
    month: 'Febrero',
    nextMonth: 'Marzo',
    daysInMonth: 28,
    tasks: [
      {
        id: '1',
        name: 'Planificación inicial',
        startDate: 1,
        endDate: 7,
        employees: [
          {
            id: 'emp1',
            name: 'Ana García',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          },
        ],
      },
      {
        id: '2',
        name: 'Desarrollo fase 1',
        startDate: 8,
        endDate: 21,
        employees: [
          {
            id: 'emp2',
            name: 'Carlos Mendoza',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          },
          {
            id: 'emp3',
            name: 'María López',
          },
        ],
      },
      {
        id: '3',
        name: 'Revisión y entrega',
        startDate: 22,
        endDate: 28,
        employees: [
          {
            id: 'emp4',
            name: 'Roberto Silva',
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
          },
        ],
      },
    ],
  },
};

export const EmptyTimeline: Story = {
  args: {
    month: 'Agosto',
    nextMonth: 'Septiembre',
    daysInMonth: 31,
    tasks: [],
  },
};

export const SingleTask: Story = {
  args: {
    month: 'Mayo',
    nextMonth: 'Junio',
    daysInMonth: 31,
    tasks: [
      {
        id: '1',
        name: 'Proyecto completo',
        startDate: 1,
        endDate: 31,
        employees: [
          {
            id: 'emp1',
            name: 'Ana García',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          },
          {
            id: 'emp2',
            name: 'Carlos Mendoza',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          },
          {
            id: 'emp3',
            name: 'María López',
          },
          {
            id: 'emp4',
            name: 'Roberto Silva',
          },
          {
            id: 'emp5',
            name: 'Laura Martínez',
          },
        ],
      },
    ],
  },
};

export const Interactive: Story = {
  render: () => {
    const handleTaskClick = (task: CalendarTimelineTask) => {
      alert(`Clicked on: ${task.name}\nDuration: ${task.startDate} - ${task.endDate}\nEmployees: ${task.employees.map(e => e.name).join(', ')}`);
    };

    return (
      <div className="w-full">
        <CalendarTimeline
          month="Junio"
          nextMonth="Julio"
          daysInMonth={30}
          tasks={sampleTasks}
          onTaskClick={handleTaskClick}
        />
      </div>
    );
  },
};

export const OverlappingTasks: Story = {
  args: {
    month: 'Abril',
    nextMonth: 'Mayo',
    daysInMonth: 30,
    tasks: [
      {
        id: '1',
        name: 'Diseño arquitectónico',
        startDate: 1,
        endDate: 15,
        employees: [
          {
            id: 'emp1',
            name: 'Ana García',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          },
        ],
      },
      {
        id: '2',
        name: 'Permisos y licencias',
        startDate: 5,
        endDate: 20,
        employees: [
          {
            id: 'emp2',
            name: 'Carlos Mendoza',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          },
        ],
      },
      {
        id: '3',
        name: 'Preparación del sitio',
        startDate: 10,
        endDate: 25,
        employees: [
          {
            id: 'emp3',
            name: 'María López',
          },
          {
            id: 'emp4',
            name: 'Roberto Silva',
          },
        ],
      },
      {
        id: '4',
        name: 'Construcción inicial',
        startDate: 20,
        endDate: 30,
        employees: [
          {
            id: 'emp5',
            name: 'Laura Martínez',
            avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
          },
          {
            id: 'emp6',
            name: 'Diego Ramírez',
          },
          {
            id: 'emp7',
            name: 'Fernando Torres',
          },
        ],
      },
    ],
  },
};
