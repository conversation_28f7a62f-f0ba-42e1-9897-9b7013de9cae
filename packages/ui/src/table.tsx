"use client";

import * as React from "react";
import { cn } from "./lib/utils";

// Table Root Component
export interface TableProps extends React.HTMLAttributes<HTMLTableElement> {}

const Table = React.forwardRef<HTMLTableElement, TableProps>(
  ({ className, ...props }, ref) => (
    <table
      ref={ref}
      className={cn("w-full border-separate border-spacing-0 rounded-[var(--radius-4)] overflow-hidden", className)}
      {...props}
    />
  )
);
Table.displayName = "Table";

// Table Header Component
export interface TableHeaderProps extends React.HTMLAttributes<HTMLTableSectionElement> {}

const TableHeader = React.forwardRef<HTMLTableSectionElement, TableHeaderProps>(
  ({ className, ...props }, ref) => (
    <thead ref={ref} className={cn("", className)} {...props} />
  )
);
TableHeader.displayName = "TableHeader";

// Table Header Row Component
export interface TableHeaderRowProps extends React.HTMLAttributes<HTMLTableRowElement> {}

const TableHeaderRow = React.forwardRef<HTMLTableRowElement, TableHeaderRowProps>(
  ({ className, ...props }, ref) => (
    <tr ref={ref} className={cn("", className)} {...props} />
  )
);
TableHeaderRow.displayName = "TableHeaderRow";

// Table Header Cell Component
export interface TableHeaderCellProps extends React.ThHTMLAttributes<HTMLTableCellElement> {
  /** Position variant for corner radius */
  variant?: 'first' | 'middle' | 'last';
}

const TableHeaderCell = React.forwardRef<HTMLTableCellElement, TableHeaderCellProps>(
  ({ className, children, variant = 'middle', ...props }, ref) => {
    const getHeaderStyles = () => {
      const baseStyles = "py-[var(--spacing-4)] text-left bg-[var(--color-background-secondary)]";
      switch (variant) {
        case 'first':
          return `${baseStyles} rounded-tl-[var(--radius-4)]`;
        case 'last':
          return `${baseStyles} rounded-tr-[var(--radius-4)]`;
        default:
          return baseStyles;
      }
    };

    const getChildStyles = () => {
      switch (variant) {
        case 'first':
          return "px-[var(--spacing-12)]";
        case 'middle':
          return "border-l border-[var(--color-stroke)] px-[var(--spacing-12)]";
        case 'last':
          return "border-l border-[var(--color-stroke)] px-[var(--spacing-12)]";
        default:
          return "px-[var(--spacing-12)]";
      }
    };

    return (
      <th
        ref={ref}
        className={cn(getHeaderStyles(), className)}
        {...props}
      >
        <div className={getChildStyles()}>
          <span className="font-semibold text-[10px] text-[var(--color-text-primary)] font-sans whitespace-nowrap overflow-hidden text-ellipsis leading-[0.8]">
            {children}
          </span>
        </div>
      </th>
    );
  }
);
TableHeaderCell.displayName = "TableHeaderCell";

// Table Body Component
export interface TableBodyProps extends React.HTMLAttributes<HTMLTableSectionElement> {}

const TableBody = React.forwardRef<HTMLTableSectionElement, TableBodyProps>(
  ({ className, ...props }, ref) => (
    <tbody
      ref={ref}
      className={cn("mt-[var(--spacing-4)]", className)}
      {...props}
    />
  )
);
TableBody.displayName = "TableBody";

// Table Body Row Component
export interface TableBodyRowProps extends React.HTMLAttributes<HTMLTableRowElement> {}

const TableBodyRow = React.forwardRef<HTMLTableRowElement, TableBodyRowProps>(
  ({ className, ...props }, ref) => (
    <tr ref={ref} className={cn("", className)} {...props} />
  )
);
TableBodyRow.displayName = "TableBodyRow";

// Table Body Cell Component with variants
export interface TableBodyCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
  /** Position variant for different border styles */
  variant?: 'first' | 'middle' | 'last';
  /** Row position for border management */
  rowPosition?: 'first' | 'middle' | 'last';
}

const TableBodyCell = React.forwardRef<HTMLTableCellElement, TableBodyCellProps>(
  ({ className, children, variant = 'middle', rowPosition = 'middle', ...props }, ref) => {
    const getTopLevelStyles = () => {
      let borderStyles = "";
      let radiusStyles = "";

      // Handle horizontal borders (left/right)
      switch (variant) {
        case 'first':
          borderStyles += " border-l";
          break;
        case 'last':
          borderStyles += " border-r";
          break;
      }

      // Handle vertical borders (top/bottom) - avoid double borders
      switch (rowPosition) {
        case 'first':
          borderStyles += " border-t border-b";
          break;
        case 'middle':
          borderStyles += " border-b"; // Only bottom border to avoid double with row above
          break;
        case 'last':
          borderStyles += " border-b";
          break;
      }

      // Handle corner radius
      if (rowPosition === 'last') {
        if (variant === 'first') radiusStyles = " rounded-bl-[var(--radius-4)]";
        if (variant === 'last') radiusStyles = " rounded-br-[var(--radius-4)]";
      }

      return `py-[var(--spacing-8)] bg-[var(--color-background-primary)] border-[var(--color-stroke)]${borderStyles}${radiusStyles}`;
    };

    const getChildStyles = () => {
      switch (variant) {
        case 'first':
          return "px-[var(--spacing-12)]";
        case 'middle':
          return "border-l border-[var(--color-stroke)] px-[var(--spacing-12)]";
        case 'last':
          return "border-l border-[var(--color-stroke)] px-[var(--spacing-12)]";
        default:
          return "px-[var(--spacing-12)]";
      }
    };

    return (
      <td
        ref={ref}
        className={cn(getTopLevelStyles(), className)}
        {...props}
      >
        <div className={getChildStyles()}>
          <span className="font-medium text-[12px] text-[var(--color-text-primary)] font-sans leading-[0.8]">
            {children}
          </span>
        </div>
      </td>
    );
  }
);
TableBodyCell.displayName = "TableBodyCell";

export {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
};
