import React, { useState } from 'react';

export interface CatalogItem {
  id: string;
  productName: string;
  productDescription: string;
  categoryLabel: 'Product' | 'Service';
  imageSrc?: string;
}

export interface SelectedCatalogItem {
  item: CatalogItem;
  quantity: number;
}

interface CatalogSelectionProps {
  catalogItems: CatalogItem[];
  selectedItems: SelectedCatalogItem[];
  onSelectionChange: (selectedItems: SelectedCatalogItem[]) => void;
  loading?: boolean;
}

export const CatalogSelection: React.FC<CatalogSelectionProps> = ({
  catalogItems,
  selectedItems,
  onSelectionChange,
  loading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');



  // Filter items based on search term
  const filteredItems = catalogItems.filter(item =>
    item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.productDescription.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group items by category
  const products = filteredItems.filter(item => item.categoryLabel === 'Product');
  const services = filteredItems.filter(item => item.categoryLabel === 'Service');

  const handleQuantityChange = (item: CatalogItem, quantity: number) => {
    const updatedSelection = [...selectedItems];
    const existingIndex = updatedSelection.findIndex(selected => selected.item.id === item.id);

    if (quantity > 0) {
      if (existingIndex >= 0 && updatedSelection[existingIndex]) {
        updatedSelection[existingIndex].quantity = quantity;
      } else {
        updatedSelection.push({ item, quantity });
      }
    } else {
      if (existingIndex >= 0) {
        updatedSelection.splice(existingIndex, 1);
      }
    }

    onSelectionChange(updatedSelection);
  };

  const getSelectedQuantity = (itemId: string): number => {
    const selected = selectedItems.find(selected => selected.item.id === itemId);
    return selected ? selected.quantity : 0;
  };

  const renderCatalogCard = (item: CatalogItem) => {
    const selectedQuantity = getSelectedQuantity(item.id);
    const isSelected = selectedQuantity > 0;

    return (
      <div
        key={item.id}
        className={`border rounded-[var(--radius-8)] p-[var(--spacing-12)] transition-all duration-200 ${
          isSelected 
            ? 'border-[var(--color-primary)] bg-[var(--color-primary-background)]' 
            : 'border-[var(--color-stroke)] hover:border-[var(--color-stroke-hover)]'
        }`}
      >
        <div className="flex items-start justify-between mb-[var(--spacing-8)]">
          <div className="flex-1">
            <h4 className="text-[var(--typography-body-size)] font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
              {item.productName}
            </h4>
            <p className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)] mb-[var(--spacing-8)]">
              {item.productDescription}
            </p>
            <span className={`inline-block px-[var(--spacing-8)] py-[var(--spacing-2)] rounded-[var(--radius-4)] text-xs font-medium ${
              item.categoryLabel === 'Product' 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-green-100 text-green-800'
            }`}>
              {item.categoryLabel}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-[var(--spacing-8)]">
            <label className="text-[var(--typography-caption-size)] text-[var(--color-text-secondary)]">
              Quantity:
            </label>
            <input
              type="number"
              min="0"
              value={selectedQuantity}
              onChange={(e) => handleQuantityChange(item, parseInt(e.target.value) || 0)}
              className="w-20 px-[var(--spacing-8)] py-[var(--spacing-4)] border border-[var(--color-stroke)] rounded-[var(--radius-4)] text-[var(--typography-caption-size)] text-center focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent"
              placeholder="0"
            />
          </div>
          
          {isSelected && (
            <div className="text-[var(--typography-caption-size)] text-[var(--color-primary)] font-medium">
              Selected
            </div>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-[var(--spacing-32)]">
        <div className="text-[var(--color-text-secondary)]">Loading catalog items...</div>
      </div>
    );
  }

  return (
    <div className="space-y-[var(--spacing-16)]">
      {/* Search */}
      <div>
        <input
          type="text"
          placeholder="Search products and services..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-[var(--spacing-12)] py-[var(--spacing-8)] border border-[var(--color-stroke)] rounded-[var(--radius-4)] text-[var(--typography-body-size)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent"
        />
      </div>

      {/* Selected Items Summary */}
      {selectedItems.length > 0 && (
        <div className="bg-[var(--color-background-secondary)] rounded-[var(--radius-8)] p-[var(--spacing-12)]">
          <h4 className="text-[var(--typography-body-size)] font-medium text-[var(--color-text-primary)] mb-[var(--spacing-8)]">
            Selected Items ({selectedItems.length})
          </h4>
          <div className="space-y-[var(--spacing-4)]">
            {selectedItems.map(({ item, quantity }) => (
              <div key={item.id} className="flex justify-between items-center text-[var(--typography-caption-size)]">
                <span className="text-[var(--color-text-primary)]">{item.productName}</span>
                <span className="text-[var(--color-text-secondary)]">Qty: {quantity}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Products Section */}
      {products.length > 0 && (
        <div>
          <h3 className="text-[var(--typography-h3-size)] font-[var(--typography-h3-weight)] text-[var(--color-text-primary)] mb-[var(--spacing-12)]">
            Products
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-[var(--spacing-12)]">
            {products.map(renderCatalogCard)}
          </div>
        </div>
      )}

      {/* Services Section */}
      {services.length > 0 && (
        <div>
          <h3 className="text-[var(--typography-h3-size)] font-[var(--typography-h3-weight)] text-[var(--color-text-primary)] mb-[var(--spacing-12)]">
            Services
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-[var(--spacing-12)]">
            {services.map(renderCatalogCard)}
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredItems.length === 0 && (
        <div className="text-center py-[var(--spacing-32)]">
          <div className="text-[var(--color-text-secondary)] mb-[var(--spacing-8)]">
            {searchTerm ? 'No items found matching your search.' : 'No catalog items available.'}
          </div>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="text-[var(--color-primary)] hover:text-[var(--color-primary-hover)] text-[var(--typography-caption-size)]"
            >
              Clear search
            </button>
          )}
        </div>
      )}
    </div>
  );
};
