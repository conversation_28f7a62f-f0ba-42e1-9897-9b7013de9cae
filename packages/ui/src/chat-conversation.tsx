"use client";

import * as React from "react";
import { ChevronLeft, Loader2 } from "lucide-react";
import { cn } from "./lib/utils";
import { ChatBubble } from "./chat-bubble";
import { DataForm, FormField } from "./data-form";
import { FadeIn } from "./fade-in";
import { ScaleIn } from "./scale-in";

export interface ChatMessage {
  id: string;
  message: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'message' | 'form';
}

export interface ChatConversationProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback to close the modal */
  onClose: () => void;
  /** Entity type being created */
  entityType: string;
  /** Chat messages */
  messages: ChatMessage[];
  /** Form fields to display (when showing form) */
  fields?: FormField[];
  /** Whether the form is in loading state */
  loading?: boolean;
  /** Whether the form submission is disabled */
  submitDisabled?: boolean;
  /** Callback when field value changes */
  onFieldChange?: (fieldId: string, value: any) => void;
  /** Callback when form is submitted */
  onSubmit?: () => void;
  /** Whether AI is typing */
  isTyping?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const ChatConversation = React.forwardRef<HTMLDivElement, ChatConversationProps>(
  ({
    className,
    isOpen,
    onClose,
    entityType,
    messages,
    fields,
    loading = false,
    submitDisabled = false,
    onFieldChange,
    onSubmit,
    isTyping = false
  }, ref) => {
    const messagesEndRef = React.useRef<HTMLDivElement>(null);
    const messagesContainerRef = React.useRef<HTMLDivElement>(null);
    const [hasScrolledToBottom, setHasScrolledToBottom] = React.useState(false);

    // Auto-scroll behavior like ChatGPT
    React.useEffect(() => {
      if (!messagesContainerRef.current) return;

      const container = messagesContainerRef.current;
      const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 50;

      // Always scroll to bottom when new messages arrive or when typing
      if (messages.length > 0 || isTyping) {
        // Use a small delay to ensure the content is rendered
        setTimeout(() => {
          messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 50);
      }
    }, [messages, isTyping]);

    // Reset scroll position when modal opens
    React.useEffect(() => {
      if (isOpen && !hasScrolledToBottom) {
        setHasScrolledToBottom(true);
        // Start at top when modal first opens
        if (messagesContainerRef.current && messages.length === 0) {
          messagesContainerRef.current.scrollTop = 0;
        }
      } else if (!isOpen) {
        setHasScrolledToBottom(false);
      }
    }, [isOpen, messages.length, hasScrolledToBottom]);

    // Calculate if form is valid
    const hasErrors = fields?.some(field => field.error) || false;
    const hasRequiredEmpty = fields?.some(field => field.required && (!field.value || field.value === '')) || false;
    const isFormValid = !hasErrors && !hasRequiredEmpty;

    return (
      <FadeIn show={isOpen} duration="normal" easing="smooth" unmountOnExit>
        <ScaleIn
          show={isOpen}
          duration="normal"
          easing="smooth"
          initialScale={0.95}
          className={cn(
            "absolute bg-[var(--color-background-primary)] rounded-[var(--radius-8)]",
            "flex flex-col z-10 transition-modal",
            "top-0 left-0 bottom-0 w-[400px]",
            className
          )}
          style={{
            boxShadow: 'var(--shadow-outer1)',
          }}
        >
          <div ref={ref} className="flex flex-col h-full">
            {/* Header */}
            <div className="flex justify-between items-center p-[var(--spacing-16)] border-b border-[var(--color-stroke)]">
              <div className="space-y-[var(--spacing-4)]">
                <h2 className="text-base font-semibold text-[var(--color-text-primary)]">
                  AI Assistant
                </h2>
                <p className="text-xs text-[var(--color-text-secondary)]">
                  Creating {entityType}
                </p>
              </div>
              <button
                onClick={onClose}
                className="text-[var(--color-text-primary)] hover-scale"
                title="Close"
              >
                <ChevronLeft size={16} />
              </button>
            </div>

            {/* Messages Area */}
            <div
              ref={messagesContainerRef}
              className="flex-1 overflow-y-auto p-[var(--spacing-16)] space-y-[var(--spacing-8)]"
            >
              {messages.map((msg) => (
                <ChatBubble
                  key={msg.id}
                  message={msg.message}
                  isUser={msg.isUser}
                  timestamp={msg.timestamp}
                  showTimestamp={false}
                />
              ))}

              {/* Typing Indicator */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-[var(--color-background-secondary)] px-[var(--spacing-16)] py-[var(--spacing-12)] rounded-[var(--radius-16)] rounded-bl-[var(--spacing-4)] shadow-sm">
                    <div className="flex items-center space-x-[var(--spacing-4)]">
                      <Loader2 size={12} className="animate-spin text-[var(--color-text-secondary)]" />
                      <span className="text-xs text-[var(--color-text-secondary)]">AI is typing...</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Form Section (when ready to show form) */}
              {fields && fields.length > 0 && (
                <div className="mt-[var(--spacing-16)] p-[var(--spacing-16)] bg-[var(--color-background-secondary)] rounded-[var(--radius-8)] border border-[var(--color-stroke)]">
                  
                  <DataForm
                    fields={fields}
                    entityType={entityType}
                    onFieldChange={onFieldChange || (() => {})}
                    loading={loading}
                    submitDisabled={submitDisabled || !isFormValid}
                    onSubmit={onSubmit || (() => {})}
                    submitText={`Create ${entityType}`}
                  />
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </div>
        </ScaleIn>
      </FadeIn>
    );
  }
);

ChatConversation.displayName = "ChatConversation";

export { ChatConversation };
