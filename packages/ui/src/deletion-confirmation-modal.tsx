"use client";

import * as React from "react";
import { <PERSON>ev<PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>riangle, Trash2 } from "lucide-react";
import { cn } from "./lib/utils";
import { FadeIn } from "./fade-in";
import { ScaleIn } from "./scale-in";

export interface DeletionConfirmationModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback to close the modal */
  onClose: () => void;
  /** Entity type being deleted */
  entityType: string;
  /** Entity name/identifier */
  entityName: string;
  /** Warnings about the deletion */
  warnings?: string[];
  /** Errors preventing deletion */
  errors?: string[];
  /** Whether the deletion is in progress */
  loading?: boolean;
  /** Whether the deletion is disabled */
  deleteDisabled?: boolean;
  /** Callback when deletion is confirmed */
  onConfirm: () => void;
  /** Additional deletion options */
  deletionOptions?: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
}

const DeletionConfirmationModal = React.forwardRef<HTMLDivElement, DeletionConfirmationModalProps>(
  ({
    className,
    isOpen,
    onClose,
    entityType,
    entityName,
    warnings = [],
    errors = [],
    loading = false,
    deleteDisabled = false,
    onConfirm,
    deletionOptions,
    ...props
  }, ref) => {
    const hasErrors = errors.length > 0;
    const hasWarnings = warnings.length > 0;
    const canDelete = !hasErrors && !deleteDisabled;

    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <FadeIn show={isOpen} duration="fast" easing="smooth">
          <div 
            className="absolute inset-0 bg-black/20 backdrop-blur-sm"
            onClick={onClose}
          />
        </FadeIn>

        {/* Modal */}
        <ScaleIn
          show={isOpen}
          duration="fast"
          easing="smooth"
          initialScale={0.95}
          className={cn(
            "relative bg-[var(--color-background-primary)] rounded-[var(--radius-8)] p-[var(--spacing-16)] w-full max-w-md mx-4 flex flex-col",
            "border border-[var(--color-stroke)]",
            className
          )}
          style={{ boxShadow: 'var(--shadow-outer1)' }}
          {...props}
          ref={ref}
        >
          {/* Header */}
          <div className="flex justify-between items-start mb-[var(--spacing-16)]">
            <div className="flex items-center space-x-[var(--spacing-8)]">
              <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                <Trash2 size={16} className="text-red-600" />
              </div>
              <div>
                <h2 className="text-base font-semibold text-[var(--color-text-primary)]">
                  Delete {entityType}
                </h2>
                <p className="text-sm text-[var(--color-text-secondary)]">
                  "{entityName}"
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-[var(--color-text-primary)] hover-scale"
              title="Close"
            >
              <ChevronLeft size={16} />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 space-y-[var(--spacing-16)]">
            {/* Confirmation Message */}
            <p className="text-sm text-[var(--color-text-primary)]">
              Are you sure you want to delete this {entityType.toLowerCase()}? This action cannot be undone.
            </p>

            {/* Errors */}
            {hasErrors && (
              <div className="p-[var(--spacing-12)] bg-red-50 border border-red-200 rounded-[var(--radius-4)]">
                <div className="flex items-start space-x-[var(--spacing-8)]">
                  <AlertTriangle size={16} className="text-red-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-red-800 mb-[var(--spacing-4)]">
                      Cannot Delete
                    </h4>
                    <ul className="text-sm text-red-700 space-y-[var(--spacing-2)]">
                      {errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Warnings */}
            {hasWarnings && !hasErrors && (
              <div className="p-[var(--spacing-12)] bg-yellow-50 border border-yellow-200 rounded-[var(--radius-4)]">
                <div className="flex items-start space-x-[var(--spacing-8)]">
                  <AlertTriangle size={16} className="text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-800 mb-[var(--spacing-4)]">
                      Warning
                    </h4>
                    <ul className="text-sm text-yellow-700 space-y-[var(--spacing-2)]">
                      {warnings.map((warning, index) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Deletion Options */}
            {deletionOptions && canDelete && (
              <div className="space-y-[var(--spacing-12)]">
                <h4 className="text-sm font-medium text-[var(--color-text-primary)]">
                  Deletion Options
                </h4>
                {deletionOptions}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-[var(--spacing-8)] mt-[var(--spacing-16)] pt-[var(--spacing-16)] border-t border-[var(--color-stroke)]">
            <button
              onClick={onClose}
              className="px-[var(--spacing-16)] py-[var(--spacing-8)] text-sm font-medium text-[var(--color-text-secondary)] hover:text-[var(--color-text-primary)] hover-scale"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              disabled={!canDelete || loading}
              className={cn(
                "px-[var(--spacing-16)] py-[var(--spacing-8)] text-sm font-medium rounded-[var(--radius-4)] hover-scale",
                canDelete && !loading
                  ? "bg-red-600 text-white hover:bg-red-700"
                  : "bg-[var(--color-background-secondary)] text-[var(--color-text-disabled)] cursor-not-allowed"
              )}
            >
              {loading ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </ScaleIn>
      </div>
    );
  }
);

DeletionConfirmationModal.displayName = "DeletionConfirmationModal";

export { DeletionConfirmationModal };
