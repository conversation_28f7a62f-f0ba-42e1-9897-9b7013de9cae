import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Document } from './document';
import { FileText, Download, Eye } from 'lucide-react';

const meta: Meta<typeof Document> = {
  title: 'UI/Document',
  component: Document,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Document name/title',
    },
    onClick: {
      action: 'document-clicked',
      description: 'Callback when document is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'Especificaciones técnicas - Lámina Acero Inoxidable.pdf',
  },
};

export const ShortName: Story = {
  args: {
    name: 'Manual.pdf',
  },
};

export const LongName: Story = {
  args: {
    name: 'Chapa desplegada - Lateral hecho de Payeras 350 - Especificaciones completas y detalladas.pdf',
  },
};

export const WithCustomIcon: Story = {
  args: {
    name: 'Download Manual.pdf',
    icon: <Download className="w-3 h-3 text-[var(--color-icon-secondary)]" />,
  },
};

export const Clickable: Story = {
  args: {
    name: 'Planos de fabricación - Silla 467.pdf',
    onClick: () => console.log('Document clicked'),
  },
};

export const DocumentList: Story = {
  render: () => (
    <div className="space-y-2 w-80">
      <Document 
        name="Especificaciones técnicas - Lámina Acero.pdf"
        onClick={() => console.log('Specs clicked')}
      />
      <Document 
        name="Planos de fabricación - Silla 467.pdf"
        onClick={() => console.log('Plans clicked')}
      />
      <Document
        name="Manual de instalación.pdf"
        icon={<Eye className="w-3 h-3 text-[var(--color-icon-secondary)]" />}
        onClick={() => console.log('Manual clicked')}
      />
      <Document 
        name="Certificado de calidad.pdf"
        onClick={() => console.log('Certificate clicked')}
      />
    </div>
  ),
};
