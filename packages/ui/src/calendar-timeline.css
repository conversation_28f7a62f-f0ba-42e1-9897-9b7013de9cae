/* Calendar Timeline CSS - Grid-based layout */
.calendar-timeline {
  /* Ensure proper isolation and layout */
  isolation: isolate;
  contain: layout style;
}

/* Ensure grid containers work properly */
.calendar-timeline .day-header,
.calendar-timeline .task-row {
  /* Force proper grid behavior */
  display: grid !important;
}

.calendar-timeline .task-container {
  /* Task containers with proper borders and hover */
  border: 1px solid var(--color-stroke);
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.calendar-timeline .task-container:hover {
  /* Enhanced hover state */
  z-index: 3;
  border-color: var(--color-text-primary);
}

.calendar-timeline .avatar-container {
  /* Avatar containers with white borders */
  border: 1px solid white;
}

/* Ensure proper stacking for task rows */
.calendar-timeline .task-row {
  position: relative;
  z-index: 1;
}

.calendar-timeline .task-row:hover {
  z-index: 2;
}
