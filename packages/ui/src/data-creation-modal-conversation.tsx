"use client";

import * as React from "react";
import { Loader2 } from "lucide-react";
import { ChatBubble } from "./chat-bubble";
import { StreamingChatBubble } from "./streaming-chat-bubble";
import { ConversationMessage } from "./guided-conversation";

interface DataCreationModalConversationProps {
  /** Original command that started this creation */
  originalCommand?: string;
  /** AI conversation response message */
  aiMessage?: string;
  /** Conversation history for displaying all messages */
  conversationHistory?: ConversationMessage[];
  /** Whether AI is currently typing/generating response */
  aiIsTyping?: boolean;
  /** Whether to stream the AI response */
  aiIsStreaming?: boolean;
  /** Callback when AI streaming is complete */
  onAiStreamingComplete?: () => void;
  /** Unknown category notification message */
  unknownCategoryMessage?: string;
}

export const DataCreationModalConversation: React.FC<DataCreationModalConversationProps> = ({
  originalCommand,
  aiMessage,
  conversationHistory = [],
  aiIsTyping,
  aiIsStreaming,
  onAiStreamingComplete,
  unknownCategoryMessage,
}) => {
  return (
    <div className="space-y-[var(--spacing-16)]">
      {/* Unknown Category Notification */}
      {unknownCategoryMessage && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-[var(--radius-4)] p-[var(--spacing-12)]">
          <div className="flex items-start space-x-[var(--spacing-8)]">
            <div className="text-yellow-600 text-sm">⚠️</div>
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">Category Not Found</p>
              <p>{unknownCategoryMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* AI Conversation Messages */}
      {(conversationHistory.length > 0 || originalCommand || aiMessage) && (
        <div className="space-y-[var(--spacing-8)]">
          {/* Conversation History */}
          {conversationHistory.map((message, index) => {
            if (message.role === 'user') {
              return (
                <div key={index} className="flex justify-end">
                  <ChatBubble
                    message={message.content}
                    isUser={true}
                    timestamp={message.timestamp}
                  />
                </div>
              );
            } else {
              return (
                <div key={index} className="flex justify-start">
                  <ChatBubble
                    message={message.content}
                    isUser={false}
                    timestamp={message.timestamp}
                  />
                </div>
              );
            }
          })}

          {/* Fallback: User's original command (if no conversation history) */}
          {conversationHistory.length === 0 && originalCommand && (
            <div className="flex justify-end">
              <ChatBubble
                message={originalCommand}
                isUser={true}
              />
            </div>
          )}

          {/* AI Typing Indicator */}
          {aiIsTyping && (
            <div className="flex justify-start">
              <div className="bg-[var(--color-background-secondary)] rounded-[var(--radius-8)] p-[var(--spacing-12)] max-w-[80%]">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin text-[var(--color-text-secondary)]" />
                  <span className="text-sm text-[var(--color-text-secondary)]">AI is thinking...</span>
                </div>
              </div>
            </div>
          )}

          {/* New AI Response (when not in history yet) */}
          {aiMessage && !aiIsStreaming && !conversationHistory.some(msg => msg.content === aiMessage && msg.role === 'ai') && (
            <div className="flex justify-start">
              <ChatBubble
                message={aiMessage}
                isUser={false}
              />
            </div>
          )}

          {/* Streaming AI Response */}
          {aiIsStreaming && aiMessage && (
            <div className="flex justify-start">
              <StreamingChatBubble
                message={aiMessage}
                onStreamingComplete={onAiStreamingComplete}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};
