"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface TaskFormData {
  name: string;
  description?: string;
  startDate: number; // Day of month (1-31)
  endDate: number; // Day of month (1-31)
  assignedTo: string[]; // Team member IDs
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  estimatedHours?: number;
  tags?: string[];
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
}

export interface TaskFormProps {
  initialData?: Partial<TaskFormData>;
  teamMembers: TeamMember[];
  onSubmit: (data: TaskFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
  className?: string;
}

export function TaskForm({
  initialData,
  teamMembers,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = "Create Task",
  className
}: TaskFormProps) {
  const [formData, setFormData] = React.useState<TaskFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    startDate: initialData?.startDate || 1,
    endDate: initialData?.endDate || 1,
    assignedTo: initialData?.assignedTo || [],
    priority: initialData?.priority || 'medium',
    estimatedHours: initialData?.estimatedHours || undefined,
    tags: initialData?.tags || [],
  });

  const [newTag, setNewTag] = React.useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;
    
    onSubmit(formData);
  };

  const handleTeamMemberToggle = (memberId: string) => {
    setFormData(prev => ({
      ...prev,
      assignedTo: prev.assignedTo.includes(memberId)
        ? prev.assignedTo.filter(id => id !== memberId)
        : [...prev.assignedTo, memberId]
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-6", className)}>
      {/* Task Name */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Task Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter task name"
          required
        />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Description
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter task description"
          rows={3}
        />
      </div>

      {/* Date Range */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">
            Start Date (Day) *
          </label>
          <input
            type="number"
            min="1"
            max="31"
            value={formData.startDate}
            onChange={(e) => setFormData(prev => ({ ...prev, startDate: parseInt(e.target.value) || 1 }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">
            End Date (Day) *
          </label>
          <input
            type="number"
            min="1"
            max="31"
            value={formData.endDate}
            onChange={(e) => setFormData(prev => ({ ...prev, endDate: parseInt(e.target.value) || 1 }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>
      </div>

      {/* Priority */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Priority
        </label>
        <select
          value={formData.priority}
          onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as TaskFormData['priority'] }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
          <option value="urgent">Urgent</option>
        </select>
      </div>

      {/* Estimated Hours */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Estimated Hours
        </label>
        <input
          type="number"
          min="0"
          step="0.5"
          value={formData.estimatedHours || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, estimatedHours: e.target.value ? parseFloat(e.target.value) : undefined }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter estimated hours"
        />
      </div>

      {/* Team Member Assignment */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Assign Team Members
        </label>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {teamMembers.map((member) => (
            <label key={member.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer">
              <input
                type="checkbox"
                checked={formData.assignedTo.includes(member.id)}
                onChange={() => handleTeamMemberToggle(member.id)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <div className="flex items-center space-x-2">
                {member.avatar && (
                  <img src={member.avatar} alt={member.name} className="w-6 h-6 rounded-full" />
                )}
                <div>
                  <div className="text-sm font-medium text-gray-900">{member.name}</div>
                  <div className="text-xs text-gray-500">{member.role}</div>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Tags */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Tags
        </label>
        <div className="flex flex-wrap gap-2 mb-2">
          {formData.tags?.map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {tag}
              <button
                type="button"
                onClick={() => handleRemoveTag(tag)}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
        <div className="flex space-x-2">
          <input
            type="text"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Add a tag"
          />
          <button
            type="button"
            onClick={handleAddTag}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
          >
            Add
          </button>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading || !formData.name.trim()}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Creating...' : submitLabel}
        </button>
      </div>
    </form>
  );
}
