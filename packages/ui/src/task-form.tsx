"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface TaskFormData {
  name: string; // Task concept/description
  startDate?: string; // Optional start date (ISO string)
  endDate?: string; // Optional end date (ISO string)
  assignedToId?: string; // Single team member ID (optional)
  projectId?: string; // Optional project assignment
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
}

export interface Project {
  id: string;
  name: string;
}

export interface TaskFormProps {
  initialData?: Partial<TaskFormData>;
  teamMembers: TeamMember[];
  projects?: Project[]; // Optional projects list
  onSubmit: (data: TaskFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
  className?: string;
}

export function TaskForm({
  initialData,
  teamMembers,
  projects = [],
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = "Create Task",
  className
}: TaskFormProps) {
  const [formData, setFormData] = React.useState<TaskFormData>({
    name: initialData?.name || '',
    startDate: initialData?.startDate || '',
    endDate: initialData?.endDate || '',
    assignedToId: initialData?.assignedToId || '',
    projectId: initialData?.projectId || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;

    // Clean up empty strings to undefined
    const cleanData = {
      ...formData,
      startDate: formData.startDate || undefined,
      endDate: formData.endDate || undefined,
      assignedToId: formData.assignedToId || undefined,
      projectId: formData.projectId || undefined,
    };

    onSubmit(cleanData);
  };

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-4", className)}>
      {/* Task Concept/Description */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Task Concept *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter task concept or description"
          required
        />
      </div>

      {/* Date Range */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">
            Start Date
          </label>
          <input
            type="date"
            value={formData.startDate}
            onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">
            End Date
          </label>
          <input
            type="date"
            value={formData.endDate}
            onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Team Member Assignment */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900">
          Assign Team Member
        </label>
        <select
          value={formData.assignedToId}
          onChange={(e) => setFormData(prev => ({ ...prev, assignedToId: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Select team member (optional)</option>
          {teamMembers.map((member) => (
            <option key={member.id} value={member.id}>
              {member.name} - {member.role}
            </option>
          ))}
        </select>
      </div>

      {/* Project Assignment */}
      {projects.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">
            Assign to Project
          </label>
          <select
            value={formData.projectId}
            onChange={(e) => setFormData(prev => ({ ...prev, projectId: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select project (optional)</option>
            {projects.map((project) => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading || !formData.name.trim()}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Creating...' : submitLabel}
        </button>
      </div>
    </form>
  );
}
