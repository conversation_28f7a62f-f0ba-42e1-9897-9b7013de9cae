"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface EventCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** The event text to display */
  text?: string;
  /** State of the event card */
  state?: 'default' | 'active';
}

const EventCard = React.forwardRef<HTMLDivElement, EventCardProps>(
  ({
    className,
    text = "Event",
    state = 'default',
    children,
    ...props
  }, ref) => {
    // Component-specific variables from global design tokens
    const getBackgroundColor = () => {
      switch (state) {
        case 'active':
          return 'var(--color-background-primary)';
        default:
          return 'var(--color-frame-primary)';
      }
    };

    const getTextColor = () => {
      switch (state) {
        case 'active':
          return 'var(--color-text-primary)';
        default:
          return 'var(--color-text-inverse)';
      }
    };

    const getBorderColor = () => {
      switch (state) {
        case 'active':
          return 'var(--color-stroke)';
        default:
          return 'transparent';
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "py-1 px-2 rounded-[var(--radius-4)] font-inter font-semibold text-[10px] border leading-tight",
          className
        )}
        style={{
          backgroundColor: getBackgroundColor(),
          color: getTextColor(),
          borderColor: getBorderColor(),
        }}
        {...props}
      >
        {children || text}
      </div>
    );
  }
);

EventCard.displayName = "EventCard";

export { EventCard };
