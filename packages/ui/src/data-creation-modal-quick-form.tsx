"use client";

import * as React from "react";
import { FormField } from "./data-form";
import { ConversationMessage } from "./guided-conversation";

interface DataCreationModalQuickFormProps {
  /** Form fields to display */
  fields: FormField[];
  /** AI conversation response message */
  aiMessage?: string;
  /** Whether to stream the AI response */
  aiIsStreaming?: boolean;
  /** Whether AI is currently typing/generating response */
  aiIsTyping?: boolean;
  /** Whether AI is waiting for more information */
  aiNeedsMoreInfo?: boolean;
  /** Whether to show the form or keep in conversation mode */
  showForm?: boolean;
  /** Whether this is a guided conversation flow */
  isGuidedFlow?: boolean;
  /** Conversation history for displaying all messages */
  conversationHistory?: ConversationMessage[];
  /** Callback for quick form submission */
  onQuickFormSubmit?: (message: string) => void;
}

export const DataCreationModalQuickForm: React.FC<DataCreationModalQuickFormProps> = ({
  fields,
  aiMessage,
  aiIsStreaming,
  aiIsTyping,
  aiNeedsMoreInfo,
  showForm,
  isGuidedFlow,
  conversationHistory = [],
  onQuickFormSubmit,
}) => {
  const [quickFormData, setQuickFormData] = React.useState<Record<string, string>>({});

  // Helper function to get relevant fields based on AI message
  const getRelevantFields = () => {
    if (!aiMessage) return [];
    
    // Get all previous conversation content to check what's already been provided
    const conversationText = conversationHistory
      .map((msg: any) => msg.content.toLowerCase())
      .join(' ');

    // Helper function to check if information was already provided
    const wasAlreadyProvided = (fieldId: string, fieldLabel: string) => {
      const variations = [
        fieldId.toLowerCase(),
        fieldLabel.toLowerCase(),
        fieldLabel.toLowerCase().replace(/\s+/g, ''),
      ];
      
      return variations.some(variation => 
        conversationText.includes(variation) || 
        conversationText.includes(variation.replace(/[^a-z0-9]/g, ''))
      );
    };

    // Filter fields that AI is asking about and haven't been provided yet
    return fields.filter(field => {
      const fieldMentioned = aiMessage.toLowerCase().includes(field.id.toLowerCase()) ||
                           aiMessage.toLowerCase().includes(field.label.toLowerCase());
      const alreadyProvided = wasAlreadyProvided(field.id, field.label);
      
      return fieldMentioned && !alreadyProvided;
    });
  };

  const relevantFields = getRelevantFields();

  const handleQuickFormSubmit = () => {
    if (!onQuickFormSubmit) return;
    
    const responses = Object.entries(quickFormData)
      .filter(([_, value]) => value.trim() !== '')
      .map(([fieldId, value]) => {
        const field = relevantFields.find(f => f.id === fieldId);
        return `${field?.label || fieldId}: ${value}`;
      })
      .join(', ');
    
    if (responses) {
      onQuickFormSubmit(responses);
    }
    
    // Reset form
    setQuickFormData({});
  };

  // Don't show if conditions aren't met
  if (isGuidedFlow || showForm || !aiMessage || aiIsStreaming || aiIsTyping || !aiNeedsMoreInfo || relevantFields.length === 0) {
    return null;
  }

  return (
    <div className="bg-[var(--color-background-secondary)] rounded-[var(--radius-8)] p-[var(--spacing-16)] space-y-[var(--spacing-12)]">
      <h4 className="text-sm font-medium text-[var(--color-text-primary)]">
        Please provide the following information:
      </h4>
      
      {/* Dynamic quick form fields based on AI message */}
      <div className="space-y-[var(--spacing-12)]">
        {relevantFields.map((field) => (
          <div key={field.id} className="space-y-[var(--spacing-4)]">
            <label className="block text-sm font-medium text-[var(--color-text-primary)]">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type={field.type === 'number' ? 'number' : 'text'}
              value={quickFormData[field.id] || ''}
              onChange={(e) => setQuickFormData(prev => ({
                ...prev,
                [field.id]: e.target.value
              }))}
              placeholder={field.placeholder}
              className="w-full px-[var(--spacing-12)] py-[var(--spacing-8)] border border-[var(--color-stroke)] rounded-[var(--radius-4)] text-[var(--typography-body-size)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent"
            />
          </div>
        ))}
      </div>
      
      <button
        onClick={handleQuickFormSubmit}
        disabled={Object.values(quickFormData).every(value => !value || value.trim() === '')}
        className="w-full bg-[var(--color-primary)] text-white py-[var(--spacing-8)] px-[var(--spacing-16)] rounded-[var(--radius-4)] text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Continue with these details
      </button>
    </div>
  );
};
