"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface SlideInProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Whether the component should be visible */
  show?: boolean;
  /** Direction of the slide animation */
  direction?: 'up' | 'down' | 'left' | 'right';
  /** Distance to slide (in pixels) */
  distance?: number;
  /** Duration of the slide transition */
  duration?: 'fast' | 'normal' | 'slow';
  /** Delay before the transition starts */
  delay?: 'none' | 'short' | 'medium' | 'long';
  /** Custom transition timing function */
  easing?: 'ease' | 'easeIn' | 'easeOut' | 'easeInOut' | 'smooth';
  /** Whether to unmount the component when hidden */
  unmountOnExit?: boolean;
  /** Callback fired when transition completes */
  onTransitionEnd?: () => void;
}

const SlideIn = React.forwardRef<HTMLDivElement, SlideInProps>(
  ({
    className,
    show = true,
    direction = 'up',
    distance = 20,
    duration = 'normal',
    delay = 'none',
    easing = 'smooth',
    unmountOnExit = false,
    onTransitionEnd,
    children,
    style,
    ...props
  }, ref) => {
    const [shouldRender, setShouldRender] = React.useState(show || !unmountOnExit);
    const [isVisible, setIsVisible] = React.useState(show);

    React.useEffect(() => {
      if (show) {
        setShouldRender(true);
        // Small delay to ensure the element is rendered before making it visible
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
      } else {
        setIsVisible(false);
        if (unmountOnExit) {
          // Wait for transition to complete before unmounting
          const durationMs = duration === 'fast' ? 150 : duration === 'slow' ? 350 : 250;
          const timer = setTimeout(() => setShouldRender(false), durationMs);
          return () => clearTimeout(timer);
        }
      }
    }, [show, unmountOnExit, duration]);

    const handleTransitionEnd = React.useCallback((e: React.TransitionEvent) => {
      if (e.propertyName === 'transform') {
        onTransitionEnd?.();
      }
    }, [onTransitionEnd]);

    if (!shouldRender) {
      return null;
    }

    // Calculate transform values based on direction and distance
    const getTransform = (visible: boolean) => {
      if (visible) return 'translate3d(0, 0, 0)';
      
      switch (direction) {
        case 'up':
          return `translate3d(0, ${distance}px, 0)`;
        case 'down':
          return `translate3d(0, -${distance}px, 0)`;
        case 'left':
          return `translate3d(${distance}px, 0, 0)`;
        case 'right':
          return `translate3d(-${distance}px, 0, 0)`;
        default:
          return `translate3d(0, ${distance}px, 0)`;
      }
    };

    const durationValue = `var(--transition-duration-${duration})`;
    const delayValue = `var(--transition-delay-${delay})`;
    const easingValue = `var(--transition-easing-${easing})`;

    return (
      <div
        ref={ref}
        className={cn(
          "transition-transform",
          className
        )}
        style={{
          transform: getTransform(isVisible),
          transitionDuration: durationValue,
          transitionDelay: delayValue,
          transitionTimingFunction: easingValue,
          ...style,
        }}
        onTransitionEnd={handleTransitionEnd}
        {...props}
      >
        {children}
      </div>
    );
  }
);

SlideIn.displayName = "SlideIn";

export { SlideIn };
