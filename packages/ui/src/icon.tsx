"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface IconProps extends React.HTMLAttributes<HTMLSpanElement> {
  /** The Material Symbol name (e.g., 'expand_more', 'arrow_forward') */
  name: string;
  /** Icon size in pixels */
  size?: number;
  /** Icon weight (100-700) */
  weight?: number;
  /** Icon fill (0 or 1) */
  fill?: number;
  /** Icon grade (-25 to 200) */
  grade?: number;
  /** Icon optical size (20-48) */
  opticalSize?: number;
}

const Icon = React.forwardRef<HTMLSpanElement, IconProps>(
  ({ 
    className, 
    name, 
    size = 24, 
    weight = 400, 
    fill = 0, 
    grade = 0, 
    opticalSize = 24,
    style,
    ...props 
  }, ref) => {
    const iconStyle = {
      fontSize: `${size}px`,
      fontVariationSettings: `'FILL' ${fill}, 'wght' ${weight}, 'GRAD' ${grade}, 'opsz' ${opticalSize}`,
      ...style
    };

    return (
      <span
        ref={ref}
        className={cn("material-symbols-outlined", className)}
        style={iconStyle}
        {...props}
      >
        {name}
      </span>
    );
  }
);

Icon.displayName = "Icon";

export { Icon };
