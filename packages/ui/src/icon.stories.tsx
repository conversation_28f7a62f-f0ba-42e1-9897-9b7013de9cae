import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Icon } from './icon';
import { 
  ExpandIcon, 
  CollapseIcon, 
  ArrowForwardIcon, 
  ArrowBackIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  SearchIcon,
  FilterIcon,
  CloseIcon,
  MenuIcon,
  MoreVertIcon,
  AddIcon,
  EditIcon,
  DeleteIcon,
  CheckIcon,
  WarningIcon,
  InfoIcon,
  DownloadIcon,
  UploadIcon
} from './icons';

const meta: Meta<typeof Icon> = {
  title: 'UI/Icon',
  component: Icon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'The Material Symbol name',
    },
    size: {
      control: { type: 'range', min: 12, max: 48, step: 1 },
      description: 'Icon size in pixels',
    },
    weight: {
      control: { type: 'range', min: 100, max: 700, step: 100 },
      description: 'Icon weight (100-700)',
    },
    fill: {
      control: { type: 'range', min: 0, max: 1, step: 1 },
      description: 'Icon fill (0 or 1)',
    },
    grade: {
      control: { type: 'range', min: -25, max: 200, step: 25 },
      description: 'Icon grade (-25 to 200)',
    },
    opticalSize: {
      control: { type: 'range', min: 20, max: 48, step: 1 },
      description: 'Icon optical size (20-48)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'home',
    size: 24,
  },
};

export const Large: Story = {
  args: {
    name: 'star',
    size: 48,
  },
};

export const Filled: Story = {
  args: {
    name: 'favorite',
    size: 32,
    fill: 1,
  },
};

export const Bold: Story = {
  args: {
    name: 'settings',
    size: 32,
    weight: 700,
  },
};

export const AllCommonIcons: Story = {
  render: () => (
    <div className="grid grid-cols-6 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <ExpandIcon size={32} />
        <span className="text-xs">Fullscreen</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <CollapseIcon size={32} />
        <span className="text-xs">Exit Fullscreen</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ArrowForwardIcon size={32} />
        <span className="text-xs">Arrow Forward</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ArrowBackIcon size={32} />
        <span className="text-xs">Arrow Back</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ChevronRightIcon size={32} />
        <span className="text-xs">Chevron Right</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ChevronLeftIcon size={32} />
        <span className="text-xs">Chevron Left</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SearchIcon size={32} />
        <span className="text-xs">Search</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <FilterIcon size={32} />
        <span className="text-xs">Filter</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <CloseIcon size={32} />
        <span className="text-xs">Close</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <MenuIcon size={32} />
        <span className="text-xs">Menu</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <MoreVertIcon size={32} />
        <span className="text-xs">More Vert</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <AddIcon size={32} />
        <span className="text-xs">Add</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <EditIcon size={32} />
        <span className="text-xs">Edit</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <DeleteIcon size={32} />
        <span className="text-xs">Delete</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <CheckIcon size={32} />
        <span className="text-xs">Check</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <WarningIcon size={32} />
        <span className="text-xs">Warning</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <InfoIcon size={32} />
        <span className="text-xs">Info</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <DownloadIcon size={32} />
        <span className="text-xs">Download</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <UploadIcon size={32} />
        <span className="text-xs">Upload</span>
      </div>
    </div>
  ),
};
