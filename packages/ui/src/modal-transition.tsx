"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface ModalTransitionProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Whether the modal is expanded */
  isExpanded?: boolean;
  /** Duration of the expansion transition */
  duration?: 'fast' | 'normal' | 'slow';
  /** Custom transition timing function */
  easing?: 'ease' | 'easeIn' | 'easeOut' | 'easeInOut' | 'smooth';
  /** Callback fired when transition completes */
  onTransitionEnd?: () => void;
}

const ModalTransition = React.forwardRef<HTMLDivElement, ModalTransitionProps>(
  ({
    className,
    isExpanded = false,
    duration = 'normal',
    easing = 'smooth',
    onTransitionEnd,
    children,
    style,
    ...props
  }, ref) => {
    const [isAnimating, setIsAnimating] = React.useState(false);
    const [prevExpanded, setPrevExpanded] = React.useState(isExpanded);

    React.useEffect(() => {
      if (prevExpanded !== isExpanded) {
        setIsAnimating(true);
        setPrevExpanded(isExpanded);
        
        // Reset animation state after transition completes
        const durationMs = duration === 'fast' ? 150 : duration === 'slow' ? 350 : 250;
        const timer = setTimeout(() => {
          setIsAnimating(false);
        }, durationMs);
        
        return () => clearTimeout(timer);
      }
    }, [isExpanded, prevExpanded, duration]);

    const handleTransitionEnd = React.useCallback((e: React.TransitionEvent) => {
      if (e.propertyName === 'width' || e.propertyName === 'height') {
        setIsAnimating(false);
        onTransitionEnd?.();
      }
    }, [onTransitionEnd]);

    const durationValue = `var(--transition-duration-${duration})`;
    const easingValue = `var(--transition-easing-${easing})`;

    return (
      <div
        ref={ref}
        className={cn(
          "transition-all",
          isAnimating && "transition-modal",
          className
        )}
        style={{
          transitionDuration: durationValue,
          transitionTimingFunction: easingValue,
          ...style,
        }}
        onTransitionEnd={handleTransitionEnd}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ModalTransition.displayName = "ModalTransition";

export { ModalTransition };
