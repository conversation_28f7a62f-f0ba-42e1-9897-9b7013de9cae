import type { Meta, StoryObj } from '@storybook/react';
import { LabelValue } from './label-value';

const meta: Meta<typeof LabelValue> = {
  title: 'Components/LabelValue',
  component: LabelValue,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'The label text (will be automatically uppercased)',
    },
    value: {
      control: 'text',
      description: 'The value text to display below the label',
    },
    labelClassName: {
      control: 'text',
      description: 'Optional custom styling for the label',
    },
    valueClassName: {
      control: 'text',
      description: 'Optional custom styling for the value',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Cliente',
    value: 'Tommy Hilfiger Corp.',
  },
};

export const WithNumber: Story = {
  args: {
    label: 'Presupuesto',
    value: 250000,
  },
};

export const LongText: Story = {
  args: {
    label: 'Descripción del proyecto',
    value: 'Construcción completa de tienda retail con acabados premium y sistema de iluminación LED',
  },
};

export const CustomStyling: Story = {
  args: {
    label: 'Estado',
    value: 'En progreso',
    labelClassName: 'text-blue-600',
    valueClassName: 'text-green-600 font-bold',
  },
};

export const Multiple: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-6">
      <LabelValue label="Cliente" value="Tommy Hilfiger Corp." />
      <LabelValue label="Contacto" value="María González" />
      <LabelValue label="Fecha inicio" value="15 Jun, 2024" />
      <LabelValue label="Fecha entrega" value="30 Jul, 2024" />
      <LabelValue label="Presupuesto" value={250000} />
      <LabelValue label="Estado" value="En progreso" />
    </div>
  ),
};
