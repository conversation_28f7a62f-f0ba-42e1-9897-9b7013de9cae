"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface FadeInProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Whether the component should be visible */
  show?: boolean;
  /** Duration of the fade transition */
  duration?: 'fast' | 'normal' | 'slow';
  /** Delay before the transition starts */
  delay?: 'none' | 'short' | 'medium' | 'long';
  /** Custom transition timing function */
  easing?: 'ease' | 'easeIn' | 'easeOut' | 'easeInOut' | 'smooth';
  /** Whether to unmount the component when hidden */
  unmountOnExit?: boolean;
  /** Callback fired when transition completes */
  onTransitionEnd?: () => void;
}

const FadeIn = React.forwardRef<HTMLDivElement, FadeInProps>(
  ({
    className,
    show = true,
    duration = 'normal',
    delay = 'none',
    easing = 'smooth',
    unmountOnExit = false,
    onTransitionEnd,
    children,
    style,
    ...props
  }, ref) => {
    const [shouldRender, setShouldRender] = React.useState(show || !unmountOnExit);
    const [isVisible, setIsVisible] = React.useState(show);

    React.useEffect(() => {
      if (show) {
        setShouldRender(true);
        // Small delay to ensure the element is rendered before making it visible
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
      } else {
        setIsVisible(false);
        if (unmountOnExit) {
          // Wait for transition to complete before unmounting
          const durationMs = duration === 'fast' ? 150 : duration === 'slow' ? 350 : 250;
          const timer = setTimeout(() => setShouldRender(false), durationMs);
          return () => clearTimeout(timer);
        }
      }
    }, [show, unmountOnExit, duration]);

    const handleTransitionEnd = React.useCallback((e: React.TransitionEvent) => {
      if (e.propertyName === 'opacity') {
        onTransitionEnd?.();
      }
    }, [onTransitionEnd]);

    if (!shouldRender) {
      return null;
    }

    const durationValue = `var(--transition-duration-${duration})`;
    const delayValue = `var(--transition-delay-${delay})`;
    const easingValue = `var(--transition-easing-${easing})`;

    return (
      <div
        ref={ref}
        className={cn(
          "transition-opacity",
          className
        )}
        style={{
          opacity: isVisible ? 1 : 0,
          transitionDuration: durationValue,
          transitionDelay: delayValue,
          transitionTimingFunction: easingValue,
          ...style,
        }}
        onTransitionEnd={handleTransitionEnd}
        {...props}
      >
        {children}
      </div>
    );
  }
);

FadeIn.displayName = "FadeIn";

export { FadeIn };
