{"name": "@admin/ui", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./styles": "./dist/styles.css"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@admin/eslint-config": "*", "@admin/typescript-config": "*", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-interactions": "^8.0.0", "@storybook/addon-links": "^8.0.0", "@storybook/blocks": "^8.0.0", "@storybook/react": "^8.0.0", "@storybook/react-vite": "^8.0.0", "@storybook/test": "^8.0.0", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "eslint": "^9.28.0", "postcss": "^8.4.31", "storybook": "^8.0.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.4.17", "tsup": "^8.0.0", "typescript": "5.8.2", "vite": "^5.0.0"}, "dependencies": {"@admin/design-tokens": "*", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "files": ["dist/**"]}