const fs = require('fs');
const path = require('path');

// Import the compiled tokens
const { tokens } = require('../dist/index.js');

function generateCSSVariables(obj, prefix = '') {
  let css = '';

  for (const [key, value] of Object.entries(obj)) {
    // Replace dots with dashes for valid CSS variable names
    const sanitizedKey = key.replace(/\./g, '-');
    const cssKey = prefix ? `${prefix}-${sanitizedKey}` : sanitizedKey;

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      css += generateCSSVariables(value, cssKey);
    } else {
      const cssValue = Array.isArray(value) ? value[0] : value;
      css += `  --${cssKey}: ${cssValue};\n`;
    }
  }

  return css;
}

function generateCSS() {
  let css = ':root {\n';

  // Generate CSS variables for each token category
  css += generateCSSVariables(tokens.colors, 'color');
  css += generateCSSVariables(tokens.radius, 'radius');
  css += generateCSSVariables(tokens.spacing, 'spacing');
  css += generateCSSVariables(tokens.fontFamily, 'font-family');
  css += generateCSSVariables(tokens.fontSize, 'font-size');
  css += generateCSSVariables(tokens.fontWeight, 'font-weight');
  css += generateCSSVariables(tokens.lineHeight, 'line-height');
  css += generateCSSVariables(tokens.boxShadow, 'shadow');
  css += generateCSSVariables(tokens.transitions, 'transition');

  css += '}\n';

  return css;
}

// Generate and write CSS file
const cssContent = generateCSS();
const outputPath = path.join(__dirname, '../dist/tokens.css');

// Ensure dist directory exists
const distDir = path.dirname(outputPath);
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

fs.writeFileSync(outputPath, cssContent);
console.log('✅ Generated CSS variables at', outputPath);
