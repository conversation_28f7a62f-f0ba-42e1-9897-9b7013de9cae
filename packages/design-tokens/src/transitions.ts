/**
 * Transition Design Tokens
 * 
 * Provides consistent timing, duration, and easing values for animations
 * throughout the application.
 */

export const transitions = {
  // Duration tokens
  duration: {
    instant: '0ms',
    fast: '150ms',
    normal: '250ms',
    slow: '350ms',
    slower: '500ms',
    slowest: '750ms',
  },
  
  // Timing function tokens
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    // Custom cubic-bezier curves for more sophisticated animations
    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)', // Material Design standard
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    emphasized: 'cubic-bezier(0.2, 0, 0, 1)',
  },
  
  // Common transition combinations
  common: {
    // For opacity changes (fade in/out)
    fade: 'opacity 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // For transform changes (scale, translate)
    transform: 'transform 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // For color changes (hover states)
    colors: 'color 150ms ease-out, background-color 150ms ease-out, border-color 150ms ease-out',
    
    // For size changes
    size: 'width 250ms ease-out, height 250ms ease-out',
    
    // For modal/overlay animations
    modal: 'opacity 250ms cubic-bezier(0.4, 0, 0.2, 1), transform 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // For smooth all-property transitions
    all: 'all 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // For button interactions
    button: 'background-color 150ms ease-out, transform 150ms ease-out, box-shadow 150ms ease-out',
    
    // For card hover effects
    card: 'transform 200ms ease-out, box-shadow 200ms ease-out',
  },
  
  // Delay tokens for staggered animations
  delay: {
    none: '0ms',
    short: '50ms',
    medium: '100ms',
    long: '200ms',
    longer: '300ms',
  },
} as const;

// Type definitions for TypeScript support
export type TransitionDuration = keyof typeof transitions.duration;
export type TransitionEasing = keyof typeof transitions.easing;
export type TransitionCommon = keyof typeof transitions.common;
export type TransitionDelay = keyof typeof transitions.delay;
