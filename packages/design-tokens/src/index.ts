export { colors } from './colors';
export { radius, spacing } from './spacing';
export { fontFamily, fontSize, fontWeight, lineHeight } from './typography';
export { boxShadow, dropShadow } from './shadows';
export { transitions } from './transitions';

// Combined tokens object for easy access
export const tokens = {
  colors: require('./colors').colors,
  radius: require('./spacing').radius,
  spacing: require('./spacing').spacing,
  fontFamily: require('./typography').fontFamily,
  fontSize: require('./typography').fontSize,
  fontWeight: require('./typography').fontWeight,
  lineHeight: require('./typography').lineHeight,
  boxShadow: require('./shadows').boxShadow,
  dropShadow: require('./shadows').dropShadow,
  transitions: require('./transitions').transitions,
} as const;
