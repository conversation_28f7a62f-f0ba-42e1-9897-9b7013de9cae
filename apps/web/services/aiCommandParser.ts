import { parseCommand, refineData, EntityType, ParsedIntent, ENTITY_SCHEMAS, EntityField, AIConversationResponse, generateConversationalResponse } from './openai';
import { projectEditingService, ProjectEditSession } from './projectEditingService';
import { generateInventoryFields } from './inventoryFieldGenerator';
import { BusinessType } from '../contexts/types';

// Conversation message for tracking chat history
export interface ConversationMessage {
  id: string;
  role: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

// Data creation session state
export interface DataCreationSession {
  id: string;
  intent: ParsedIntent;
  currentData: Record<string, any>;
  entityType: EntityType;
  isComplete: boolean;
  createdAt: Date;
  lastModified: Date;
  // AI conversation state
  aiResponse?: AIConversationResponse;
  conversationHistory: ConversationMessage[];
  needsMoreInfo: boolean;
  isReadyForForm: boolean;
  // Dynamic schema for inventory items
  dynamicSchema?: EntityField[];
  categoryInfo?: any;
}

// Form field with current value
export interface FormFieldWithValue extends EntityField {
  value: any;
  error?: string;
}

// Session manager for tracking data creation sessions
class DataCreationSessionManager {
  private sessions: Map<string, DataCreationSession> = new Map();

  /**
   * Create a new data creation session
   */
  createSession(intent: ParsedIntent): DataCreationSession | null {
    if (!intent.entityType || intent.action !== 'create') {
      return null;
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // For inventory items, use dynamic field generation with category detection
    let schema = ENTITY_SCHEMAS[intent.entityType];
    let categoryInfo = null;

    if (intent.entityType === 'inventory') {
      const itemName = intent.extractedData.name || '';
      const itemDescription = intent.extractedData.description || '';
      const businessType: BusinessType = 'fabrication'; // TODO: Get from user context

      const inventoryFieldsResult = generateInventoryFields(itemName, itemDescription, businessType);
      schema = inventoryFieldsResult.fields;
      categoryInfo = inventoryFieldsResult.categoryInfo;

      // If the AI already extracted a category from the command, use that instead
      if (intent.extractedData.category) {
        categoryInfo.category = intent.extractedData.category;
      }
    }

    // Initialize data with extracted values and defaults
    const initialData: Record<string, any> = {};

    schema.forEach(field => {
      if (intent.extractedData[field.id]) {
        initialData[field.id] = intent.extractedData[field.id];
      } else if (field.defaultValue !== undefined) {
        initialData[field.id] = field.defaultValue;
      } else {
        initialData[field.id] = '';
      }
    });

    // For inventory items, apply the detected category
    if (intent.entityType === 'inventory' && categoryInfo) {
      initialData.category = categoryInfo.category;
    }

    const session: DataCreationSession = {
      id: sessionId,
      intent,
      currentData: initialData,
      entityType: intent.entityType,
      isComplete: false,
      createdAt: new Date(),
      lastModified: new Date(),
      // Initialize AI conversation state
      conversationHistory: [],
      needsMoreInfo: true, // Start with assumption that AI needs to ask questions
      isReadyForForm: false, // Don't show form until AI determines it's ready
      // Store dynamic schema and category info for inventory items
      dynamicSchema: intent.entityType === 'inventory' ? schema : undefined,
      categoryInfo: intent.entityType === 'inventory' ? categoryInfo : undefined
    };

    // Template-based guided conversation is handled separately

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): DataCreationSession | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Update session data
   */
  updateSession(sessionId: string, newData: Record<string, any>): DataCreationSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    session.currentData = { ...session.currentData, ...newData };
    session.lastModified = new Date();

    // For inventory items, regenerate dynamic fields if name or description changed
    if (session.entityType === 'inventory' && (newData.name || newData.description)) {
      const itemName = session.currentData.name || '';
      const itemDescription = session.currentData.description || '';
      const businessType: BusinessType = 'fabrication'; // TODO: Get from user context

      const inventoryFieldsResult = generateInventoryFields(itemName, itemDescription, businessType);
      session.dynamicSchema = inventoryFieldsResult.fields;
      session.categoryInfo = inventoryFieldsResult.categoryInfo;

      // Update category if detected
      if (inventoryFieldsResult.categoryInfo.category) {
        session.currentData.category = inventoryFieldsResult.categoryInfo.category;
      }
    }

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Update guided conversation session
   */
  updateGuidedSession(sessionId: string, updatedSession: DataCreationSession): DataCreationSession | null {
    if (!updatedSession) return null;

    updatedSession.lastModified = new Date();
    this.sessions.set(sessionId, updatedSession);
    return updatedSession;
  }

  /**
   * Add a conversation message to the session
   */
  addConversationMessage(sessionId: string, role: 'user' | 'ai', content: string): DataCreationSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    const message: ConversationMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      role,
      content,
      timestamp: new Date()
    };

    session.conversationHistory.push(message);
    session.lastModified = new Date();

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Update session AI response and conversation state
   */
  updateSessionAIResponse(sessionId: string, aiResponse: AIConversationResponse): DataCreationSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    session.aiResponse = aiResponse;
    session.needsMoreInfo = aiResponse.needsMoreInfo || false;
    session.isReadyForForm = aiResponse.type === 'data_ready';
    session.lastModified = new Date();

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Refine session data using natural language and handle conversation flow
   */
  async refineSession(sessionId: string, refinementCommand: string): Promise<DataCreationSession | null> {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    try {
      // Add user message to conversation history
      this.addConversationMessage(sessionId, 'user', refinementCommand);

      // If the session is ready for form, treat this as data refinement
      if (session.isReadyForForm) {
        const refinedData = await refineData(session.currentData, session.entityType, refinementCommand);
        return this.updateSession(sessionId, refinedData);
      } else {
        // If still in conversation mode, this is a response to AI questions
        // The AI response will be handled by the calling function
        return session;
      }
    } catch (error) {
      console.error('Error refining session data:', error);
      return session; // Return unchanged session on error
    }
  }

  /**
   * Mark session as complete
   */
  completeSession(sessionId: string): DataCreationSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    session.isComplete = true;
    session.lastModified = new Date();
    
    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Delete session
   */
  deleteSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  /**
   * Clean up old sessions (older than 1 hour)
   */
  cleanupOldSessions(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.lastModified < oneHourAgo) {
        this.sessions.delete(sessionId);
      }
    }
  }
}

// Global session manager instance
export const sessionManager = new DataCreationSessionManager();

/**
 * Process project editing command
 */
async function processProjectEditCommand(intent: ParsedIntent): Promise<{
  success: boolean;
  editSession?: ProjectEditSession;
  aiResponse?: AIConversationResponse;
  error?: string;
}> {
  try {
    // Import backend data store to find the target project
    const { backendDataStore } = await import('./backendDataStore');
    const projects = await backendDataStore.getProjects();

    if (!intent.targetProject) {
      return {
        success: false,
        error: 'Please specify which project you want to edit. For example: "edit Tommy Hilfiger project to add John as team member".'
      };
    }

    // Find the target project
    console.log('🔍 Looking for project:', intent.targetProject);
    console.log('📋 Available projects:', projects.map(p => p.name));

    const targetProject = projects.find(project =>
      project.name.toLowerCase().includes(intent.targetProject!.toLowerCase()) ||
      intent.targetProject!.toLowerCase().includes(project.name.toLowerCase())
    );

    if (!targetProject) {
      const projectList = projects.map(p => `• ${p.name}`).join('\n');
      return {
        success: false,
        error: `I couldn't find a project named "${intent.targetProject}". Available projects:\n\n${projectList}\n\nPlease check the project name and try again.`
      };
    }

    // Create editing session
    const editSession = projectEditingService.createEditSession(intent, targetProject);
    if (!editSession) {
      return {
        success: false,
        error: 'Failed to create project editing session.'
      };
    }

    // Load context data for the edit session
    await projectEditingService.loadContextDataForSession(editSession.id);

    // Get the updated session with context data
    const updatedSession = projectEditingService.getEditSession(editSession.id);
    if (!updatedSession) {
      return {
        success: false,
        error: 'Failed to retrieve updated session after loading context data.'
      };
    }

    // Add user command to conversation history
    projectEditingService.addConversationMessage(editSession.id, 'user', intent.originalCommand);

    // Generate AI response based on business template using updated session
    const aiResponse = await projectEditingService.generateEditResponse(updatedSession);

    // Add AI response to conversation history
    projectEditingService.addConversationMessage(editSession.id, 'ai', aiResponse.message);

    // Update session with AI response using immutable pattern
    const finalSession: ProjectEditSession = {
      ...updatedSession,
      aiResponse,
      needsMoreInfo: aiResponse.needsMoreInfo || false,
      isReadyForForm: aiResponse.type === 'data_ready'
    };

    // Store the final session
    projectEditingService.setEditSession(editSession.id, finalSession);

    return {
      success: true,
      editSession: finalSession,
      aiResponse
    };

  } catch (error) {
    console.error('Error processing project edit command:', error);
    return {
      success: false,
      error: 'An error occurred while processing your edit command. Please try again.'
    };
  }
}

/**
 * Process a natural language command and create a data creation session
 */
export async function processCommand(command: string): Promise<{
  success: boolean;
  session?: DataCreationSession;
  editSession?: ProjectEditSession;
  aiResponse?: AIConversationResponse;
  error?: string;
  isDeletion?: boolean;
  deletionSession?: any;
  isProjectEdit?: boolean;
}> {
  try {
    // Clean up old sessions periodically
    sessionManager.cleanupOldSessions();

    // Parse the command
    const intent = await parseCommand(command);

    // Check if we can handle this command
    if (intent.action === 'unknown' || !intent.entityType || intent.confidence < 0.5) {
      return {
        success: false,
        error: 'Sorry, I couldn\'t understand that command. Try something like "add a new employee named John" or "create a project for client ABC".'
      };
    }

    if (!['create', 'delete', 'edit', 'modify', 'update', 'add_to'].includes(intent.action)) {
      return {
        success: false,
        error: 'Currently, I can help you create new items, delete existing ones, or edit projects. Try commands like "add", "create", "delete", "remove", "edit", or "modify".'
      };
    }

    // Handle delete commands
    if (intent.action === 'delete') {
      const { processDeletionCommand } = await import('./aiDeletionProcessor');
      return await processDeletionCommand(intent);
    }

    // Handle task creation without specific project (e.g., "assign task to Jane Smith")
    if (intent.action === 'create' && intent.entityType === 'project' && intent.editType === 'add_task') {
      // This is a task creation command, we need to ask which project
      try {
        const { backendDataStore } = await import('./backendDataStore');
        const projects = await backendDataStore.getProjects();

        if (projects.length === 0) {
          return {
            success: false,
            error: 'No projects found. Please create a project first before adding tasks.'
          };
        }

        const projectList = projects.map(p => `• ${p.name}`).join('\n');
        return {
          success: false,
          error: `Please specify which project you want to add the task to. Available projects:\n\n${projectList}\n\nFor example: "add task to ${projects[0].name}" or "assign task to Jane Smith in ${projects[0].name}".`
        };
      } catch (error) {
        return {
          success: false,
          error: 'Please specify which project you want to add the task to. For example: "add task to [project name]" or "assign task to Jane Smith in [project name]".'
        };
      }
    }

    // Handle project edit commands
    if (['edit', 'modify', 'update', 'add_to'].includes(intent.action) && intent.entityType === 'project') {
      const editResult = await processProjectEditCommand(intent);
      return {
        ...editResult,
        isProjectEdit: true
      };
    }

    // Create a new session
    const session = sessionManager.createSession(intent);
    if (!session) {
      return {
        success: false,
        error: 'Failed to create data creation session.'
      };
    }

    // Handle project creation - simplified to basic data only
    if (session.entityType === 'project') {
      session.isReadyForForm = true;
      session.needsMoreInfo = false;

      // Add user command to conversation history
      sessionManager.addConversationMessage(session.id, 'user', command);

      const confirmationMessage = `I'll help you create a new project. Please fill out the basic project information below. After creating the project, you can use the chat to add team members, tasks, catalog items, and other details.`;

      // Add confirmation message to conversation history
      sessionManager.addConversationMessage(session.id, 'ai', confirmationMessage);

      // Create simple AI response for basic project creation
      const aiResponse: AIConversationResponse = {
        type: 'data_ready',
        message: confirmationMessage,
        needsMoreInfo: false,
        extractedData: session.currentData
      };

      // Update session with AI response
      sessionManager.updateSessionAIResponse(session.id, aiResponse);

      return {
        success: true,
        session,
        aiResponse
      };
    }

    // Handle inventory items with conversational AI
    if (session.entityType === 'inventory') {
      // Add user command to conversation history
      sessionManager.addConversationMessage(session.id, 'user', command);

      // Generate AI conversational response
      const aiResponse = await generateConversationalResponse(
        command,
        intent,
        session.entityType,
        session.currentData
      );

      // Add AI response to conversation history
      sessionManager.addConversationMessage(session.id, 'ai', aiResponse.message);

      // Update session based on AI response
      session.isReadyForForm = aiResponse.type === 'data_ready';
      session.needsMoreInfo = aiResponse.needsMoreInfo || false;

      // Update session with AI response
      sessionManager.updateSessionAIResponse(session.id, aiResponse);

      return {
        success: true,
        session,
        aiResponse
      };
    }

    // Default behavior for other entities (employee, financial, catalog)
    session.isReadyForForm = true;
    session.needsMoreInfo = false;

    // Add user command to conversation history
    sessionManager.addConversationMessage(session.id, 'user', command);

    // Generate a simple confirmation message
    const entityDisplayName = session.entityType.replace('_', ' ');
    const confirmationMessage = `I'll help you create a new ${entityDisplayName}. Please fill out the form below.`;

    // Add confirmation message to conversation history
    sessionManager.addConversationMessage(session.id, 'ai', confirmationMessage);

    // Create a simple AI response
    const aiResponse: AIConversationResponse = {
      type: 'data_ready',
      message: confirmationMessage,
      needsMoreInfo: false,
      extractedData: session.currentData
    };

    // Update session with AI response
    sessionManager.updateSessionAIResponse(session.id, aiResponse);

    return {
      success: true,
      session,
      aiResponse
    };

  } catch (error) {
    console.error('Error processing command:', error);
    return {
      success: false,
      error: 'An error occurred while processing your command. Please try again.'
    };
  }
}

/**
 * Generate form fields with current values for a session
 */
export function generateFormFields(session: DataCreationSession): FormFieldWithValue[] {
  // Use dynamic schema for inventory items, fallback to static schema for others
  const schema = session.dynamicSchema || ENTITY_SCHEMAS[session.entityType];

  return schema.map(field => ({
    ...field,
    value: session.currentData[field.id] || '',
    error: validateField(field, session.currentData[field.id])
  }));
}

/**
 * Validate a single field value
 */
function validateField(field: EntityField, value: any): string | undefined {
  if (field.required && (!value || value === '')) {
    return `${field.label} is required`;
  }

  if (field.type === 'email' && value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
  }

  if (field.type === 'number' && value !== '' && isNaN(Number(value))) {
    return 'Please enter a valid number';
  }

  if (field.type === 'currency' && value !== '' && (isNaN(Number(value)) || Number(value) < 0)) {
    return 'Please enter a valid amount';
  }

  return undefined;
}

/**
 * Validate all fields in a session
 */
export function validateSession(session: DataCreationSession): {
  isValid: boolean;
  errors: Record<string, string>;
} {
  const schema = ENTITY_SCHEMAS[session.entityType];
  const errors: Record<string, string> = {};

  schema.forEach(field => {
    const error = validateField(field, session.currentData[field.id]);
    if (error) {
      errors[field.id] = error;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Get entity type display name
 */
export function getEntityDisplayName(entityType: EntityType): string {
  const displayNames: Record<EntityType, string> = {
    employee: 'Employee',
    project: 'Project',
    inventory: 'Inventory Item',
    financial: 'Financial Record',
    catalog: 'Catalog Item',
    task: 'Task'
  };

  return displayNames[entityType] || entityType;
}
