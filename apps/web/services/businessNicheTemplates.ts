/**
 * Business Niche Templates for Guided Project Creation
 * Each template defines the specific data and modules needed for that business type
 */

export interface ProjectField {
  id: string;
  label: string;
  type: 'text' | 'date' | 'textarea' | 'select';
  required: boolean;
  placeholder?: string;
  options?: string[];
  description?: string;
}

export interface ProjectModule {
  id: string;
  name: string;
  description: string;
  required: boolean;
  questions: string[];
}

export interface BusinessNicheTemplate {
  id: string;
  name: string;
  description: string;
  basicFields: ProjectField[];
  modules: ProjectModule[];
  conversationFlow: {
    steps: string[];
    stepQuestions: Record<string, string>;
  };
}

/**
 * Fabrication Business Template
 */
export const FABRICATION_TEMPLATE: BusinessNicheTemplate = {
  id: 'fabrication',
  name: 'Fabrication & Manufacturing',
  description: 'For businesses that build, manufacture, or fabricate physical products',
  
  // Basic project information (common to all projects)
  basicFields: [
    {
      id: 'name',
      label: 'Project Name',
      type: 'text',
      required: true,
      placeholder: 'e.g., Custom Metal Chairs for Restaurant'
    },
    {
      id: 'client',
      label: 'Client Name',
      type: 'text',
      required: true,
      placeholder: 'e.g., ABC Restaurant'
    },
    {
      id: 'startDate',
      label: 'Start Date',
      type: 'date',
      required: true
    },
    {
      id: 'endDate',
      label: 'End Date',
      type: 'date',
      required: false
    },
    {
      id: 'description',
      label: 'Project Description',
      type: 'textarea',
      required: true,
      placeholder: 'Describe what you will be fabricating/building for the client'
    }
  ],

  // Business-specific modules (mapped to UI module types)
  modules: [
    {
      id: 'finance',
      name: 'Finance',
      description: 'Financial tracking, budgeting, and cost management',
      required: true,
      questions: [
        'What is the budget for this fabrication project?',
        'Do you need to track costs and expenses?'
      ]
    },
    {
      id: 'catalog',
      name: 'Products & Materials',
      description: 'Select products or materials from your catalog',
      required: false,
      questions: [
        'What products or materials will you need from your catalog for this fabrication project?',
        'Do you need any specific materials, tools, or components?'
      ]
    },
    {
      id: 'team',
      name: 'Team Members',
      description: 'Assign team members to the project',
      required: false,
      questions: [
        'Which team members will work on this fabrication project?',
        'What roles will each team member have?'
      ]
    },
    {
      id: 'timeline',
      name: 'Project Timeline',
      description: 'Define project timeline and tasks',
      required: false,
      questions: [
        'What specific fabrication tasks need to be completed?',
        'Do you need to assign any specialized tasks to team members?'
      ]
    },
    {
      id: 'inventory',
      name: 'Materials & Inventory',
      description: 'Manage materials and Bill of Materials (BOM)',
      required: false,
      questions: [
        'Do you need to track material inventory for this project?',
        'Will you need to create a Bill of Materials (BOM)?'
      ]
    },
    {
      id: 'logistics',
      name: 'Logistics & Delivery',
      description: 'Manage materials, shipping, and delivery',
      required: false,
      questions: [
        'Do you need to track material deliveries or inventory for this project?',
        'Will you need to arrange shipping or delivery of the finished products?'
      ]
    }
  ],

  // Conversation flow definition
  conversationFlow: {
    steps: ['basic_info', 'catalog_selection', 'team_assignment', 'task_assignment', 'logistics_setup', 'confirmation'],
    stepQuestions: {
      basic_info: 'Let\'s start with the project basics. I need the project name, client name, start date, and a description of what you\'ll be fabricating.',
      catalog_selection: 'Now let\'s select the products and materials you\'ll need from your catalog for this fabrication project.',
      team_assignment: 'Which team members will work on this fabrication project?',
      task_assignment: 'What specific fabrication tasks need to be completed for this project?',
      logistics_setup: 'Do you need any logistics support like material tracking, inventory management, or delivery coordination?',
      confirmation: 'Perfect! Let me confirm all the details for your fabrication project before we create it.'
    }
  }
};

/**
 * All available business niche templates
 */
export const BUSINESS_NICHE_TEMPLATES: Record<string, BusinessNicheTemplate> = {
  fabrication: FABRICATION_TEMPLATE
};

/**
 * Get template for a specific business niche
 */
export function getBusinessNicheTemplate(nicheId: string): BusinessNicheTemplate | null {
  return BUSINESS_NICHE_TEMPLATES[nicheId] || null;
}

/**
 * Get current business niche (for now, defaulting to fabrication)
 * In the future, this could be user-configurable
 */
export function getCurrentBusinessNiche(): string {
  // TODO: Get from user settings/profile
  return 'fabrication';
}

/**
 * Get template for current business niche
 */
export function getCurrentBusinessTemplate(): BusinessNicheTemplate {
  const currentNiche = getCurrentBusinessNiche();
  const template = getBusinessNicheTemplate(currentNiche);
  
  if (!template) {
    throw new Error(`No template found for business niche: ${currentNiche}`);
  }
  
  return template;
}
