import { ParsedIntent, AIConversationResponse, generateConversationalResponse } from './openai';
import { getCurrentBusinessNiche, getBusinessNicheTemplate, BusinessNicheTemplate } from './businessNicheTemplates';
import { Project, TeamMember, ProjectTask, CatalogItem, InventoryItem } from '../contexts/types';
import { DataCreationSession, ConversationMessage } from './aiCommandParser';
import { ContextDataService, SelectableItem, ContextDataResult } from './contextDataService';

/**
 * Project editing session for tracking edit operations
 */
export interface ProjectEditSession {
  id: string;
  projectId: string;
  projectName: string;
  intent: ParsedIntent;
  editType: string;
  businessTemplate: BusinessNicheTemplate;
  currentData: Record<string, any>;
  conversationHistory: ConversationMessage[];
  isComplete: boolean;
  createdAt: Date;
  lastModified: Date;
  aiResponse?: AIConversationResponse;
  needsMoreInfo: boolean;
  isReadyForForm: boolean;
  // Context data for selection
  contextData?: ContextDataResult;
  selectedItems?: SelectableItem[];
  showContextSelection?: boolean;
}

/**
 * Project editing service that handles project modifications
 * using business niche templates for context-aware suggestions
 */
export class ProjectEditingService {
  private editSessions = new Map<string, ProjectEditSession>();

  /**
   * Create a new project editing session
   */
  createEditSession(
    intent: ParsedIntent, 
    project: Project
  ): ProjectEditSession | null {
    if (!intent.targetProject || !intent.editType) {
      return null;
    }

    const sessionId = `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const businessNiche = getCurrentBusinessNiche();
    const businessTemplate = getBusinessNicheTemplate(businessNiche);
    
    if (!businessTemplate) {
      return null;
    }

    const session: ProjectEditSession = {
      id: sessionId,
      projectId: project.id,
      projectName: project.name,
      intent,
      editType: intent.editType,
      businessTemplate,
      currentData: intent.extractedData || {},
      conversationHistory: [],
      isComplete: false,
      createdAt: new Date(),
      lastModified: new Date(),
      needsMoreInfo: false,
      isReadyForForm: false,
      selectedItems: [],
      showContextSelection: false
    };

    this.editSessions.set(sessionId, session);

    // Load context data for the session (async, but don't wait)
    this.loadContextDataForSession(sessionId);

    return session;
  }

  /**
   * Get editing session by ID
   */
  getEditSession(sessionId: string): ProjectEditSession | null {
    return this.editSessions.get(sessionId) || null;
  }

  /**
   * Update editing session data
   */
  updateEditSession(sessionId: string, newData: Record<string, any>): ProjectEditSession | null {
    const session = this.editSessions.get(sessionId);
    if (!session) return null;

    session.currentData = { ...session.currentData, ...newData };
    session.lastModified = new Date();

    this.editSessions.set(sessionId, session);
    return session;
  }

  /**
   * Add conversation message to editing session
   */
  addConversationMessage(sessionId: string, role: 'user' | 'ai', content: string): ProjectEditSession | null {
    const session = this.editSessions.get(sessionId);
    if (!session) return null;

    const message: ConversationMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      role,
      content,
      timestamp: new Date()
    };

    session.conversationHistory.push(message);
    session.lastModified = new Date();

    this.editSessions.set(sessionId, session);
    return session;
  }

  /**
   * Generate AI response for project editing based on business template
   */
  async generateEditResponse(session: ProjectEditSession): Promise<AIConversationResponse> {
    const { editType, businessTemplate, currentData, projectName, contextData, showContextSelection } = session;

    // Get relevant module from business template
    const relevantModule = this.getRelevantModule(editType, businessTemplate);

    let message = '';
    let needsMoreInfo = false;
    let extractedData = currentData;

    // If we have context data and should show selection, present the options
    if (contextData && showContextSelection && contextData.items.length > 0) {
      switch (editType) {
        case 'add_team_member':
          message = `I found ${contextData.totalCount} team members available. Please select who you'd like to add to the ${projectName} project:`;
          break;
        case 'add_product':
          message = `I found ${contextData.totalCount} products in your catalog. Please select which products to add to the ${projectName} project:`;
          break;
        case 'add_service':
          message = `I found ${contextData.totalCount} services in your catalog. Please select which services to add to the ${projectName} project:`;
          break;
        case 'add_material':
          message = `I found ${contextData.totalCount} materials in your inventory. Please select which materials to add to the ${projectName} project:`;
          break;
        case 'add_task':
          message = `I found ${contextData.totalCount} task templates available. Please select which tasks to add to the ${projectName} project, or create a custom task:`;
          break;
        default:
          message = `I found ${contextData.totalCount} items available for selection.`;
      }
      return {
        type: 'question',
        message,
        needsMoreInfo: false,
        extractedData: currentData
      };
    }

    switch (editType) {
      case 'add_team_member':
        if (!currentData.name && (!contextData || contextData.items.length === 0)) {
          message = `I'll help you add a team member to the ${projectName} project. What's the team member's name and role?`;
          needsMoreInfo = true;
        } else {
          message = `I'll add ${currentData.name} to the ${projectName} project team. Please provide their role, hourly rate, and any other details.`;
          extractedData = {
            name: currentData.name,
            role: currentData.role || '',
            hourlyRate: currentData.hourlyRate || 0,
            email: currentData.email || '',
            skills: currentData.skills || []
          };
        }
        break;

      case 'add_task':
        if (!currentData.name) {
          message = `I'll help you add a task to the ${projectName} project. What task would you like to add?`;
          needsMoreInfo = true;
        } else {
          message = `I'll add the task "${currentData.name}" to the ${projectName} project. Please provide the start date, end date, and assign team members.`;
          extractedData = {
            name: currentData.name,
            startDate: currentData.startDate || '',
            endDate: currentData.endDate || '',
            assignedTo: currentData.assignedTo || [],
            completed: false
          };
        }
        break;

      case 'add_product':
      case 'add_service':
        const itemType = editType === 'add_product' ? 'product' : 'service';
        if (!currentData.name) {
          message = `I'll help you add a ${itemType} to the ${projectName} project from your catalog. What ${itemType} would you like to add?`;
          needsMoreInfo = true;
        } else {
          message = `I'll add the ${itemType} "${currentData.name}" to the ${projectName} project. Please specify the quantity and any other details.`;
          extractedData = {
            name: currentData.name,
            quantity: currentData.quantity || 1,
            price: currentData.price || 0,
            description: currentData.description || ''
          };
        }
        break;

      case 'add_material':
        if (!currentData.name) {
          message = `I'll help you add materials to the ${projectName} project. What materials do you need?`;
          needsMoreInfo = true;
        } else {
          message = `I'll add "${currentData.name}" materials to the ${projectName} project. Please specify the quantity, unit, and cost.`;
          extractedData = {
            name: currentData.name,
            quantity: currentData.quantity || 1,
            unit: currentData.unit || '',
            cost: currentData.cost || 0,
            category: currentData.category || 'Materials'
          };
        }
        break;

      case 'enable_module':
        const moduleQuestions = relevantModule?.questions || [];
        message = `I'll help you enable additional modules for the ${projectName} project. ${moduleQuestions[0] || 'What module would you like to enable?'}`;
        needsMoreInfo = true;
        break;

      case 'update_info':
        message = `I'll help you update the ${projectName} project information. What would you like to change?`;
        needsMoreInfo = true;
        break;

      default:
        message = `I'll help you modify the ${projectName} project. Please provide more details about what you'd like to change.`;
        needsMoreInfo = true;
    }

    return {
      type: needsMoreInfo ? 'question' : 'data_ready',
      message,
      needsMoreInfo,
      extractedData
    };
  }

  /**
   * Get relevant module from business template based on edit type
   */
  private getRelevantModule(editType: string, template: BusinessNicheTemplate) {
    switch (editType) {
      case 'add_team_member':
        return template.modules.find(m => m.id === 'team');
      case 'add_product':
      case 'add_service':
        return template.modules.find(m => m.id === 'catalog');
      case 'add_material':
        return template.modules.find(m => m.id === 'inventory');
      case 'add_task':
        return template.modules.find(m => m.id === 'timeline');
      default:
        return null;
    }
  }

  /**
   * Clean up old editing sessions
   */
  cleanupOldSessions(): void {
    const now = new Date();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    for (const [sessionId, session] of this.editSessions.entries()) {
      if (now.getTime() - session.lastModified.getTime() > maxAge) {
        this.editSessions.delete(sessionId);
      }
    }
  }

  /**
   * Complete editing session
   */
  completeEditSession(sessionId: string): void {
    const session = this.editSessions.get(sessionId);
    if (session) {
      session.isComplete = true;
      session.lastModified = new Date();
      this.editSessions.set(sessionId, session);
    }
  }

  /**
   * Set editing session (replace entire session)
   */
  setEditSession(sessionId: string, session: ProjectEditSession): void {
    this.editSessions.set(sessionId, session);
  }

  /**
   * Delete editing session
   */
  deleteEditSession(sessionId: string): void {
    this.editSessions.delete(sessionId);
  }

  /**
   * Load context data for a session based on edit type
   */
  async loadContextDataForSession(sessionId: string): Promise<void> {
    try {
      const session = this.editSessions.get(sessionId);
      if (!session) {
        console.error('Session not found:', sessionId);
        return;
      }

      const contextData = await ContextDataService.getContextDataForEditType(session.editType);

      // Create updated session object
      const updatedSession: ProjectEditSession = {
        ...session,
        contextData,
        showContextSelection: contextData.items.length > 0
      };

      // Update session in storage
      this.editSessions.set(sessionId, updatedSession);
    } catch (error) {
      console.error('Error loading context data for session:', error);
    }
  }

  /**
   * Update selected items in a session
   */
  updateSessionSelection(sessionId: string, itemId: string, isSelected: boolean, quantity?: number): ProjectEditSession | null {
    const session = this.getEditSession(sessionId);
    if (!session || !session.contextData) {
      return null;
    }

    // Update selection in context data
    const updatedItems = ContextDataService.updateItemSelection(
      session.contextData.items,
      itemId,
      isSelected,
      quantity
    );

    // Create updated session object
    const updatedSession: ProjectEditSession = {
      ...session,
      contextData: {
        ...session.contextData,
        items: updatedItems
      },
      selectedItems: ContextDataService.getSelectedItems(updatedItems)
    };

    // Update session in storage
    this.editSessions.set(sessionId, updatedSession);

    return updatedSession;
  }

  /**
   * Confirm selection and prepare for project integration
   */
  confirmSelection(sessionId: string): ProjectEditSession | null {
    const session = this.getEditSession(sessionId);
    if (!session || !session.selectedItems) {
      return null;
    }

    // Format selected items for project integration
    const formattedItems = ContextDataService.formatSelectedItemsForProject(
      session.selectedItems,
      session.editType
    );

    // Create updated session object
    const updatedSession: ProjectEditSession = {
      ...session,
      currentData: {
        ...session.currentData,
        selectedItems: formattedItems,
        selectionConfirmed: true
      },
      showContextSelection: false,
      isReadyForForm: true
    };

    // Update session in storage
    this.editSessions.set(sessionId, updatedSession);

    return updatedSession;
  }
}

// Export singleton instance
export const projectEditingService = new ProjectEditingService();
