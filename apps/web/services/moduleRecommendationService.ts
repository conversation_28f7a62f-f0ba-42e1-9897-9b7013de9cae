// Frontend wrapper for module recommendation service - calls backend API
import { ProjectModuleType, ModuleRecommendation, ModuleAnalysisResult, ModuleConfiguration } from '../contexts/types';

/**
 * Analyze project description and recommend appropriate modules
 */
export async function analyzeProjectAndRecommendModules(
  projectName: string,
  projectDescription: string
): Promise<ModuleAnalysisResult> {
  try {
    const response = await fetch('http://localhost:3001/api/ai/module-recommendations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectName,
        projectDescription
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result as ModuleAnalysisResult;

  } catch (error) {
    console.error('Error analyzing project for module recommendations:', error);
    // Return fallback recommendation
    return getDefaultModuleRecommendation(projectName, projectDescription);
  }
}

/**
 * Get default module recommendations when AI fails
 */
function getDefaultModuleRecommendation(projectName: string, projectDescription: string): ModuleAnalysisResult {
  const description = projectDescription.toLowerCase();
  const name = projectName.toLowerCase();
  
  // Basic keyword-based analysis
  const isConstruction = /construction|building|structure|renovation|install/i.test(description + ' ' + name);
  const isManufacturing = /manufacturing|production|factory|assembly/i.test(description + ' ' + name);
  const isService = /service|consulting|design|analysis|support/i.test(description + ' ' + name);
  const isRetail = /store|shop|retail|sales|commerce/i.test(description + ' ' + name);
  
  let recommendations: ModuleRecommendation[] = [
    {
      module: 'finance',
      confidence: 1.0,
      reasoning: 'Financial tracking is essential for all projects',
      configuration: {}
    }
  ];

  let projectType = 'general';
  let complexity: 'simple' | 'medium' | 'complex' = 'medium';

  if (isConstruction) {
    projectType = 'construction';
    complexity = 'complex';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.9,
        reasoning: 'Construction projects typically involve products and materials',
        configuration: {
          catalog: { enableProducts: true, enableServices: true }
        }
      },
      {
        module: 'team',
        confidence: 0.8,
        reasoning: 'Construction requires team coordination and resource management',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.9,
        reasoning: 'Construction projects need detailed timeline and task management',
        configuration: {}
      },
      {
        module: 'inventory',
        confidence: 0.8,
        reasoning: 'Material tracking and BOM management is important for construction',
        configuration: {}
      },
      {
        module: 'logistics',
        confidence: 0.7,
        reasoning: 'Construction involves material delivery and supply chain coordination',
        configuration: {}
      }
    );
  } else if (isManufacturing) {
    projectType = 'manufacturing';
    complexity = 'complex';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.9,
        reasoning: 'Manufacturing projects involve products and components',
        configuration: {
          catalog: { enableProducts: true, enableServices: false }
        }
      },
      {
        module: 'team',
        confidence: 0.8,
        reasoning: 'Manufacturing requires skilled team coordination',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.8,
        reasoning: 'Production schedules and milestones are critical',
        configuration: {}
      },
      {
        module: 'inventory',
        confidence: 0.9,
        reasoning: 'Raw materials and component tracking is essential',
        configuration: {}
      },
      {
        module: 'logistics',
        confidence: 0.8,
        reasoning: 'Supply chain and delivery management is important',
        configuration: {}
      }
    );
  } else if (isService) {
    projectType = 'services';
    complexity = 'simple';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.8,
        reasoning: 'Service projects need service catalog management',
        configuration: {
          catalog: { enableProducts: false, enableServices: true }
        }
      },
      {
        module: 'team',
        confidence: 0.7,
        reasoning: 'Team skills and time tracking for service delivery',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.6,
        reasoning: 'Basic timeline tracking for service milestones',
        configuration: {}
      }
    );
  } else {
    // Default recommendation for general projects
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.6,
        reasoning: 'Most projects benefit from catalog management',
        configuration: {}
      },
      {
        module: 'team',
        confidence: 0.7,
        reasoning: 'Team management is useful for most projects',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.8,
        reasoning: 'Timeline tracking helps with project organization',
        configuration: {}
      }
    );
  }

  return {
    recommendations,
    projectType,
    complexity,
    suggestedModules: recommendations.map(r => r.module)
  };
}

/**
 * Get module configuration suggestions based on project analysis
 */
export function getModuleConfigurationSuggestions(
  modules: ProjectModuleType[],
  projectType: string,
  projectDescription: string
): ModuleConfiguration {
  const config: ModuleConfiguration = {};

  if (modules.includes('catalog')) {
    const isProductBased = /product|material|equipment|hardware|physical/i.test(projectDescription);
    const isServiceBased = /service|consulting|design|analysis|support|software/i.test(projectDescription);
    
    config.catalog = {
      enableProducts: isProductBased || projectType === 'construction' || projectType === 'manufacturing',
      enableServices: isServiceBased || projectType === 'services' || projectType === 'consulting',
      productCategories: [],
      serviceTypes: []
    };
  }

  if (modules.includes('team')) {
    config.team = {
      trackHours: true,
      enableSkillsManagement: projectType === 'services' || projectType === 'consulting',
      enableCostTracking: true
    };
  }

  if (modules.includes('timeline')) {
    config.timeline = {
      enableGanttView: projectType === 'construction' || projectType === 'manufacturing',
      enableMilestones: true,
      trackDependencies: projectType === 'construction' || projectType === 'manufacturing'
    };
  }

  if (modules.includes('inventory')) {
    config.inventory = {
      trackMaterials: true,
      enableBOM: projectType === 'construction' || projectType === 'manufacturing',
      trackSuppliers: projectType === 'construction' || projectType === 'manufacturing'
    };
  }

  if (modules.includes('logistics')) {
    config.logistics = {
      trackShipments: projectType === 'construction' || projectType === 'manufacturing',
      enableSupplyChain: projectType === 'manufacturing',
      trackDeliveries: true
    };
  }

  return config;
}
