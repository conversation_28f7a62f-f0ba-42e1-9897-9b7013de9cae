import { EntityField } from './openai';
import { inventoryTemplates, InventoryCategory } from './inventoryTemplates';
import { rawMaterialTemplates, MaterialTypeTemplate } from './rawMaterialTemplates';
import { equipmentTemplates, EquipmentTypeTemplate } from './equipmentTemplates';
import { finishedProductTemplates, ProductTypeTemplate } from './finishedProductTemplates';
import { BusinessType } from '../contexts/types';

// Extended interface for inventory-specific fields
export interface InventoryEntityField extends EntityField {
  category?: string;
  subcategory?: string;
  isTemplateField?: boolean;
}

// Result of category detection
export interface CategoryDetectionResult {
  category: string;
  subcategory?: string;
  confidence: number;
  isKnownCategory: boolean;
  suggestedFields?: InventoryEntityField[];
  missingCategoryInfo?: {
    detectedItem: string;
    suggestedCategory: string;
    message: string;
  };
}

// Enhanced keyword matching with synonyms and variations
const MATERIAL_KEYWORDS = {
  metal: ['metal', 'acero', 'steel', 'hierro', 'iron', 'aluminio', 'aluminum', 'cobre', 'copper', 'lamina', 'sheet', 'placa', 'plate', 'chapa', 'metallic', 'metálico'],
  equipment: ['equipo', 'equipment', 'maquina', 'machine', 'herramienta', 'tool', 'soldadora', 'welder', 'taladro', 'drill', 'sierra', 'saw'],
  consumable: ['consumible', 'consumable', 'insumo', 'supply', 'suministro'], // Removed 'material' to prevent false matches
  finished: [
    // Spanish terms
    'producto', 'terminado', 'final', 'acabado', 'completo', 'listo', 'fabricado', 'manufacturado',
    'puerta', 'ventana', 'marco', 'estructura', 'barandal', 'mueble', 'mesa', 'silla', 'escritorio',
    'estantería', 'gabinete', 'armario', 'cama', 'sofá', 'sillón', 'lámpara', 'espejo',
    // English terms
    'product', 'finished', 'final', 'complete', 'ready', 'manufactured', 'fabricated', 'assembled',
    'door', 'window', 'frame', 'structure', 'railing', 'furniture', 'table', 'chair', 'desk',
    'shelf', 'cabinet', 'wardrobe', 'bed', 'sofa', 'lamp', 'mirror', 'appliance', 'device',
    // Technology/Electronics
    'ipad', 'tablet', 'computer', 'laptop', 'phone', 'smartphone', 'television', 'tv', 'monitor',
    'computadora', 'tableta', 'teléfono', 'televisión', 'monitor', 'electrodoméstico', 'aparato'
  ]
};

/**
 * Check if text contains any of the keywords (improved matching to prevent false positives)
 */
function containsKeywords(text: string, keywords: string[]): number {
  const words = text.toLowerCase().split(/\s+/);
  let matches = 0;
  let totalScore = 0;

  for (const keyword of keywords) {
    const keywordLower = keyword.toLowerCase();

    for (const word of words) {
      // Exact match - highest score
      if (word === keywordLower) {
        matches++;
        totalScore += 1.0;
        break; // Don't double-count this keyword
      }
      // Partial match with stricter rules to prevent false positives
      else if (word.length >= 4 && keywordLower.length >= 4) {
        // Only allow partial matches if:
        // 1. One word starts with the other (prefix matching)
        // 2. The shorter word is at least 70% of the longer word's length
        const minLength = Math.min(word.length, keywordLower.length);
        const maxLength = Math.max(word.length, keywordLower.length);
        const lengthRatio = minLength / maxLength;

        if (lengthRatio >= 0.7) {
          if (word.startsWith(keywordLower) || keywordLower.startsWith(word)) {
            matches++;
            totalScore += 0.8;
            break; // Don't double-count this keyword
          }
          // Allow contains matching only for very similar lengths
          else if (lengthRatio >= 0.85 && (word.includes(keywordLower) || keywordLower.includes(word))) {
            matches++;
            totalScore += 0.6;
            break; // Don't double-count this keyword
          }
        }
      }
    }
  }

  return matches > 0 ? totalScore / keywords.length : 0;
}

/**
 * Detect inventory category and subcategory from item description
 */
export function detectInventoryCategory(
  itemName: string,
  itemDescription: string,
  businessType: BusinessType = 'fabrication'
): CategoryDetectionResult {
  const searchText = `${itemName} ${itemDescription}`.toLowerCase();

  // Get categories for the business type
  const businessTemplate = inventoryTemplates[businessType];
  if (!businessTemplate) {
    return {
      category: 'Materia Prima',
      confidence: 0.3,
      isKnownCategory: false,
      missingCategoryInfo: {
        detectedItem: itemName,
        suggestedCategory: 'Unknown',
        message: `Business type "${businessType}" not found in templates`
      }
    };
  }

  let bestMatch: { category: InventoryCategory; confidence: number } | null = null;
  let bestSubcategoryMatch: { template: any; confidence: number; type: string } | null = null;

  // Check each category
  for (const category of businessTemplate.categories) {
    let confidence = 0;

    // Enhanced matching for typical items
    for (const typicalItem of category.typical_items) {
      const itemWords = typicalItem.toLowerCase().split(/\s+/);
      const score = containsKeywords(searchText, itemWords);
      if (score > 0) {
        confidence += score * 0.8;
        break;
      }
    }

    // Enhanced category name matching
    const categoryWords = category.display_name.toLowerCase().split(/\s+/);
    const categoryScore = containsKeywords(searchText, categoryWords);
    if (categoryScore > 0) {
      confidence += categoryScore * 0.6;
    }

    // Enhanced description keyword matching
    const descWords = category.description.toLowerCase().split(/\s+/).slice(0, 3); // First 3 words
    const descScore = containsKeywords(searchText, descWords);
    if (descScore > 0) {
      confidence += descScore * 0.4;
    }

    // Special keyword-based detection
    if (category.id === 'raw_materials') {
      const metalScore = containsKeywords(searchText, MATERIAL_KEYWORDS.metal);
      confidence += metalScore * 0.7;
    }

    if (category.id === 'tools_equipment' || category.id === 'equipment') {
      const equipScore = containsKeywords(searchText, MATERIAL_KEYWORDS.equipment);
      confidence += equipScore * 0.7;
    }

    if (category.id === 'consumables') {
      const consumScore = containsKeywords(searchText, MATERIAL_KEYWORDS.consumable);
      confidence += consumScore * 0.7;
    }

    if (category.id === 'finished_products') {
      const finishedScore = containsKeywords(searchText, MATERIAL_KEYWORDS.finished);
      confidence += finishedScore * 0.7;

      // Additional boost for finished product specific terms
      if (searchText.includes('terminado') || searchText.includes('finished') ||
          searchText.includes('producto') || searchText.includes('product') ||
          searchText.includes('completo') || searchText.includes('complete') ||
          searchText.includes('listo') || searchText.includes('ready')) {
        confidence += 0.3;
      }
    }

    if (confidence > (bestMatch?.confidence || 0)) {
      bestMatch = { category, confidence };
    }
  }

  // If we found a good category match, try to find subcategory
  if (bestMatch && bestMatch.confidence > 0.3) { // Lower threshold for subcategory detection
    // Check raw material templates for subcategory
    if (bestMatch.category.id === 'raw_materials') {
      for (const [templateKey, template] of Object.entries(rawMaterialTemplates)) {
        let subConfidence = 0;

        // Enhanced form type matching
        for (const formType of template.form_types) {
          const formWords = formType.toLowerCase().split(/\s+/);
          const score = containsKeywords(searchText, formWords);
          if (score > 0) {
            subConfidence += score * 0.9;
            break;
          }
        }

        // Enhanced template display name matching
        const templateWords = template.display_name.toLowerCase().split(/\s+/);
        const templateScore = containsKeywords(searchText, templateWords);
        if (templateScore > 0) {
          subConfidence += templateScore * 0.7;
        }

        // Direct keyword matching for common English terms
        if (templateKey === 'metal_sheet') {
          const metalSheetKeywords = ['metal sheet', 'steel sheet', 'sheet metal', 'metal plate', 'steel plate'];
          for (const keyword of metalSheetKeywords) {
            if (searchText.includes(keyword)) {
              subConfidence += 0.9; // High confidence for direct match
              break;
            }
          }
        }

        if (subConfidence > (bestSubcategoryMatch?.confidence || 0)) {
          bestSubcategoryMatch = { template, confidence: subConfidence, type: 'raw_material' };
        }
      }
    }

    // Check equipment templates for subcategory
    if (bestMatch.category.id === 'tools_equipment' || bestMatch.category.id === 'equipment') {
      for (const [templateKey, template] of Object.entries(equipmentTemplates)) {
        let subConfidence = 0;

        // Enhanced typical items matching
        for (const typicalItem of template.typical_items) {
          const itemWords = typicalItem.toLowerCase().split(/\s+/);
          const score = containsKeywords(searchText, itemWords);
          if (score > 0) {
            subConfidence += score * 0.9;
            break;
          }
        }

        // Enhanced template display name matching
        const templateWords = template.display_name.toLowerCase().split(/\s+/);
        const templateScore = containsKeywords(searchText, templateWords);
        if (templateScore > 0) {
          subConfidence += templateScore * 0.7;
        }

        if (subConfidence > (bestSubcategoryMatch?.confidence || 0)) {
          bestSubcategoryMatch = { template, confidence: subConfidence, type: 'equipment' };
        }
      }
    }

    // Check finished product templates for subcategory
    if (bestMatch.category.id === 'finished_products') {
      for (const [templateKey, template] of Object.entries(finishedProductTemplates)) {
        let subConfidence = 0;

        // Enhanced typical categories matching
        for (const typicalCategory of template.typical_categories) {
          const categoryWords = typicalCategory.toLowerCase().split(/\s+/);
          const score = containsKeywords(searchText, categoryWords);
          if (score > 0) {
            subConfidence += score * 0.9;
            break;
          }
        }

        // Enhanced template display name matching
        const templateWords = template.display_name.toLowerCase().split(/\s+/);
        const templateScore = containsKeywords(searchText, templateWords);
        if (templateScore > 0) {
          subConfidence += templateScore * 0.7;
        }

        // Enhanced description matching
        const descWords = template.description.toLowerCase().split(/\s+/).slice(0, 3);
        const descScore = containsKeywords(searchText, descWords);
        if (descScore > 0) {
          subConfidence += descScore * 0.5;
        }

        if (subConfidence > (bestSubcategoryMatch?.confidence || 0)) {
          bestSubcategoryMatch = { template, confidence: subConfidence, type: 'finished_product' };
        }
      }
    }
  }

  // Determine if this is a known category (more lenient threshold)
  const isKnownCategory = bestMatch ? bestMatch.confidence > 0.3 : false;
  const finalConfidence = bestMatch ? bestMatch.confidence : 0.3;

  // Generate suggested fields if we have a good match (lower threshold)
  let suggestedFields: InventoryEntityField[] | undefined;
  if (bestSubcategoryMatch && bestSubcategoryMatch.confidence > 0.3) {
    suggestedFields = generateFieldsFromTemplate(bestSubcategoryMatch.template, bestSubcategoryMatch.type);
  }

  // Handle unknown categories
  let missingCategoryInfo;
  if (!isKnownCategory) {
    missingCategoryInfo = {
      detectedItem: itemName,
      suggestedCategory: bestMatch?.category.display_name || 'Unknown',
      message: `The item "${itemName}" doesn't match any known inventory categories. Please add this category to the templates.`
    };
  }

  // Smarter default category selection
  let defaultCategory = 'Materia Prima';
  if (!bestMatch) {
    // If no match found, try to make an educated guess based on keywords
    const hasFinishedKeywords = MATERIAL_KEYWORDS.finished.some(keyword =>
      searchText.includes(keyword.toLowerCase())
    );
    const hasEquipmentKeywords = MATERIAL_KEYWORDS.equipment.some(keyword =>
      searchText.includes(keyword.toLowerCase())
    );
    const hasConsumableKeywords = MATERIAL_KEYWORDS.consumable.some(keyword =>
      searchText.includes(keyword.toLowerCase())
    );

    if (hasFinishedKeywords) {
      defaultCategory = 'Productos Terminados';
    } else if (hasEquipmentKeywords) {
      defaultCategory = 'Herramientas y Equipos';
    } else if (hasConsumableKeywords) {
      defaultCategory = 'Consumibles';
    }
  }

  return {
    category: bestMatch?.category.display_name || defaultCategory,
    subcategory: bestSubcategoryMatch?.template.display_name,
    confidence: finalConfidence,
    isKnownCategory,
    suggestedFields,
    missingCategoryInfo
  };
}

/**
 * Generate form fields from template
 */
function generateFieldsFromTemplate(template: any, templateType: string): InventoryEntityField[] {
  const fields: InventoryEntityField[] = [];
  
  if (templateType === 'raw_material' || templateType === 'equipment' || templateType === 'finished_product') {
    // Convert template columns to form fields
    for (const column of template.columns) {
      fields.push({
        id: column.id,
        label: column.header,
        type: mapColumnTypeToFieldType(column.type),
        required: column.required,
        placeholder: column.description,
        isTemplateField: true,
        category: template.display_name
      });
    }
  }
  
  return fields;
}

/**
 * Map template column types to form field types
 */
function mapColumnTypeToFieldType(columnType: string): 'text' | 'number' | 'email' | 'select' | 'date' | 'currency' {
  switch (columnType) {
    case 'currency':
      return 'currency';
    case 'number':
    case 'hours':
      return 'number';
    case 'date':
      return 'date';
    case 'select':
    case 'status':
      return 'select';
    default:
      return 'text';
  }
}

/**
 * Generate complete inventory fields including base fields and template-specific fields
 */
export function generateInventoryFields(
  itemName: string,
  itemDescription: string,
  businessType: BusinessType = 'fabrication'
): {
  fields: InventoryEntityField[];
  categoryInfo: CategoryDetectionResult;
} {
  // Detect category
  const categoryInfo = detectInventoryCategory(itemName, itemDescription, businessType);
  
  // Base inventory fields (always included)
  const baseFields: InventoryEntityField[] = [
    { id: 'name', label: 'Item Name', type: 'text', required: true, placeholder: 'Enter item name' },
    { id: 'description', label: 'Description', type: 'text', required: true, placeholder: 'Item description' },
    { id: 'quantity', label: 'Quantity', type: 'number', required: true, placeholder: '0' },
    { id: 'unit', label: 'Unit', type: 'select', required: true, options: ['piezas', 'kg', 'litros', 'metros'], defaultValue: 'piezas' },
    { id: 'category', label: 'Category', type: 'select', required: true, options: ['Materia Prima', 'Herramientas y Equipos', 'Consumibles', 'Productos Terminados'], defaultValue: categoryInfo.category },
    { id: 'cost', label: 'Cost per unit', type: 'currency', required: true, placeholder: '0.00', defaultValue: '0' },
    { id: 'location', label: 'Location', type: 'text', required: false, placeholder: 'Storage location' },
    { id: 'supplier', label: 'Supplier', type: 'text', required: false, placeholder: 'Supplier name' },
  ];

  // Financial fields (always included)
  const financialFields: InventoryEntityField[] = [
    { id: 'paymentMethod', label: 'Payment Method', type: 'select', required: false, options: ['cash', 'credit_card', 'loan', 'bank_transfer', 'check'], defaultValue: 'cash' },
    { id: 'isRecurring', label: 'Recurring Payment', type: 'select', required: false, options: ['yes', 'no'], defaultValue: 'no' },
    { id: 'monthlyAmount', label: 'Monthly Payment', type: 'currency', required: false, placeholder: '0' },
    { id: 'totalMonths', label: 'Total Months', type: 'number', required: false, placeholder: '12' },
    { id: 'interestRate', label: 'Interest Rate (%)', type: 'number', required: false, placeholder: '5.5' },
    { id: 'dueDate', label: 'Due Date', type: 'date', required: false }
  ];

  // Combine all fields, avoiding duplicates
  let allFields = [...baseFields];

  // Add template-specific fields if available, but avoid duplicates
  if (categoryInfo.suggestedFields && categoryInfo.suggestedFields.length > 0) {
    const baseFieldIds = new Set(baseFields.map(f => f.id));
    const uniqueTemplateFields = categoryInfo.suggestedFields.filter(field => !baseFieldIds.has(field.id));
    allFields = [...allFields, ...uniqueTemplateFields];
  }

  // Add financial fields at the end
  allFields = [...allFields, ...financialFields];

  return {
    fields: allFields,
    categoryInfo
  };
}
