/**
 * Equipment Templates for Different Equipment Types
 * 
 * This module provides table column configurations based on equipment type.
 * Each equipment type has specific fields that are relevant for tracking maintenance, 
 * calibration, condition, and operational status.
 * These templates will be used to dynamically generate table headers and data structure.
 */

export interface TableColumn {
  id: string;
  header: string;
  description: string;
  type: 'text' | 'number' | 'currency' | 'date' | 'select' | 'status' | 'hours';
  required: boolean;
  unit?: string;
}

export interface EquipmentTypeTemplate {
  equipment_type: string;
  display_name: string;
  description: string;
  typical_items: string[]; // Common items for this equipment type
  columns: TableColumn[];
}

/**
 * Equipment Templates Dictionary
 * Maps equipment types to their relevant table columns
 */
export const equipmentTemplates: Record<string, EquipmentTypeTemplate> = {
  welding_equipment: {
    equipment_type: 'welding_equipment',
    display_name: 'Equipo de Soldadura',
    description: 'Soldadoras, equipos de soldadura y accesorios relacionados',
    typical_items: ['Soldadora MIG', 'Soldadora TIG', 'Soldadora Arco', 'Antorcha', 'Reguladores'],
    columns: [
      {
        id: 'asset_tag',
        header: 'Etiqueta',
        description: 'Etiqueta de identificación del activo',
        type: 'text',
        required: true
      },
      {
        id: 'model',
        header: 'Modelo',
        description: 'Modelo del equipo',
        type: 'text',
        required: true
      },
      {
        id: 'serial_number',
        header: 'Número Serie',
        description: 'Número de serie del fabricante',
        type: 'text',
        required: true
      },
      {
        id: 'amperage_rating',
        header: 'Amperaje',
        description: 'Capacidad de amperaje',
        type: 'text',
        required: true
      },
      {
        id: 'condition_status',
        header: 'Estado',
        description: 'Condición operativa del equipo',
        type: 'status',
        required: true
      },
      {
        id: 'operating_hours',
        header: 'Horas Uso',
        description: 'Horas totales de operación',
        type: 'hours',
        required: true,
        unit: 'horas'
      },
      {
        id: 'last_maintenance',
        header: 'Último Mantenimiento',
        description: 'Fecha del último mantenimiento',
        type: 'date',
        required: true
      },
      {
        id: 'next_maintenance',
        header: 'Próximo Mantenimiento',
        description: 'Fecha programada del próximo mantenimiento',
        type: 'date',
        required: true
      },
      {
        id: 'acquisition_cost',
        header: 'Costo Adquisición',
        description: 'Costo original de adquisición',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'location',
        header: 'Ubicación',
        description: 'Ubicación actual del equipo',
        type: 'text',
        required: true
      }
    ]
  },

  cutting_machinery: {
    equipment_type: 'cutting_machinery',
    display_name: 'Maquinaria de Corte',
    description: 'Máquinas de corte, sierras, cortadoras y equipos de corte',
    typical_items: ['Sierra Cinta', 'Cortadora Plasma', 'Oxicorte', 'Sierra Circular', 'Cortadora Láser'],
    columns: [
      {
        id: 'asset_tag',
        header: 'Etiqueta',
        description: 'Etiqueta de identificación del activo',
        type: 'text',
        required: true
      },
      {
        id: 'model',
        header: 'Modelo',
        description: 'Modelo del equipo',
        type: 'text',
        required: true
      },
      {
        id: 'serial_number',
        header: 'Número Serie',
        description: 'Número de serie del fabricante',
        type: 'text',
        required: true
      },
      {
        id: 'cutting_capacity',
        header: 'Capacidad Corte',
        description: 'Capacidad máxima de corte',
        type: 'text',
        required: true
      },
      {
        id: 'power_rating',
        header: 'Potencia',
        description: 'Potencia nominal del motor',
        type: 'text',
        required: true
      },
      {
        id: 'condition_status',
        header: 'Estado',
        description: 'Condición operativa del equipo',
        type: 'status',
        required: true
      },
      {
        id: 'operating_hours',
        header: 'Horas Uso',
        description: 'Horas totales de operación',
        type: 'hours',
        required: true,
        unit: 'horas'
      },
      {
        id: 'last_maintenance',
        header: 'Último Mantenimiento',
        description: 'Fecha del último mantenimiento',
        type: 'date',
        required: true
      },
      {
        id: 'next_maintenance',
        header: 'Próximo Mantenimiento',
        description: 'Fecha programada del próximo mantenimiento',
        type: 'date',
        required: true
      },
      {
        id: 'acquisition_cost',
        header: 'Costo Adquisición',
        description: 'Costo original de adquisición',
        type: 'currency',
        required: true,
        unit: 'MXN'
      }
    ]
  },

  measuring_tools: {
    equipment_type: 'measuring_tools',
    display_name: 'Instrumentos de Medición',
    description: 'Herramientas de medición, calibradores y equipos de precisión',
    typical_items: ['Calibrador', 'Micrómetro', 'Escuadra', 'Nivel', 'Medidor de Espesores'],
    columns: [
      {
        id: 'asset_tag',
        header: 'Etiqueta',
        description: 'Etiqueta de identificación del activo',
        type: 'text',
        required: true
      },
      {
        id: 'model',
        header: 'Modelo',
        description: 'Modelo del instrumento',
        type: 'text',
        required: true
      },
      {
        id: 'serial_number',
        header: 'Número Serie',
        description: 'Número de serie del fabricante',
        type: 'text',
        required: true
      },
      {
        id: 'measurement_range',
        header: 'Rango Medición',
        description: 'Rango de medición del instrumento',
        type: 'text',
        required: true
      },
      {
        id: 'accuracy',
        header: 'Precisión',
        description: 'Precisión del instrumento',
        type: 'text',
        required: true
      },
      {
        id: 'calibration_status',
        header: 'Estado Calibración',
        description: 'Estado actual de calibración',
        type: 'status',
        required: true
      },
      {
        id: 'last_calibration',
        header: 'Última Calibración',
        description: 'Fecha de la última calibración',
        type: 'date',
        required: true
      },
      {
        id: 'next_calibration',
        header: 'Próxima Calibración',
        description: 'Fecha programada de la próxima calibración',
        type: 'date',
        required: true
      },
      {
        id: 'condition_status',
        header: 'Estado Físico',
        description: 'Condición física del instrumento',
        type: 'status',
        required: true
      },
      {
        id: 'acquisition_cost',
        header: 'Costo Adquisición',
        description: 'Costo original de adquisición',
        type: 'currency',
        required: true,
        unit: 'MXN'
      }
    ]
  },

  power_tools: {
    equipment_type: 'power_tools',
    display_name: 'Herramientas Eléctricas',
    description: 'Herramientas eléctricas portátiles y equipos de mano',
    typical_items: ['Taladro', 'Amoladora', 'Lijadora', 'Caladora', 'Martillo Neumático'],
    columns: [
      {
        id: 'asset_tag',
        header: 'Etiqueta',
        description: 'Etiqueta de identificación del activo',
        type: 'text',
        required: true
      },
      {
        id: 'model',
        header: 'Modelo',
        description: 'Modelo de la herramienta',
        type: 'text',
        required: true
      },
      {
        id: 'serial_number',
        header: 'Número Serie',
        description: 'Número de serie del fabricante',
        type: 'text',
        required: true
      },
      {
        id: 'power_rating',
        header: 'Potencia',
        description: 'Potencia nominal',
        type: 'text',
        required: true
      },
      {
        id: 'voltage',
        header: 'Voltaje',
        description: 'Voltaje de operación',
        type: 'text',
        required: true
      },
      {
        id: 'condition_status',
        header: 'Estado',
        description: 'Condición operativa de la herramienta',
        type: 'status',
        required: true
      },
      {
        id: 'operating_hours',
        header: 'Horas Uso',
        description: 'Horas totales de operación',
        type: 'hours',
        required: true,
        unit: 'horas'
      },
      {
        id: 'last_maintenance',
        header: 'Último Mantenimiento',
        description: 'Fecha del último mantenimiento',
        type: 'date',
        required: true
      },
      {
        id: 'next_maintenance',
        header: 'Próximo Mantenimiento',
        description: 'Fecha programada del próximo mantenimiento',
        type: 'date',
        required: true
      },
      {
        id: 'assigned_to',
        header: 'Asignado a',
        description: 'Persona o área asignada',
        type: 'text',
        required: false
      },
      {
        id: 'acquisition_cost',
        header: 'Costo Adquisición',
        description: 'Costo original de adquisición',
        type: 'currency',
        required: true,
        unit: 'MXN'
      }
    ]
  },

  safety_equipment: {
    equipment_type: 'safety_equipment',
    display_name: 'Equipo de Seguridad',
    description: 'Equipos de protección personal y seguridad industrial',
    typical_items: ['Casco Soldadura', 'Respirador', 'Arnés', 'Detector de Gases', 'Extinguidor'],
    columns: [
      {
        id: 'asset_tag',
        header: 'Etiqueta',
        description: 'Etiqueta de identificación del activo',
        type: 'text',
        required: true
      },
      {
        id: 'model',
        header: 'Modelo',
        description: 'Modelo del equipo',
        type: 'text',
        required: true
      },
      {
        id: 'certification',
        header: 'Certificación',
        description: 'Certificaciones de seguridad',
        type: 'text',
        required: true
      },
      {
        id: 'protection_level',
        header: 'Nivel Protección',
        description: 'Nivel de protección ofrecido',
        type: 'text',
        required: true
      },
      {
        id: 'condition_status',
        header: 'Estado',
        description: 'Condición del equipo de seguridad',
        type: 'status',
        required: true
      },
      {
        id: 'last_inspection',
        header: 'Última Inspección',
        description: 'Fecha de la última inspección',
        type: 'date',
        required: true
      },
      {
        id: 'next_inspection',
        header: 'Próxima Inspección',
        description: 'Fecha programada de la próxima inspección',
        type: 'date',
        required: true
      },
      {
        id: 'expiry_date',
        header: 'Fecha Vencimiento',
        description: 'Fecha de vencimiento del equipo',
        type: 'date',
        required: false
      },
      {
        id: 'assigned_to',
        header: 'Asignado a',
        description: 'Persona asignada',
        type: 'text',
        required: false
      },
      {
        id: 'acquisition_cost',
        header: 'Costo Adquisición',
        description: 'Costo original de adquisición',
        type: 'currency',
        required: true,
        unit: 'MXN'
      }
    ]
  }
};

/**
 * Mock fetch function to simulate API call for equipment type template
 * @param equipmentType - The equipment type to get template for
 * @returns Promise resolving to the equipment type template
 */
export async function fetchEquipmentTypeTemplate(equipmentType: string): Promise<EquipmentTypeTemplate | null> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
  
  const template = equipmentTemplates[equipmentType];
  
  if (!template) {
    console.warn(`Equipment type template for '${equipmentType}' not found, using default`);
    return equipmentTemplates.welding_equipment || null; // Default fallback
  }
  
  return template;
}

/**
 * Get all available equipment types
 * @returns Promise resolving to array of equipment type keys
 */
export async function fetchAvailableEquipmentTypes(): Promise<string[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 30));
  
  return Object.keys(equipmentTemplates);
}
