import { EntityType } from './openai';
import {
  TeamMember,
  Project,
  InventoryItem,
  FinancialRecord,
  CatalogItem,
  FinancialMovement,
  PaymentMethod,
  RecurringPaymentDetails,
  ProjectModuleType,
  ModuleConfiguration
} from '../contexts/types';

// Data store interface for managing entities
export interface DataStore {
  addTeamMember: (data: Partial<TeamMember>) => TeamMember;
  addProject: (data: Partial<Project>) => Project;
  addInventoryItem: (data: Partial<InventoryItem>) => InventoryItem;
  addFinancialRecord: (data: Partial<FinancialRecord>) => FinancialRecord;
  addCatalogItem: (data: Partial<CatalogItem>) => CatalogItem;
  deleteTeamMember: (id: string) => TeamMember | null;
  deleteProject: (id: string) => Project | null;
  deleteInventoryItem: (id: string) => InventoryItem | null;
  deleteFinancialRecord: (id: string) => FinancialRecord | null;
  deleteCatalogItem: (id: string) => CatalogItem | null;
}

// Generate unique ID
function generateId(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

// Generate current date string
function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0] || new Date().toISOString();
}

// Category ID mapping for inventory items
const INVENTORY_CATEGORY_MAP: Record<string, string> = {
  'Materia Prima': 'raw_materials',
  'Herramientas y Equipos': 'tools_equipment',
  'Consumibles': 'consumables',
  'Productos Terminados': 'finished_products'
};

/**
 * Create a new team member from form data
 */
export function createTeamMember(data: Record<string, any>): TeamMember {
  const teamMember: TeamMember = {
    id: generateId('team'),
    name: data.name || '',
    role: data.role || '',
    email: data.email || '',
    status: data.status || 'active',
    salary: Number(data.salary) || 0,
    currency: data.currency || 'USD',
    // Optional fields
    avatar: undefined, // Could be added later
    hourlyRate: data.hourlyRate ? Number(data.hourlyRate) : undefined,
    skills: data.skills ? data.skills.split(',').map((s: string) => s.trim()) : undefined
  };

  return teamMember;
}

/**
 * Create default module configuration based on enabled modules
 */
function createDefaultModuleConfiguration(enabledModules: ProjectModuleType[]): ModuleConfiguration {
  const config: ModuleConfiguration = {};

  if (enabledModules.includes('catalog')) {
    config.catalog = {
      enableProducts: true,
      enableServices: true,
      productCategories: [],
      serviceTypes: []
    };
  }

  if (enabledModules.includes('team')) {
    config.team = {
      trackHours: true,
      enableSkillsManagement: false,
      enableCostTracking: true
    };
  }

  if (enabledModules.includes('timeline')) {
    config.timeline = {
      enableGanttView: true,
      enableMilestones: true,
      trackDependencies: false
    };
  }

  if (enabledModules.includes('inventory')) {
    config.inventory = {
      trackMaterials: true,
      enableBOM: true,
      trackSuppliers: false
    };
  }

  if (enabledModules.includes('logistics')) {
    config.logistics = {
      trackShipments: false,
      enableSupplyChain: false,
      trackDeliveries: true
    };
  }

  return config;
}

/**
 * Convert guided conversation data to project creation data
 */
export function convertGuidedDataToProjectData(guidedData: Record<string, any>): Record<string, any> {
  const projectData: Record<string, any> = {
    // Basic info
    name: guidedData.name || '',
    client: guidedData.client || '',
    description: guidedData.description || '',
    status: 'planning',
    startDate: guidedData.startDate || getCurrentDate(),
    endDate: guidedData.endDate || undefined,

    // Determine enabled modules based on guided responses
    enabledModules: ['finance'] // Always include finance
  };

  // Add modules based on guided responses
  if (guidedData.offeringType && (guidedData.offeringType === 'products' || guidedData.offeringType === 'services' || guidedData.offeringType === 'both')) {
    projectData.enabledModules.push('catalog');
  }

  if (guidedData.teamMembers || guidedData.teamRoles) {
    projectData.enabledModules.push('team');
  }

  if (guidedData.needsTimeline === 'yes' || !guidedData.needsTimeline) { // Default to yes
    projectData.enabledModules.push('timeline');
  }

  if (guidedData.needsInventory === 'yes') {
    projectData.enabledModules.push('inventory');
  }

  if (guidedData.needsLogistics === 'yes') {
    projectData.enabledModules.push('logistics');
  }

  return projectData;
}

/**
 * Create a new project from form data
 */
export function createProject(data: Record<string, any>): Project {
  // Finance module is always enabled
  const enabledModules: ProjectModuleType[] = ['finance'];

  // Add modules based on form data - only add explicitly requested modules
  if (data.enabledModules && Array.isArray(data.enabledModules)) {
    data.enabledModules.forEach((module: string) => {
      if (!enabledModules.includes(module as ProjectModuleType)) {
        enabledModules.push(module as ProjectModuleType);
      }
    });
  }
  // No default modules - only finance is enabled by default
  // Other modules will be added via edit operations

  const project: Project = {
    id: generateId('proj'),
    name: data.name || '',
    description: data.description || '',
    status: data.status || 'planning',
    startDate: data.startDate || getCurrentDate(),
    endDate: data.endDate || undefined,
    teamMembers: [], // Will be empty initially
    tasks: [], // Will be empty initially
    modules: {
      enabled: enabledModules,
      configuration: createDefaultModuleConfiguration(enabledModules),
      aiRecommended: data.aiRecommendedModules || [],
      userOverrides: data.userOverrideModules || []
    }
  };

  return project;
}

/**
 * Create a new inventory item from form data
 */
export function createInventoryItem(data: Record<string, any>): InventoryItem {
  const categoryId = INVENTORY_CATEGORY_MAP[data.category] || 'raw_materials';
  
  const inventoryItem: InventoryItem = {
    id: generateId('inv'),
    name: data.name || '',
    description: data.description || '',
    quantity: Number(data.quantity) || 0,
    unit: data.unit || 'piezas',
    category: data.category || 'Materia Prima',
    categoryId: categoryId,
    cost: data.cost ? Number(data.cost) : 0,
    location: data.location || undefined,
    supplier: data.supplier || undefined,
    lastUpdated: getCurrentDate(),
    // Additional fields can be added based on category
    additionalFields: {}
  };

  return inventoryItem;
}

/**
 * Create a new financial record from form data
 */
export function createFinancialRecord(data: Record<string, any>): FinancialRecord {
  const financialRecord: FinancialRecord = {
    id: generateId('fin'),
    type: data.type || 'expense',
    amount: Number(data.amount) || 0,
    description: data.description || '',
    date: data.date || getCurrentDate(),
    category: data.category || ''
  };

  return financialRecord;
}

/**
 * Create a new financial movement from form data (includes payment method and recurring details)
 */
export function createFinancialMovement(data: Record<string, any>): FinancialMovement {
  // Create recurring payment details if applicable
  let recurringDetails: RecurringPaymentDetails | undefined;
  if (data.isRecurring === 'yes' && data.monthlyAmount && data.totalMonths) {
    recurringDetails = {
      monthlyAmount: Number(data.monthlyAmount),
      totalMonths: Number(data.totalMonths),
      remainingMonths: Number(data.totalMonths), // Initially all months remain
      interestRate: data.interestRate ? Number(data.interestRate) : undefined,
      startDate: data.date || getCurrentDate(),
      nextDueDate: data.dueDate || getCurrentDate()
    };
  }

  const financialMovement: FinancialMovement = {
    id: generateId('fin'),
    fecha: data.date || getCurrentDate(),
    concepto: data.description || '',
    monto: Number(data.amount) || 0,
    tipo: data.type === 'income' ? 'Entrada' : 'Salida',
    categoria: data.category || '',
    comportamiento: data.isRecurring === 'yes' ? 'Recurrente' : 'Fijo',
    comprobante: '🗂',
    paymentMethod: data.paymentMethod as PaymentMethod || 'cash',
    isRecurring: data.isRecurring === 'yes',
    recurringDetails,
    dueDate: data.dueDate
  };

  return financialMovement;
}

/**
 * Create a new catalog item from form data
 */
export function createCatalogItem(data: Record<string, any>): CatalogItem {
  const catalogItem: CatalogItem = {
    id: generateId('cat'),
    productName: data.productName || '',
    productDescription: data.productDescription || '',
    categoryLabel: data.categoryLabel || 'Product',
    // Optional fields
    imageSrc: undefined,
    details: {
      title: data.productName || '',
      type: data.categoryLabel || 'Product',
      documents: [],
      images: []
    }
  };

  return catalogItem;
}

/**
 * Create entity based on type and form data
 */
export function createEntity(entityType: EntityType, data: Record<string, any>): any {
  switch (entityType) {
    case 'employee':
      return createTeamMember(data);
    case 'project':
      return createProject(data);
    case 'inventory':
      return createInventoryItem(data);
    case 'financial':
      return createFinancialRecord(data);
    case 'catalog':
      return createCatalogItem(data);
    default:
      throw new Error(`Unsupported entity type: ${entityType}`);
  }
}

/**
 * Delete entity based on type and ID using context functions
 */
export function deleteEntity(
  entityType: EntityType,
  id: string,
  deletionFunctions: {
    removeTeamMember?: (id: string) => TeamMember | null;
    removeProject?: (id: string) => Project | null;
    removeInventoryItem?: (id: string) => InventoryItem | null;
    removeFinancialRecord?: (id: string) => FinancialRecord | null;
    removeCatalogItem?: (id: string) => CatalogItem | null;
  }
): any | null {
  switch (entityType) {
    case 'employee':
      return deletionFunctions.removeTeamMember?.(id) || null;
    case 'project':
      return deletionFunctions.removeProject?.(id) || null;
    case 'inventory':
      return deletionFunctions.removeInventoryItem?.(id) || null;
    case 'financial':
      return deletionFunctions.removeFinancialRecord?.(id) || null;
    case 'catalog':
      return deletionFunctions.removeCatalogItem?.(id) || null;
    default:
      throw new Error(`Unsupported entity type: ${entityType}`);
  }
}

/**
 * Validate entity data before creation
 */
export function validateEntityData(entityType: EntityType, data: Record<string, any>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  switch (entityType) {
    case 'employee':
      if (!data.name?.trim()) errors.push('Name is required');
      if (!data.role?.trim()) errors.push('Role is required');
      if (!data.email?.trim()) errors.push('Email is required');
      if (!data.salary || isNaN(Number(data.salary))) errors.push('Valid salary is required');
      break;

    case 'project':
      if (!data.name?.trim()) errors.push('Project name is required');
      if (!data.description?.trim()) errors.push('Description is required');
      if (!data.startDate) errors.push('Start date is required');
      break;

    case 'inventory':
      if (!data.name?.trim()) errors.push('Item name is required');
      if (!data.description?.trim()) errors.push('Description is required');
      if (!data.quantity || isNaN(Number(data.quantity))) errors.push('Valid quantity is required');
      break;

    case 'financial':
      if (!data.description?.trim()) errors.push('Description is required');
      if (!data.amount || isNaN(Number(data.amount))) errors.push('Valid amount is required');
      if (!data.category?.trim()) errors.push('Category is required');
      break;

    case 'catalog':
      if (!data.productName?.trim()) errors.push('Product name is required');
      if (!data.productDescription?.trim()) errors.push('Product description is required');
      break;

    default:
      errors.push(`Unsupported entity type: ${entityType}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get success message for entity creation
 */
export function getSuccessMessage(entityType: EntityType, entityName: string): string {
  const messages: Record<EntityType, string> = {
    employee: `Employee "${entityName}" has been added to your team.`,
    project: `Project "${entityName}" has been created successfully.`,
    inventory: `Inventory item "${entityName}" has been added to your stock.`,
    financial: `Financial record "${entityName}" has been recorded.`,
    catalog: `Catalog item "${entityName}" has been added to your catalog.`
  };

  return messages[entityType] || `${entityType} "${entityName}" has been created.`;
}
