import { EntityType } from './openai';
import { 
  TeamMember, 
  Project, 
  InventoryItem, 
  FinancialRecord, 
  CatalogItem 
} from '../contexts/types';

export interface DeletionContext {
  reason?: 'sold' | 'disposed' | 'transferred' | 'expired' | 'damaged' | 'other';
  notes?: string;
  transferTo?: string; // For transfers
  salePrice?: number; // For sales
}

export interface DeletionResult {
  success: boolean;
  deletedEntity?: any;
  error?: string;
  financialImpact?: {
    shouldCreateMovement: boolean;
    movementType?: 'income' | 'expense';
    amount?: number;
    description?: string;
  };
}

/**
 * Find entity by name or partial match
 */
export function findEntityByName<T extends { name?: string; productName?: string; id: string }>(
  entities: T[],
  searchName: string
): T | null {
  if (!searchName || !entities.length) return null;

  const normalizedSearch = searchName.toLowerCase().trim();

  // First try exact match on name or productName
  let found = entities.find(entity => {
    const entityName = (entity.name || entity.productName || '').toLowerCase();
    return entityName === normalizedSearch;
  });

  if (found) return found;

  // Then try partial match
  found = entities.find(entity => {
    const entityName = (entity.name || entity.productName || '').toLowerCase();
    return entityName.includes(normalizedSearch) || normalizedSearch.includes(entityName);
  });

  return found || null;
}

/**
 * Validate if entity can be deleted
 */
export function validateDeletion(
  entityType: EntityType,
  entity: any,
  context?: DeletionContext
): { canDelete: boolean; warnings: string[]; errors: string[] } {
  const warnings: string[] = [];
  const errors: string[] = [];

  switch (entityType) {
    case 'inventory':
      const item = entity as InventoryItem;
      if (item.quantity > 0) {
        warnings.push(`This item has ${item.quantity} ${item.unit} in stock. Deleting will remove all inventory.`);
      }
      if (item.cost && item.cost > 0) {
        warnings.push(`This item has a cost of $${item.cost}. Consider the financial impact.`);
      }
      break;

    case 'employee':
      const employee = entity as TeamMember;
      if (employee.status === 'active') {
        warnings.push('This employee is currently active. Consider changing status to inactive first.');
      }
      break;

    case 'project':
      const project = entity as Project;
      if (project.status === 'in-progress') {
        errors.push('Cannot delete a project that is currently in progress. Complete or cancel the project first.');
      }
      break;

    case 'financial':
      warnings.push('Deleting financial records may affect your accounting. Ensure this is intentional.');
      break;

    case 'catalog':
      // Catalog items can generally be deleted safely
      break;

    default:
      warnings.push('Deletion validation not implemented for this entity type.');
  }

  return {
    canDelete: errors.length === 0,
    warnings,
    errors
  };
}

/**
 * Calculate financial impact of deletion
 */
export function calculateFinancialImpact(
  entityType: EntityType,
  entity: any,
  context?: DeletionContext
): DeletionResult['financialImpact'] {
  if (entityType !== 'inventory') {
    return { shouldCreateMovement: false };
  }

  const item = entity as InventoryItem;
  
  // Only create financial movements for items with cost and quantity
  if (!item.cost || item.cost <= 0 || !item.quantity || item.quantity <= 0) {
    return { shouldCreateMovement: false };
  }

  const totalValue = item.cost * item.quantity;

  switch (context?.reason) {
    case 'sold':
      return {
        shouldCreateMovement: true,
        movementType: 'income',
        amount: context.salePrice || totalValue,
        description: `Venta de ${item.name} (${item.quantity} ${item.unit})`
      };

    case 'disposed':
    case 'expired':
    case 'damaged':
      return {
        shouldCreateMovement: true,
        movementType: 'expense',
        amount: totalValue,
        description: `Pérdida por ${context.reason} - ${item.name} (${item.quantity} ${item.unit})`
      };

    case 'transferred':
      return {
        shouldCreateMovement: true,
        movementType: 'expense',
        amount: totalValue,
        description: `Transferencia de ${item.name} a ${context.transferTo || 'otro almacén'}`
      };

    default:
      // For general deletion, treat as inventory adjustment
      return {
        shouldCreateMovement: true,
        movementType: 'expense',
        amount: totalValue,
        description: `Ajuste de inventario - eliminación de ${item.name}`
      };
  }
}

/**
 * Prepare deletion data for confirmation
 */
export function prepareDeletionData(
  entityType: EntityType,
  searchData: Record<string, any>
): {
  searchCriteria: string;
  entityDisplayName: string;
} {
  const searchName = searchData.name || searchData.productName || searchData.description || '';
  
  const entityDisplayNames: Record<EntityType, string> = {
    employee: 'Employee',
    project: 'Project', 
    inventory: 'Inventory Item',
    financial: 'Financial Record',
    catalog: 'Catalog Item'
  };

  return {
    searchCriteria: searchName,
    entityDisplayName: entityDisplayNames[entityType]
  };
}

/**
 * Generate deletion confirmation message
 */
export function generateDeletionMessage(
  entityType: EntityType,
  entity: any,
  validation: ReturnType<typeof validateDeletion>
): string {
  const entityName = entity.name || entity.productName || entity.description || 'Unknown';
  const entityDisplayName = prepareDeletionData(entityType, {}).entityDisplayName;

  let message = `Are you sure you want to delete ${entityDisplayName.toLowerCase()} "${entityName}"?`;

  if (validation.warnings.length > 0) {
    message += '\n\nWarnings:\n' + validation.warnings.map(w => `• ${w}`).join('\n');
  }

  if (validation.errors.length > 0) {
    message += '\n\nErrors:\n' + validation.errors.map(e => `• ${e}`).join('\n');
  }

  return message;
}
