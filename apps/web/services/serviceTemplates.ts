/**
 * Service Templates for Fabrication Business
 * 
 * This module provides templates for different fabrication services.
 * Each template defines the fields required to specify that service.
 * These templates will be used by AI systems to dynamically build service forms.
 */

export interface ServiceField {
  name: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean';
  required: boolean;
  options?: string[]; // For select/multiselect fields
  unit?: string; // For number fields
  description?: string;
}

export interface ServiceTemplate {
  display_name: string;
  description: string;
  category: 'cutting' | 'forming' | 'joining' | 'finishing' | 'assembly';
  fields: ServiceField[];
  typical_duration: string;
  pricing_model: 'per_piece' | 'per_hour' | 'per_area' | 'per_weight' | 'fixed';
}

/**
 * Service Templates Dictionary
 * Contains fabrication-specific service templates
 */
export const serviceTemplates: Record<string, ServiceTemplate> = {
  laser_cutting: {
    display_name: "Corte Láser",
    description: "Servicio de corte de precisión con tecnología láser",
    category: "cutting",
    typical_duration: "1-3 días",
    pricing_model: "per_area",
    fields: [
      {
        name: "Material",
        type: "select",
        required: true,
        options: ["Acero al carbón", "Acero inoxidable", "Aluminio", "Latón", "Cobre"],
        description: "Tipo de material a cortar"
      },
      {
        name: "Espesor",
        type: "number",
        required: true,
        unit: "mm",
        description: "Espesor del material en milímetros"
      },
      {
        name: "Dimensiones",
        type: "text",
        required: true,
        description: "Dimensiones de la pieza (largo x ancho)"
      },
      {
        name: "Cantidad",
        type: "number",
        required: true,
        unit: "piezas",
        description: "Número de piezas a cortar"
      },
      {
        name: "Acabado",
        type: "select",
        required: false,
        options: ["Sin acabado", "Desbarbado", "Pulido", "Biselado"],
        description: "Tipo de acabado en los bordes"
      },
      {
        name: "Tolerancia",
        type: "select",
        required: false,
        options: ["±0.1mm", "±0.2mm", "±0.5mm", "±1.0mm"],
        description: "Tolerancia dimensional requerida"
      }
    ]
  },

  metal_bending: {
    display_name: "Doblado de Metal",
    description: "Servicio de conformado y doblado de láminas metálicas",
    category: "forming",
    typical_duration: "2-5 días",
    pricing_model: "per_piece",
    fields: [
      {
        name: "Material",
        type: "select",
        required: true,
        options: ["Acero al carbón", "Acero inoxidable", "Aluminio", "Galvanizado"],
        description: "Tipo de material a doblar"
      },
      {
        name: "Espesor",
        type: "number",
        required: true,
        unit: "mm",
        description: "Espesor del material"
      },
      {
        name: "Angulo_Doblado",
        type: "number",
        required: true,
        unit: "grados",
        description: "Ángulo de doblado requerido"
      },
      {
        name: "Radio_Doblado",
        type: "number",
        required: false,
        unit: "mm",
        description: "Radio interno del doblez"
      },
      {
        name: "Longitud_Doblez",
        type: "number",
        required: true,
        unit: "mm",
        description: "Longitud de la línea de doblez"
      },
      {
        name: "Cantidad_Dobleces",
        type: "number",
        required: true,
        unit: "dobleces",
        description: "Número de dobleces por pieza"
      },
      {
        name: "Cantidad_Piezas",
        type: "number",
        required: true,
        unit: "piezas",
        description: "Número total de piezas"
      }
    ]
  },

  welding_service: {
    display_name: "Soldadura",
    description: "Servicios de soldadura y unión de metales",
    category: "joining",
    typical_duration: "3-7 días",
    pricing_model: "per_hour",
    fields: [
      {
        name: "Proceso",
        type: "select",
        required: true,
        options: ["MIG", "TIG", "Electrodo", "Arco sumergido", "Oxiacetileno"],
        description: "Proceso de soldadura a utilizar"
      },
      {
        name: "Material",
        type: "select",
        required: true,
        options: ["Acero al carbón", "Acero inoxidable", "Aluminio", "Hierro fundido"],
        description: "Material de las piezas a soldar"
      },
      {
        name: "Espesor",
        type: "number",
        required: true,
        unit: "mm",
        description: "Espesor del material base"
      },
      {
        name: "Junta",
        type: "select",
        required: true,
        options: ["A tope", "En T", "Traslape", "Esquina", "Borde"],
        description: "Tipo de junta a soldar"
      },
      {
        name: "Longitud",
        type: "number",
        required: true,
        unit: "metros",
        description: "Longitud total de soldadura"
      },
      {
        name: "Posición",
        type: "select",
        required: true,
        options: ["Plana", "Horizontal", "Vertical", "Sobrecabeza"],
        description: "Posición de soldadura"
      },
      {
        name: "Certificación",
        type: "boolean",
        required: false,
        description: "¿Se requiere certificación de soldadura?"
      }
    ]
  },

  powder_coating: {
    display_name: "Pintura Electrostática",
    description: "Servicio de recubrimiento con pintura en polvo",
    category: "finishing",
    typical_duration: "5-10 días",
    pricing_model: "per_area",
    fields: [
      {
        name: "Material",
        type: "select",
        required: true,
        options: ["Acero", "Aluminio", "Hierro", "Zinc"],
        description: "Material de la pieza a recubrir"
      },
      {
        name: "Color",
        type: "select",
        required: true,
        options: ["Blanco", "Negro", "Gris", "Azul", "Rojo", "Verde", "Amarillo", "Personalizado"],
        description: "Color del recubrimiento"
      },
      {
        name: "Acabado",
        type: "select",
        required: true,
        options: ["Mate", "Semibrillo", "Brillante", "Texturizado", "Metalizado"],
        description: "Tipo de acabado superficial"
      },
      {
        name: "Área",
        type: "number",
        required: true,
        unit: "m²",
        description: "Área total a recubrir"
      },
      {
        name: "Espesor",
        type: "number",
        required: false,
        unit: "micrones",
        description: "Espesor del recubrimiento (60-120 micrones típico)"
      },
      {
        name: "Cantidad",
        type: "number",
        required: true,
        unit: "piezas",
        description: "Número de piezas a recubrir"
      }
    ]
  },

  machining_service: {
    display_name: "Maquinado CNC",
    description: "Servicio de maquinado de precisión con control numérico",
    category: "forming",
    typical_duration: "3-14 días",
    pricing_model: "per_hour",
    fields: [
      {
        name: "Tipo_Maquinado",
        type: "multiselect",
        required: true,
        options: ["Torneado", "Fresado", "Taladrado", "Roscado", "Ranurado"],
        description: "Operaciones de maquinado requeridas"
      },
      {
        name: "Material",
        type: "select",
        required: true,
        options: ["Acero 1018", "Acero 4140", "Acero inoxidable 304", "Aluminio 6061", "Latón", "Bronce"],
        description: "Material de la pieza"
      },
      {
        name: "Dimensiones_Iniciales",
        type: "text",
        required: true,
        description: "Dimensiones del material en bruto (largo x ancho x alto)"
      },
      {
        name: "Tolerancia_Dimensional",
        type: "select",
        required: true,
        options: ["±0.01mm", "±0.02mm", "±0.05mm", "±0.1mm", "±0.2mm"],
        description: "Tolerancia dimensional requerida"
      },
      {
        name: "Acabado_Superficial",
        type: "select",
        required: true,
        options: ["Ra 0.8", "Ra 1.6", "Ra 3.2", "Ra 6.3", "Ra 12.5"],
        description: "Rugosidad superficial requerida (Ra en micrones)"
      },
      {
        name: "Cantidad",
        type: "number",
        required: true,
        unit: "piezas",
        description: "Cantidad de piezas a maquinar"
      },
      {
        name: "Tratamiento_Termico",
        type: "select",
        required: false,
        options: ["Sin tratamiento", "Temple", "Revenido", "Normalizado", "Cementado"],
        description: "Tratamiento térmico posterior"
      }
    ]
  },

  structural_design: {
    display_name: "Diseño Estructural",
    description: "Servicio de análisis y diseño de estructuras",
    category: "assembly",
    typical_duration: "2-4 semanas",
    pricing_model: "fixed",
    fields: [
      {
        name: "Tipo_Proyecto",
        type: "select",
        required: true,
        options: ["Edificio residencial", "Edificio comercial", "Nave industrial", "Puente", "Torre", "Estructura especial"],
        description: "Tipo de proyecto estructural"
      },
      {
        name: "Material_Estructura",
        type: "select",
        required: true,
        options: ["Concreto armado", "Acero estructural", "Madera", "Mixto (concreto-acero)", "Mampostería"],
        description: "Material principal de la estructura"
      },
      {
        name: "Area_Construccion",
        type: "number",
        required: true,
        unit: "m²",
        description: "Área total de construcción"
      },
      {
        name: "Numero_Niveles",
        type: "number",
        required: true,
        unit: "niveles",
        description: "Número de niveles o pisos"
      },
      {
        name: "Carga_Viva",
        type: "number",
        required: true,
        unit: "kg/m²",
        description: "Carga viva de diseño"
      },
      {
        name: "Zona_Sismica",
        type: "select",
        required: true,
        options: ["Zona A (baja)", "Zona B (intermedia)", "Zona C (alta)", "Zona D (muy alta)"],
        description: "Zona sísmica según normativa local"
      },
      {
        name: "Tipo_Suelo",
        type: "select",
        required: true,
        options: ["Roca", "Suelo firme", "Suelo blando", "Suelo muy blando", "Requiere estudio"],
        description: "Tipo de suelo según estudio geotécnico"
      },
      {
        name: "Normativa",
        type: "select",
        required: true,
        options: ["ACI 318", "AISC 360", "NSR-10", "CFE", "Eurocódigo", "Otra"],
        description: "Código o normativa de diseño aplicable"
      },
      {
        name: "Incluye_Cimentacion",
        type: "boolean",
        required: true,
        description: "¿Incluye diseño de cimentación?"
      },
      {
        name: "Software_Analisis",
        type: "select",
        required: false,
        options: ["SAP2000", "ETABS", "STAAD.Pro", "Robot Structural", "CYPECAD", "Otro"],
        description: "Software preferido para análisis estructural"
      }
    ]
  },

  consulting: {
    display_name: "Consultoría en Fabricación",
    description: "Servicios de consultoría para optimización de procesos de fabricación",
    category: "assembly",
    typical_duration: "1-3 meses",
    pricing_model: "per_hour",
    fields: [
      {
        name: "Area_Consultoria",
        type: "multiselect",
        required: true,
        options: ["Optimización de procesos", "Control de calidad", "Reducción de costos", "Automatización", "Lean Manufacturing", "Gestión de inventarios"],
        description: "Áreas específicas de consultoría"
      },
      {
        name: "Tipo_Industria",
        type: "select",
        required: true,
        options: ["Metalmecánica", "Automotriz", "Aeroespacial", "Construcción", "Energía", "Manufactura general"],
        description: "Sector industrial del cliente"
      },
      {
        name: "Tamaño_Empresa",
        type: "select",
        required: true,
        options: ["Micro (1-10 empleados)", "Pequeña (11-50 empleados)", "Mediana (51-250 empleados)", "Grande (250+ empleados)"],
        description: "Tamaño de la empresa cliente"
      },
      {
        name: "Duracion_Proyecto",
        type: "select",
        required: true,
        options: ["1 mes", "2-3 meses", "4-6 meses", "6+ meses"],
        description: "Duración estimada del proyecto"
      },
      {
        name: "Modalidad",
        type: "select",
        required: true,
        options: ["Presencial", "Remoto", "Híbrido"],
        description: "Modalidad de trabajo"
      },
      {
        name: "Incluye_Capacitacion",
        type: "boolean",
        required: true,
        description: "¿Incluye capacitación del personal?"
      },
      {
        name: "Entregables_Esperados",
        type: "multiselect",
        required: true,
        options: ["Diagnóstico inicial", "Plan de mejora", "Procedimientos documentados", "Indicadores KPI", "Capacitación", "Seguimiento post-implementación"],
        description: "Entregables esperados del proyecto"
      }
    ]
  }
};

/**
 * Mock fetch function to simulate API call for service templates
 * @param serviceType - The type of service template to fetch
 * @returns Promise resolving to the service template
 */
export async function fetchServiceTemplate(serviceType: string): Promise<ServiceTemplate | null> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  
  const template = serviceTemplates[serviceType];
  
  if (!template) {
    throw new Error(`Service template '${serviceType}' not found`);
  }
  
  return template;
}

/**
 * Get all available service types
 * @returns Promise resolving to array of service type keys
 */
export async function fetchAvailableServiceTypes(): Promise<string[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50));
  
  return Object.keys(serviceTemplates);
}

/**
 * Get service templates by category
 * @param category - The category to filter by
 * @returns Promise resolving to filtered service templates
 */
export async function fetchServiceTemplatesByCategory(category: ServiceTemplate['category']): Promise<Record<string, ServiceTemplate>> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const filtered: Record<string, ServiceTemplate> = {};
  
  for (const [key, template] of Object.entries(serviceTemplates)) {
    if (template.category === category) {
      filtered[key] = template;
    }
  }
  
  return filtered;
}
