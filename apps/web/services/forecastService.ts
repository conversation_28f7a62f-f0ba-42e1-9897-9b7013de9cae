import { ForecastEntry, FinancialMovement, RecurringPaymentDetails } from '../contexts/types';

// Generate unique ID for forecast entries
function generateForecastId(): string {
  return `forecast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Add months to a date string (YYYY-MM-DD format)
function addMonthsToDate(dateString: string, months: number): string {
  const date = new Date(dateString);
  date.setMonth(date.getMonth() + months);
  return date.toISOString().split('T')[0] || '';
}

/**
 * Generate forecast entries for recurring payments
 * This creates monthly forecast entries for the duration of the recurring payment
 */
export function generateRecurringPaymentForecasts(
  movement: FinancialMovement
): ForecastEntry[] {
  if (!movement.isRecurring || !movement.recurringDetails) {
    return [];
  }

  const { monthlyAmount, totalMonths, startDate } = movement.recurringDetails;
  const forecasts: ForecastEntry[] = [];

  // Generate forecast entries for each month
  for (let monthNumber = 1; monthNumber <= totalMonths; monthNumber++) {
    const forecastDate = addMonthsToDate(startDate, monthNumber - 1);
    const remainingMonths = totalMonths - monthNumber;

    const forecast: ForecastEntry = {
      id: generateForecastId(),
      date: forecastDate,
      concepto: `${movement.concepto} - Pago ${monthNumber}/${totalMonths}`,
      monto: monthlyAmount,
      tipo: movement.tipo,
      categoria: movement.categoria,
      sourceType: 'recurring_payment',
      sourceId: movement.id,
      isConfirmed: false,
      recurringPaymentInfo: {
        monthNumber,
        totalMonths,
        remainingMonths
      }
    };

    forecasts.push(forecast);
  }

  return forecasts;
}

/**
 * Generate forecast entries from a financial movement
 * This is the main function to call when adding new financial movements
 */
export function generateForecastsFromMovement(
  movement: FinancialMovement
): ForecastEntry[] {
  const forecasts: ForecastEntry[] = [];

  // Generate recurring payment forecasts if applicable
  if (movement.isRecurring && movement.recurringDetails) {
    const recurringForecasts = generateRecurringPaymentForecasts(movement);
    forecasts.push(...recurringForecasts);
  }

  return forecasts;
}

/**
 * Update forecast entries when a recurring payment is made
 * This marks the current month as confirmed and updates remaining months
 */
export function updateForecastsForPayment(
  forecasts: ForecastEntry[],
  paymentSourceId: string,
  paymentDate: string
): ForecastEntry[] {
  return forecasts.map(forecast => {
    if (forecast.sourceId === paymentSourceId && forecast.date === paymentDate) {
      return {
        ...forecast,
        isConfirmed: true
      };
    }
    return forecast;
  });
}

/**
 * Get forecasts for a specific month
 */
export function getForecastsForMonth(
  forecasts: ForecastEntry[],
  year: number,
  month: number
): ForecastEntry[] {
  const monthStr = month.toString().padStart(2, '0');
  const yearMonthPrefix = `${year}-${monthStr}`;
  
  return forecasts.filter(forecast => 
    forecast.date.startsWith(yearMonthPrefix)
  );
}

/**
 * Get forecasts for a date range
 */
export function getForecastsForDateRange(
  forecasts: ForecastEntry[],
  startDate: string,
  endDate: string
): ForecastEntry[] {
  return forecasts.filter(forecast => 
    forecast.date >= startDate && forecast.date <= endDate
  );
}

/**
 * Calculate total forecast amount for a specific period
 */
export function calculateForecastTotal(
  forecasts: ForecastEntry[],
  type?: 'Entrada' | 'Salida'
): number {
  return forecasts
    .filter(forecast => !type || forecast.tipo === type)
    .reduce((total, forecast) => total + forecast.monto, 0);
}

/**
 * Remove forecasts associated with a specific source (e.g., when deleting a recurring payment)
 */
export function removeForecastsBySource(
  forecasts: ForecastEntry[],
  sourceId: string
): ForecastEntry[] {
  return forecasts.filter(forecast => forecast.sourceId !== sourceId);
}
