import { FinancialRecord, FinancialMovement, InventoryItem, PaymentMethod, RecurringPaymentDetails } from '../contexts/types';
import { DeletionContext } from './deletionService';

// Generate unique ID for financial records
function generateFinancialId(): string {
  return `fin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Get current date in YYYY-MM-DD format
function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0] || '';
}

// Map inventory categories to financial categories
const INVENTORY_TO_FINANCE_CATEGORY_MAP: Record<string, string> = {
  'Materia Prima': 'Materiales',
  'Herramientas y Equipos': 'Equipos',
  'Consumibles': 'Consumibles',
  'Productos Terminados': 'Inventario'
};

// Determine transaction type based on context
export type TransactionContext = 'purchase' | 'sale' | 'adjustment' | 'transfer';

export interface InventoryTransactionData {
  item: InventoryItem;
  context: TransactionContext;
  projectId?: string;
  supplierId?: string;
  customDescription?: string;
  paymentMethod?: PaymentMethod;
  isRecurring?: boolean;
  recurringDetails?: RecurringPaymentDetails;
  dueDate?: string;
}

/**
 * Create a financial record from an inventory transaction
 */
export function createFinancialRecordFromInventory(
  transactionData: InventoryTransactionData
): FinancialRecord | null {
  const { item, context, projectId, supplierId, customDescription } = transactionData;
  
  // Only create financial records for transactions with cost
  if (!item.cost || item.cost <= 0) {
    return null;
  }

  // Calculate total amount (cost * quantity)
  const totalAmount = item.cost * item.quantity;

  // Determine transaction type and description
  let type: 'income' | 'expense';
  let description: string;
  
  switch (context) {
    case 'purchase':
      type = 'expense';
      description = customDescription || `Compra de ${item.name}`;
      break;
    case 'sale':
      type = 'income';
      description = customDescription || `Venta de ${item.name}`;
      break;
    case 'adjustment':
      // For adjustments, we'll treat as expense if it's an increase in inventory
      type = 'expense';
      description = customDescription || `Ajuste de inventario - ${item.name}`;
      break;
    case 'transfer':
      // Transfers don't typically create financial movements unless there's a cost
      type = 'expense';
      description = customDescription || `Transferencia de ${item.name}`;
      break;
    default:
      type = 'expense';
      description = customDescription || `Transacción de ${item.name}`;
  }

  // Map inventory category to financial category
  const financialCategory = INVENTORY_TO_FINANCE_CATEGORY_MAP[item.category] || 'Otros';

  const financialRecord: FinancialRecord = {
    id: generateFinancialId(),
    type,
    amount: totalAmount,
    description,
    date: getCurrentDate(),
    category: financialCategory
  };

  return financialRecord;
}

/**
 * Create a financial movement from an inventory transaction (for display in movements table)
 */
export function createFinancialMovementFromInventory(
  transactionData: InventoryTransactionData
): FinancialMovement | null {
  const { item, context, projectId, paymentMethod, isRecurring, recurringDetails, dueDate } = transactionData;
  
  // Only create financial movements for transactions with cost
  if (!item.cost || item.cost <= 0) {
    return null;
  }

  // Calculate total amount (cost * quantity)
  const totalAmount = item.cost * item.quantity;

  // Determine movement type and description
  let tipo: 'Entrada' | 'Salida';
  let concepto: string;
  let comportamiento: string;
  
  switch (context) {
    case 'purchase':
      tipo = 'Salida';
      concepto = `Compra de ${item.name}`;
      comportamiento = 'Fijo';
      break;
    case 'sale':
      tipo = 'Entrada';
      concepto = `Venta de ${item.name}`;
      comportamiento = 'Variable';
      break;
    case 'adjustment':
      tipo = 'Salida';
      concepto = `Ajuste de inventario - ${item.name}`;
      comportamiento = 'Fijo';
      break;
    case 'transfer':
      tipo = 'Salida';
      concepto = `Transferencia de ${item.name}`;
      comportamiento = 'Fijo';
      break;
    default:
      tipo = 'Salida';
      concepto = `Transacción de ${item.name}`;
      comportamiento = 'Fijo';
  }

  // Map inventory category to financial category
  const categoria = INVENTORY_TO_FINANCE_CATEGORY_MAP[item.category] || 'Otros';

  // Determine assignment (project, supplier, etc.)
  let asignacion: string | undefined;
  if (projectId) {
    asignacion = `Proyecto #${projectId}`;
  } else if (item.supplier) {
    asignacion = `Proveedor: ${item.supplier}`;
  }

  const financialMovement: FinancialMovement = {
    id: generateFinancialId(),
    fecha: getCurrentDate(),
    concepto,
    monto: totalAmount,
    tipo,
    asignacion,
    categoria,
    comportamiento,
    comprobante: '🗂',
    paymentMethod: paymentMethod || 'cash',
    isRecurring: isRecurring || false,
    recurringDetails,
    dueDate
  };

  return financialMovement;
}

/**
 * Determine transaction context based on inventory item creation data
 */
export function determineTransactionContext(
  itemData: Record<string, any>
): TransactionContext {
  // For now, we'll assume most inventory additions are purchases
  // This could be enhanced with more sophisticated logic based on:
  // - User input/context
  // - Item category
  // - Presence of supplier information
  // - Cost information
  
  if (itemData.supplier || itemData.cost) {
    return 'purchase';
  }
  
  return 'adjustment';
}

/**
 * Create financial entries for inventory deletion
 */
export function createFinancialEntriesFromDeletion(
  item: InventoryItem,
  deletionContext?: DeletionContext
): {
  record: FinancialRecord | null;
  movement: FinancialMovement | null;
} {
  // Only create financial entries for items with cost and quantity
  if (!item.cost || item.cost <= 0 || !item.quantity || item.quantity <= 0) {
    return { record: null, movement: null };
  }

  const totalValue = item.cost * item.quantity;
  let type: 'income' | 'expense';
  let description: string;
  let categoria: string;
  let comportamiento: string;
  let amount = totalValue;

  // Determine transaction details based on deletion reason
  switch (deletionContext?.reason) {
    case 'sold':
      type = 'income';
      description = `Venta de ${item.name}`;
      categoria = 'Ventas';
      comportamiento = 'Variable';
      amount = deletionContext.salePrice || totalValue;
      break;

    case 'disposed':
      type = 'expense';
      description = `Disposición de ${item.name}`;
      categoria = 'Pérdidas';
      comportamiento = 'Fijo';
      break;

    case 'expired':
      type = 'expense';
      description = `Pérdida por vencimiento - ${item.name}`;
      categoria = 'Pérdidas';
      comportamiento = 'Fijo';
      break;

    case 'damaged':
      type = 'expense';
      description = `Pérdida por daño - ${item.name}`;
      categoria = 'Pérdidas';
      comportamiento = 'Fijo';
      break;

    case 'transferred':
      type = 'expense';
      description = `Transferencia de ${item.name}${deletionContext.transferTo ? ` a ${deletionContext.transferTo}` : ''}`;
      categoria = 'Transferencias';
      comportamiento = 'Fijo';
      break;

    default:
      type = 'expense';
      description = `Ajuste de inventario - eliminación de ${item.name}`;
      categoria = 'Ajustes';
      comportamiento = 'Fijo';
  }

  // Add notes if provided
  if (deletionContext?.notes) {
    description += ` (${deletionContext.notes})`;
  }

  // Create financial record
  const record: FinancialRecord = {
    id: generateFinancialId(),
    type,
    amount,
    description,
    date: getCurrentDate(),
    category: categoria
  };

  // Create financial movement
  const movement: FinancialMovement = {
    id: generateFinancialId(),
    fecha: getCurrentDate(),
    concepto: description,
    monto: amount,
    tipo: type === 'income' ? 'Entrada' : 'Salida',
    categoria,
    comportamiento,
    comprobante: '🗂',
    paymentMethod: 'cash', // Default for deletion entries
    isRecurring: false,
    recurringDetails: undefined,
    dueDate: undefined
  };

  return { record, movement };
}

/**
 * Create both financial record and movement from inventory item
 */
export function createFinancialEntriesFromInventory(
  item: InventoryItem,
  context?: TransactionContext,
  projectId?: string
): {
  record: FinancialRecord | null;
  movement: FinancialMovement | null;
} {
  const transactionContext = context || determineTransactionContext(item);

  const transactionData: InventoryTransactionData = {
    item,
    context: transactionContext,
    projectId
  };

  const record = createFinancialRecordFromInventory(transactionData);
  const movement = createFinancialMovementFromInventory(transactionData);

  return { record, movement };
}
