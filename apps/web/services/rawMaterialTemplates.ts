/**
 * Raw Material Templates for Different Material Types
 * 
 * This module provides table column configurations based on material type.
 * Each material type has specific fields that are relevant for tracking batches.
 * These templates will be used to dynamically generate table headers and data structure.
 */

export interface TableColumn {
  id: string;
  header: string;
  description: string;
  type: 'text' | 'number' | 'currency' | 'date' | 'select';
  required: boolean;
  unit?: string;
}

export interface MaterialTypeTemplate {
  material_type: string;
  display_name: string;
  description: string;
  form_types: string[]; // Common forms for this material type
  columns: TableColumn[];
}

/**
 * Raw Material Templates Dictionary
 * Maps material types to their relevant table columns
 */
export const rawMaterialTemplates: Record<string, MaterialTypeTemplate> = {
  metal_sheet: {
    material_type: 'metal_sheet',
    display_name: 'Lámina Metálica',
    description: 'Láminas y placas de metal en diferentes espesores',
    form_types: ['Lámina', 'Placa', 'Chapa', 'Metal sheet', 'Steel sheet', 'Sheet', 'Plate'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Largo x Ancho x Espesor',
        type: 'text',
        required: true
      },
      {
        id: 'material_grade',
        header: 'Grado Material',
        description: 'Grado del material (ej: A36, 304, etc.)',
        type: 'text',
        required: false
      },
      {
        id: 'thickness',
        header: 'Espesor',
        description: 'Espesor de la lámina',
        type: 'text',
        required: false
      },
      {
        id: 'surface_finish',
        header: 'Acabado Superficial',
        description: 'Tipo de acabado superficial',
        type: 'text',
        required: false
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra del lote',
        type: 'date',
        required: false
      }
    ]
  },

  metal_bar: {
    material_type: 'metal_bar',
    display_name: 'Barra Metálica',
    description: 'Barras y perfiles metálicos sólidos',
    form_types: ['Barra Redonda', 'Barra Cuadrada', 'Barra Hexagonal', 'Perfil'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Diámetro o sección transversal',
        type: 'text',
        required: true
      },
      {
        id: 'length',
        header: 'Longitud',
        description: 'Longitud total disponible',
        type: 'number',
        required: true,
        unit: 'metros'
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo/Metro',
        description: 'Costo por metro lineal',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'total_cost',
        header: 'Costo Total',
        description: 'Costo total del lote',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra del lote',
        type: 'date',
        required: true
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del material',
        type: 'text',
        required: true
      }
    ]
  },

  metal_tube: {
    material_type: 'metal_tube',
    display_name: 'Tubo Metálico',
    description: 'Tubos y perfiles huecos metálicos',
    form_types: ['Tubo Redondo', 'Tubo Cuadrado', 'Tubo Rectangular', 'Perfil Hueco'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Sección x Espesor de pared',
        type: 'text',
        required: true
      },
      {
        id: 'quantity',
        header: 'Cantidad',
        description: 'Longitud total disponible',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo/Metro',
        description: 'Costo por metro lineal',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'total_cost',
        header: 'Costo Total',
        description: 'Costo total del lote',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra del lote',
        type: 'date',
        required: true
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del material',
        type: 'text',
        required: true
      }
    ]
  },

  plastic_sheet: {
    material_type: 'plastic_sheet',
    display_name: 'Lámina Plástica',
    description: 'Láminas y placas de materiales plásticos',
    form_types: ['Lámina', 'Placa', 'Film'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Largo x Ancho x Espesor',
        type: 'text',
        required: true
      },
      {
        id: 'quantity',
        header: 'Cantidad',
        description: 'Cantidad en stock',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'total_cost',
        header: 'Costo Total',
        description: 'Costo total del lote',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra del lote',
        type: 'date',
        required: true
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del material',
        type: 'text',
        required: true
      }
    ]
  }
};

/**
 * Mock fetch function to simulate API call for material type template
 * @param materialType - The material type to get template for
 * @returns Promise resolving to the material type template
 */
export async function fetchMaterialTypeTemplate(materialType: string): Promise<MaterialTypeTemplate | null> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
  
  const template = rawMaterialTemplates[materialType];
  
  if (!template) {
    console.warn(`Material type template for '${materialType}' not found, using default`);
    return rawMaterialTemplates.metal_sheet || null; // Default fallback
  }
  
  return template;
}

/**
 * Get all available material types
 * @returns Promise resolving to array of material type keys
 */
export async function fetchAvailableMaterialTypes(): Promise<string[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 30));
  
  return Object.keys(rawMaterialTemplates);
}
