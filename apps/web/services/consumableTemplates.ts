/**
 * Consumable Templates for Different Consumable Types
 * 
 * This module provides table column configurations based on consumable type.
 * Each consumable type has specific fields that are relevant for tracking usage and inventory.
 * These templates will be used to dynamically generate table headers and data structure.
 */

export interface TableColumn {
  id: string;
  header: string;
  description: string;
  type: 'text' | 'number' | 'currency' | 'date' | 'select' | 'percentage';
  required: boolean;
  unit?: string;
}

export interface ConsumableTypeTemplate {
  consumable_type: string;
  display_name: string;
  description: string;
  typical_items: string[]; // Common items for this consumable type
  columns: TableColumn[];
}

/**
 * Consumable Templates Dictionary
 * Maps consumable types to their relevant table columns
 */
export const consumableTemplates: Record<string, ConsumableTypeTemplate> = {
  welding_supplies: {
    consumable_type: 'welding_supplies',
    display_name: 'Suministros de Soldadura',
    description: 'Electrodos, gases, varillas y consumibles para soldadura',
    typical_items: ['Electrodos', 'Gas Argón', 'Gas CO2', 'Varillas TIG', 'Alambre MIG'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'specifications',
        header: 'Especificaciones',
        description: 'Especificaciones técnicas del consumible',
        type: 'text',
        required: true
      },
      {
        id: 'initial_quantity',
        header: 'Cantidad Inicial',
        description: 'Cantidad comprada inicialmente',
        type: 'number',
        required: true
      },
      {
        id: 'current_quantity',
        header: 'Cantidad Actual',
        description: 'Cantidad restante disponible',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'usage_percentage',
        header: '% Usado',
        description: 'Porcentaje utilizado',
        type: 'percentage',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra',
        type: 'date',
        required: true
      },
      {
        id: 'expiry_date',
        header: 'Fecha Vencimiento',
        description: 'Fecha de vencimiento',
        type: 'date',
        required: false
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del consumible',
        type: 'text',
        required: true
      }
    ]
  },

  cutting_tools: {
    consumable_type: 'cutting_tools',
    display_name: 'Herramientas de Corte',
    description: 'Discos, brocas, fresas y herramientas de corte que se desgastan',
    typical_items: ['Discos de Corte', 'Discos de Desbaste', 'Brocas', 'Fresas', 'Hojas de Sierra'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'tool_specifications',
        header: 'Especificaciones',
        description: 'Diámetro, material, aplicación',
        type: 'text',
        required: true
      },
      {
        id: 'initial_quantity',
        header: 'Cantidad Inicial',
        description: 'Cantidad comprada inicialmente',
        type: 'number',
        required: true
      },
      {
        id: 'current_quantity',
        header: 'Cantidad Actual',
        description: 'Cantidad restante disponible',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'usage_percentage',
        header: '% Usado',
        description: 'Porcentaje utilizado',
        type: 'percentage',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra',
        type: 'date',
        required: true
      },
      {
        id: 'estimated_life',
        header: 'Vida Útil Est.',
        description: 'Vida útil estimada',
        type: 'text',
        required: false
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del consumible',
        type: 'text',
        required: true
      }
    ]
  },

  abrasives_sandpaper: {
    consumable_type: 'abrasives_sandpaper',
    display_name: 'Abrasivos y Lijas',
    description: 'Lijas, abrasivos y materiales para acabado superficial',
    typical_items: ['Lija de Agua', 'Lija Seca', 'Discos Abrasivos', 'Pasta Pulir', 'Fibra Abrasiva'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'grit_size',
        header: 'Grano',
        description: 'Tamaño de grano/rugosidad',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Medidas del abrasivo',
        type: 'text',
        required: true
      },
      {
        id: 'initial_quantity',
        header: 'Cantidad Inicial',
        description: 'Cantidad comprada inicialmente',
        type: 'number',
        required: true
      },
      {
        id: 'current_quantity',
        header: 'Cantidad Actual',
        description: 'Cantidad restante disponible',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'usage_percentage',
        header: '% Usado',
        description: 'Porcentaje utilizado',
        type: 'percentage',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra',
        type: 'date',
        required: true
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del consumible',
        type: 'text',
        required: true
      }
    ]
  },

  chemicals_fluids: {
    consumable_type: 'chemicals_fluids',
    display_name: 'Químicos y Fluidos',
    description: 'Aceites, solventes, lubricantes y productos químicos',
    typical_items: ['Aceite de Corte', 'Solventes', 'Lubricantes', 'Desengrasantes', 'Refrigerantes'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'chemical_composition',
        header: 'Composición',
        description: 'Composición química principal',
        type: 'text',
        required: true
      },
      {
        id: 'concentration',
        header: 'Concentración',
        description: 'Concentración o pureza',
        type: 'text',
        required: false
      },
      {
        id: 'initial_quantity',
        header: 'Cantidad Inicial',
        description: 'Cantidad comprada inicialmente',
        type: 'number',
        required: true
      },
      {
        id: 'current_quantity',
        header: 'Cantidad Actual',
        description: 'Cantidad restante disponible',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'usage_percentage',
        header: '% Usado',
        description: 'Porcentaje utilizado',
        type: 'percentage',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra',
        type: 'date',
        required: true
      },
      {
        id: 'expiry_date',
        header: 'Fecha Vencimiento',
        description: 'Fecha de vencimiento',
        type: 'date',
        required: true
      },
      {
        id: 'safety_notes',
        header: 'Notas Seguridad',
        description: 'Notas de seguridad importantes',
        type: 'text',
        required: false
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del consumible',
        type: 'text',
        required: true
      }
    ]
  },

  fasteners_hardware: {
    consumable_type: 'fasteners_hardware',
    display_name: 'Sujetadores y Ferretería',
    description: 'Tornillos, tuercas, arandelas y elementos de sujeción',
    typical_items: ['Tornillos', 'Tuercas', 'Arandelas', 'Remaches', 'Pernos'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote del proveedor',
        type: 'text',
        required: true
      },
      {
        id: 'specifications',
        header: 'Especificaciones',
        description: 'Medida, rosca, material',
        type: 'text',
        required: true
      },
      {
        id: 'material_grade',
        header: 'Grado Material',
        description: 'Grado del material (ej: 8.8, A2, etc.)',
        type: 'text',
        required: true
      },
      {
        id: 'initial_quantity',
        header: 'Cantidad Inicial',
        description: 'Cantidad comprada inicialmente',
        type: 'number',
        required: true
      },
      {
        id: 'current_quantity',
        header: 'Cantidad Actual',
        description: 'Cantidad restante disponible',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'usage_percentage',
        header: '% Usado',
        description: 'Porcentaje utilizado',
        type: 'percentage',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'purchase_date',
        header: 'Fecha Compra',
        description: 'Fecha de compra',
        type: 'date',
        required: true
      },
      {
        id: 'supplier',
        header: 'Proveedor',
        description: 'Proveedor del consumible',
        type: 'text',
        required: true
      }
    ]
  }
};

/**
 * Mock fetch function to simulate API call for consumable type template
 * @param consumableType - The consumable type to get template for
 * @returns Promise resolving to the consumable type template
 */
export async function fetchConsumableTypeTemplate(consumableType: string): Promise<ConsumableTypeTemplate | null> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
  
  const template = consumableTemplates[consumableType];
  
  if (!template) {
    console.warn(`Consumable type template for '${consumableType}' not found, using default`);
    return consumableTemplates.welding_supplies || null; // Default fallback
  }
  
  return template;
}

/**
 * Get all available consumable types
 * @returns Promise resolving to array of consumable type keys
 */
export async function fetchAvailableConsumableTypes(): Promise<string[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 30));
  
  return Object.keys(consumableTemplates);
}
