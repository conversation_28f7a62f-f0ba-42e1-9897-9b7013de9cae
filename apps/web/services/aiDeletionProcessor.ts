import { ParsedIntent, AIConversationResponse } from './openai';
import { 
  generateDeletionResponse, 
  executeDeletion,
  EntityLookupFunctions,
  EntityDeletionFunctions,
  AIDeletionResponse 
} from './aiDeletionService';

// Deletion session for tracking confirmation flow
export interface DeletionSession {
  id: string;
  intent: ParsedIntent;
  entity: any;
  entityType: string;
  entityId: string;
  awaitingConfirmation: boolean;
  deletionResponse: AIDeletionResponse;
  createdAt: Date;
}

// Session manager for deletion confirmations
class DeletionSessionManager {
  private sessions = new Map<string, DeletionSession>();

  createSession(intent: ParsedIntent, entity: any, deletionResponse: AIDeletionResponse): DeletionSession {
    const session: DeletionSession = {
      id: this.generateId(),
      intent,
      entity,
      entityType: intent.entityType!,
      entityId: entity.id,
      awaitingConfirmation: true,
      deletionResponse,
      createdAt: new Date(),
    };

    this.sessions.set(session.id, session);
    return session;
  }

  getSession(id: string): DeletionSession | null {
    return this.sessions.get(id) || null;
  }

  removeSession(id: string): void {
    this.sessions.delete(id);
  }

  cleanupOldSessions(): void {
    const now = new Date();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    for (const [id, session] of this.sessions.entries()) {
      if (now.getTime() - session.createdAt.getTime() > maxAge) {
        this.sessions.delete(id);
      }
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}

const deletionSessionManager = new DeletionSessionManager();

/**
 * Get context functions from React contexts
 * This will be called from the main app with actual context functions
 */
let contextFunctions: (EntityLookupFunctions & EntityDeletionFunctions) | null = null;

export function setContextFunctions(functions: EntityLookupFunctions & EntityDeletionFunctions) {
  contextFunctions = functions;
}

/**
 * Process a deletion command from the AI
 */
export async function processDeletionCommand(intent: ParsedIntent): Promise<{
  success: boolean;
  session?: any;
  aiResponse?: AIConversationResponse;
  error?: string;
  isDeletion?: boolean;
  deletionSession?: DeletionSession;
}> {
  try {
    // Clean up old sessions
    deletionSessionManager.cleanupOldSessions();

    if (!contextFunctions) {
      return {
        success: false,
        error: 'Context functions not initialized. Please try again.',
      };
    }

    // Generate deletion response (includes entity lookup and validation)
    const deletionResponse = await generateDeletionResponse(intent, contextFunctions);

    if (deletionResponse.type === 'error' || deletionResponse.type === 'not_found') {
      return {
        success: false,
        error: deletionResponse.message,
      };
    }

    if (deletionResponse.type === 'confirmation_needed') {
      // Create a deletion session for confirmation flow
      const deletionSession = deletionSessionManager.createSession(intent, deletionResponse.entity, deletionResponse);

      // Convert to AI conversation response
      const aiResponse: AIConversationResponse = {
        type: 'question',
        message: deletionResponse.message,
        needsMoreInfo: true,
        followUpQuestions: ['Type "confirm" to proceed or "cancel" to abort.'],
      };

      return {
        success: true,
        aiResponse,
        isDeletion: true,
        deletionSession,
      };
    }

    if (deletionResponse.type === 'multiple_matches') {
      // Return the list of matching entities for user selection
      const aiResponse: AIConversationResponse = {
        type: 'question',
        message: deletionResponse.message,
        needsMoreInfo: true,
        followUpQuestions: ['Please specify which item you want to delete.'],
      };

      return {
        success: true,
        aiResponse,
        isDeletion: false, // Not in deletion mode yet, waiting for selection
      };
    }

    return {
      success: false,
      error: 'Unexpected deletion response type',
    };
  } catch (error) {
    console.error('Error processing deletion command:', error);
    return {
      success: false,
      error: 'An error occurred while processing the deletion command.',
    };
  }
}

/**
 * Process confirmation response for deletion
 */
export async function processConfirmationResponse(
  sessionId: string, 
  response: string
): Promise<{
  success: boolean;
  message: string;
  completed?: boolean;
  cancelled?: boolean;
}> {
  try {
    const session = deletionSessionManager.getSession(sessionId);
    if (!session) {
      return {
        success: false,
        message: 'Deletion session not found or expired. Please start over.',
      };
    }

    if (!contextFunctions) {
      return {
        success: false,
        message: 'Context functions not initialized. Please try again.',
      };
    }

    const normalizedResponse = response.toLowerCase().trim();

    if (normalizedResponse === 'cancel' || normalizedResponse === 'no' || normalizedResponse === 'abort') {
      deletionSessionManager.removeSession(sessionId);
      return {
        success: true,
        message: 'Deletion cancelled.',
        cancelled: true,
      };
    }

    if (normalizedResponse === 'confirm' || normalizedResponse === 'yes' || normalizedResponse === 'proceed') {
      // Execute the deletion
      const result = await executeDeletion(
        session.entityType as any,
        session.entityId,
        contextFunctions
      );

      deletionSessionManager.removeSession(sessionId);

      if (result.success) {
        const entityTypeName = getEntityTypeName(session.entityType);
        const entityName = getEntityDisplayName(result.deletedEntity, session.entityType);
        
        let message = `✅ Successfully deleted ${entityTypeName} "${entityName}".`;
        
        if (result.financialImpact?.shouldCreateMovement) {
          message += `\n\n💰 Financial impact: ${result.financialImpact.description} (${result.financialImpact.movementType === 'income' ? '+' : '-'}$${result.financialImpact.amount})`;
        }

        return {
          success: true,
          message,
          completed: true,
        };
      } else {
        return {
          success: false,
          message: `Failed to delete: ${result.error}`,
        };
      }
    }

    return {
      success: false,
      message: 'Please respond with "confirm" to proceed or "cancel" to abort the deletion.',
    };
  } catch (error) {
    console.error('Error processing confirmation response:', error);
    return {
      success: false,
      message: 'An error occurred while processing your response.',
    };
  }
}

/**
 * Check if a session is awaiting deletion confirmation
 */
export function isDeletionSessionActive(sessionId?: string): boolean {
  if (!sessionId) return false;
  const session = deletionSessionManager.getSession(sessionId);
  return session?.awaitingConfirmation || false;
}

/**
 * Get deletion session by ID
 */
export function getDeletionSession(sessionId: string): DeletionSession | null {
  return deletionSessionManager.getSession(sessionId);
}

/**
 * Helper function to get user-friendly entity type name
 */
function getEntityTypeName(entityType: string): string {
  switch (entityType) {
    case 'employee':
      return 'employee';
    case 'project':
      return 'project';
    case 'inventory':
      return 'inventory item';
    case 'financial':
      return 'financial record';
    case 'catalog':
      return 'catalog item';
    default:
      return 'item';
  }
}

/**
 * Helper function to get display name for an entity
 */
function getEntityDisplayName(entity: any, entityType: string): string {
  if (!entity) return 'Unknown';
  
  switch (entityType) {
    case 'employee':
      return entity.name || entity.id;
    case 'project':
      return entity.name || entity.id;
    case 'inventory':
      return entity.name || entity.id;
    case 'financial':
      return entity.description || entity.id;
    case 'catalog':
      return entity.name || entity.id;
    default:
      return entity.id || 'Unknown';
  }
}
