import { ProjectTask } from '../contexts/types';

export interface CreateTaskData {
  name: string;
  startDate?: string;
  endDate?: string;
  assignedToId?: string;
  projectId?: string;
}

export interface UpdateTaskData {
  name?: string;
  startDate?: string;
  endDate?: string;
  assignedToId?: string;
  projectId?: string;
  completed?: boolean;
}

class TaskService {
  private baseUrl = 'http://localhost:3001/api';

  /**
   * Get all tasks for a user
   */
  async getTasks(userId: string): Promise<ProjectTask[]> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks?userId=${userId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch tasks');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }
  }

  /**
   * Get tasks for a specific project
   */
  async getProjectTasks(projectId: string): Promise<ProjectTask[]> {
    try {
      const response = await fetch(`${this.baseUrl}/projects/${projectId}/tasks`);
      if (!response.ok) {
        throw new Error('Failed to fetch project tasks');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching project tasks:', error);
      throw error;
    }
  }

  /**
   * Create a new task
   */
  async createTask(taskData: CreateTaskData): Promise<ProjectTask> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taskData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create task');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  /**
   * Create a task for a specific project
   */
  async createProjectTask(projectId: string, taskData: Omit<CreateTaskData, 'projectId'>): Promise<ProjectTask> {
    try {
      const response = await fetch(`${this.baseUrl}/projects/${projectId}/tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taskData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create project task');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating project task:', error);
      throw error;
    }
  }

  /**
   * Update a task
   */
  async updateTask(taskId: string, updates: UpdateTaskData): Promise<ProjectTask> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update task');
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks/${taskId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }

  /**
   * Toggle task completion status
   */
  async toggleTaskCompletion(taskId: string, completed: boolean): Promise<ProjectTask> {
    return this.updateTask(taskId, { completed });
  }

  /**
   * Assign task to team member
   */
  async assignTask(taskId: string, teamMemberId: string): Promise<ProjectTask> {
    return this.updateTask(taskId, { assignedToId: teamMemberId });
  }

  /**
   * Unassign task from team member
   */
  async unassignTask(taskId: string): Promise<ProjectTask> {
    return this.updateTask(taskId, { assignedToId: undefined });
  }

  /**
   * Move task to project
   */
  async moveTaskToProject(taskId: string, projectId: string): Promise<ProjectTask> {
    return this.updateTask(taskId, { projectId });
  }

  /**
   * Remove task from project (make it standalone)
   */
  async removeTaskFromProject(taskId: string): Promise<ProjectTask> {
    return this.updateTask(taskId, { projectId: undefined });
  }
}

export const taskService = new TaskService();
