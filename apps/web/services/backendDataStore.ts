import apiService from './apiClient';
import type {
  User,
  Project,
  ProjectTask,
  TeamMember,
  CatalogItem,
  InventoryItem,
  FinancialRecord,
  FinancialMovement,
} from '../contexts/types';

// Backend-powered DataStore that replaces the mock data implementation
// This maintains the same interface as the original DataStore but uses real API calls
export class BackendDataStore {
  private currentUserId: string = 'default-user-id'; // This should be set from auth context

  // Set the current user ID (should be called from auth context)
  setCurrentUserId(userId: string) {
    this.currentUserId = userId;
  }

  getCurrentUserId(): string {
    return this.currentUserId;
  }

  // Team Member operations
  async getTeamMembers(): Promise<TeamMember[]> {
    return apiService.teamMembers.getAll(this.currentUserId);
  }

  async addTeamMember(teamMemberData: Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<TeamMember> {
    return apiService.teamMembers.create({
      ...teamMemberData,
      userId: this.currentUserId,
    });
  }

  async updateTeamMember(id: string, teamMemberData: Partial<TeamMember>): Promise<TeamMember> {
    return apiService.teamMembers.update(id, teamMemberData);
  }

  async deleteTeamMember(id: string): Promise<void> {
    return apiService.teamMembers.delete(id);
  }

  // Project operations
  async getProjects(): Promise<Project[]> {
    return apiService.projects.getAll(this.currentUserId);
  }

  async addProject(projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {
    return apiService.projects.create({
      ...projectData,
      userId: this.currentUserId,
    });
  }

  async updateProject(id: string, projectData: Partial<Project>): Promise<Project> {
    return apiService.projects.update(id, projectData);
  }

  async deleteProject(id: string): Promise<void> {
    return apiService.projects.delete(id);
  }

  // Task operations
  async getProjectTasks(projectId: string): Promise<ProjectTask[]> {
    return apiService.tasks.getAll(projectId);
  }

  async addProjectTask(projectId: string, taskData: Omit<ProjectTask, 'id' | 'projectId'>): Promise<ProjectTask> {
    return apiService.tasks.create(projectId, taskData);
  }

  async updateProjectTask(projectId: string, taskId: string, taskData: Partial<ProjectTask>): Promise<ProjectTask> {
    return apiService.tasks.update(projectId, taskId, taskData);
  }

  async deleteProjectTask(projectId: string, taskId: string): Promise<void> {
    return apiService.tasks.delete(projectId, taskId);
  }

  // Catalog operations
  async getCatalogItems(): Promise<CatalogItem[]> {
    return apiService.catalog.getAll(this.currentUserId);
  }

  async addCatalogItem(catalogItemData: Omit<CatalogItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<CatalogItem> {
    return apiService.catalog.create({
      ...catalogItemData,
      userId: this.currentUserId,
    });
  }

  async updateCatalogItem(id: string, catalogItemData: Partial<CatalogItem>): Promise<CatalogItem> {
    return apiService.catalog.update(id, catalogItemData);
  }

  async deleteCatalogItem(id: string): Promise<void> {
    return apiService.catalog.delete(id);
  }

  // Inventory operations
  async getInventoryItems(): Promise<InventoryItem[]> {
    return apiService.inventory.getAll(this.currentUserId);
  }

  async addInventoryItem(inventoryItemData: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<InventoryItem> {
    return apiService.inventory.create({
      ...inventoryItemData,
      userId: this.currentUserId,
    });
  }

  async updateInventoryItem(id: string, inventoryItemData: Partial<InventoryItem>): Promise<InventoryItem> {
    return apiService.inventory.update(id, inventoryItemData);
  }

  async deleteInventoryItem(id: string): Promise<void> {
    return apiService.inventory.delete(id);
  }

  // Batch tracking operations
  async getRawMaterialBatches(itemId: string): Promise<any[]> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/inventory/${itemId}/batches/raw-materials`);
    if (!response.ok) {
      throw new Error('Failed to fetch raw material batches');
    }
    return response.json();
  }

  async getFinishedProductBatches(itemId: string): Promise<any[]> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/inventory/${itemId}/batches/finished-products`);
    if (!response.ok) {
      throw new Error('Failed to fetch finished product batches');
    }
    return response.json();
  }

  async getConsumableBatches(itemId: string): Promise<any[]> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/inventory/${itemId}/batches/consumables`);
    if (!response.ok) {
      throw new Error('Failed to fetch consumable batches');
    }
    return response.json();
  }

  async getEquipmentRecords(itemId: string): Promise<any[]> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/inventory/${itemId}/batches/equipment`);
    if (!response.ok) {
      throw new Error('Failed to fetch equipment records');
    }
    return response.json();
  }

  // Financial operations
  async getFinancialRecords(): Promise<FinancialRecord[]> {
    const result = await apiService.finance.getAll(this.currentUserId, 'records');
    return result.records || [];
  }

  async addFinancialRecord(financialRecordData: Omit<FinancialRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<FinancialRecord> {
    return apiService.finance.createRecord({
      ...financialRecordData,
      userId: this.currentUserId,
    });
  }

  async deleteFinancialRecord(id: string): Promise<void> {
    return apiService.finance.deleteRecord(id);
  }

  async getFinancialMovements(): Promise<FinancialMovement[]> {
    const result = await apiService.finance.getAll(this.currentUserId, 'movements');
    return result.movements || [];
  }

  async addFinancialMovement(financialMovementData: Omit<FinancialMovement, 'id' | 'createdAt' | 'updatedAt'>): Promise<FinancialMovement> {
    return apiService.finance.createMovement({
      ...financialMovementData,
      userId: this.currentUserId,
    });
  }

  async deleteFinancialMovement(id: string): Promise<void> {
    return apiService.finance.deleteMovement(id);
  }

  // User operations
  async getCurrentUser(): Promise<User | null> {
    try {
      return await apiService.users.getById(this.currentUserId);
    } catch (error) {
      console.error('Error fetching current user:', error);
      return null;
    }
  }

  async updateCurrentUser(userData: Partial<User>): Promise<User> {
    return apiService.users.update(this.currentUserId, userData);
  }

  // Utility methods for compatibility with existing frontend code
  async getAllData(): Promise<{
    teamMembers: TeamMember[];
    projects: Project[];
    catalogItems: CatalogItem[];
    inventoryItems: InventoryItem[];
    financialRecords: FinancialRecord[];
    financialMovements: FinancialMovement[];
  }> {
    const [
      teamMembers,
      projects,
      catalogItems,
      inventoryItems,
      financialRecords,
      financialMovements,
    ] = await Promise.all([
      this.getTeamMembers(),
      this.getProjects(),
      this.getCatalogItems(),
      this.getInventoryItems(),
      this.getFinancialRecords(),
      this.getFinancialMovements(),
    ]);

    return {
      teamMembers,
      projects,
      catalogItems,
      inventoryItems,
      financialRecords,
      financialMovements,
    };
  }

  // Search and filter methods
  async searchTeamMembers(query: string): Promise<TeamMember[]> {
    const allTeamMembers = await this.getTeamMembers();
    const searchQuery = query.toLowerCase();
    
    return allTeamMembers.filter(member =>
      member.name.toLowerCase().includes(searchQuery) ||
      member.role.toLowerCase().includes(searchQuery) ||
      member.email.toLowerCase().includes(searchQuery)
    );
  }

  async searchCatalogItems(query: string, type?: 'Product' | 'Service'): Promise<CatalogItem[]> {
    const allCatalogItems = await apiService.catalog.getAll(this.currentUserId, type);
    const searchQuery = query.toLowerCase();
    
    return allCatalogItems.filter(item =>
      item.productName.toLowerCase().includes(searchQuery) ||
      item.productDescription.toLowerCase().includes(searchQuery)
    );
  }

  async searchInventoryItems(query: string, category?: string): Promise<InventoryItem[]> {
    const filters = category ? { category } : undefined;
    const allInventoryItems = await apiService.inventory.getAll(this.currentUserId, filters);
    const searchQuery = query.toLowerCase();
    
    return allInventoryItems.filter(item =>
      item.name.toLowerCase().includes(searchQuery) ||
      item.description.toLowerCase().includes(searchQuery)
    );
  }

  async getLowStockItems(): Promise<InventoryItem[]> {
    return apiService.inventory.getAll(this.currentUserId, { lowStock: true });
  }
}

// Export singleton instance
export const backendDataStore = new BackendDataStore();

// Export as default for easy replacement of the mock dataStore
export default backendDataStore;
