// Note: OpenAI client moved to backend - this file needs to be migrated
import { DataCreationSession } from './aiCommandParser';
import { AIConversationResponse } from './openai';
import { getCurrentBusinessTemplate, BusinessNicheTemplate } from './businessNicheTemplates';

/**
 * Template-based guided conversation for project creation
 * Uses business niche templates to determine what questions to ask
 */

export interface TemplateBasedSession extends DataCreationSession {
  businessTemplate: BusinessNicheTemplate;
  templateData: {
    basicInfo: Record<string, any>;
    moduleSelections: Record<string, boolean>;
    moduleData: Record<string, any>;
  };
  currentTemplateStep: string;
}

/**
 * Initialize a template-based guided conversation session
 */
export function initializeTemplateBasedSession(session: DataCreationSession): TemplateBasedSession {
  const businessTemplate = getCurrentBusinessTemplate();
  
  return {
    ...session,
    businessTemplate,
    templateData: {
      basicInfo: {},
      moduleSelections: {},
      moduleData: {}
    },
    currentTemplateStep: 'basic_info'
  };
}

/**
 * Generate AI response based on business template
 */
export async function generateTemplateBasedResponse(
  session: TemplateBasedSession,
  userMessage?: string
): Promise<AIConversationResponse> {
  const { businessTemplate, currentTemplateStep, templateData } = session;

  try {
    // TODO: Migrate this function to backend - temporarily disabled
    throw new Error('Template-based conversation needs to be migrated to backend');

    /*
    const systemPrompt = getTemplateSystemPrompt(businessTemplate, currentTemplateStep);
    const userPrompt = getTemplateUserPrompt(businessTemplate, currentTemplateStep, templateData, userMessage);

    const response = await getOpenAIClient().chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 400
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Try to parse as JSON, fallback to text response
    try {
      const parsed = JSON.parse(content);
      const response = {
        type: parsed.type || 'question',
        message: parsed.message || content,
        needsMoreInfo: parsed.needsMoreInfo !== false,
        followUpQuestions: parsed.followUpQuestions || []
      };

      // For basic_info and catalog_selection steps, show form after asking the question
      // Other steps remain conversational
      if (currentTemplateStep === 'basic_info' || currentTemplateStep === 'catalog_selection') {
        response.type = 'data_ready';
        response.needsMoreInfo = false;
      }

      return response;
    } catch {
      // Fallback to text response
      const response: AIConversationResponse = {
        type: 'question',
        message: content,
        needsMoreInfo: true
      };

      // For basic_info and catalog_selection steps, show form after asking the question
      // Other steps (team, tasks, logistics) remain conversational
      if (currentTemplateStep === 'basic_info' || currentTemplateStep === 'catalog_selection') {
        response.type = 'data_ready';
        response.needsMoreInfo = false;
      }

      return response;
    }
    */

  } catch (error) {
    console.error('Error generating template-based response:', error);
    return getTemplateFallbackResponse(businessTemplate, currentTemplateStep);
  }
}

/**
 * Get system prompt based on business template and current step
 */
function getTemplateSystemPrompt(template: BusinessNicheTemplate, step: string): string {
  const basePrompt = `You are a friendly AI assistant helping users create a ${template.name} project step by step.

Business Context: ${template.description}

You should:
- Ask clear, specific questions relevant to ${template.name}
- Be conversational and encouraging
- Guide users through the process naturally
- Keep responses concise but helpful

Always respond with JSON in this format:
{
  "type": "question" | "confirmation" | "data_ready",
  "message": "Your conversational response",
  "needsMoreInfo": true/false
}`;

  switch (step) {
    case 'basic_info':
      const requiredFields = template.basicFields
        .filter(field => field.required)
        .map(field => `- ${field.label}`)
        .join('\n');
      
      return `${basePrompt}

CURRENT STEP: Basic Project Information
You need to collect these required fields:
${requiredFields}

Ask for ALL missing required information in ONE comprehensive question.
Be specific to ${template.name} projects.`;

    case 'catalog_selection':
      const catalogModule = template.modules.find(m => m.id === 'catalog');
      return `${basePrompt}

CURRENT STEP: ${catalogModule?.name || 'Catalog Selection'}
${catalogModule?.description || ''}

The user will now see a catalog selection interface where they can choose products and materials from their catalog.
Your response should introduce this step and explain that they can select items from the catalog interface that will appear.
Do NOT ask specific questions about materials - the interface will handle the selection.`;

    case 'team_assignment':
      const teamModule = template.modules.find(m => m.id === 'team');
      return `${basePrompt}

CURRENT STEP: ${teamModule?.name || 'Team Assignment'}
${teamModule?.description || ''}

Ask about which team members will work on this project.
This step is optional - if they don't need team assignment, move to next step.`;

    case 'task_assignment':
      const taskModule = template.modules.find(m => m.id === 'tasks');
      return `${basePrompt}

CURRENT STEP: ${taskModule?.name || 'Task Assignment'}
${taskModule?.description || ''}

Ask about specific fabrication tasks that need to be completed.
This step is optional - if they don't need task assignment, move to next step.`;

    case 'logistics_setup':
      const logisticsModule = template.modules.find(m => m.id === 'logistics');
      return `${basePrompt}

CURRENT STEP: ${logisticsModule?.name || 'Logistics Setup'}
${logisticsModule?.description || ''}

Ask about logistics needs like shipping, delivery, inventory tracking.
This step is optional - if they don't need logistics, move to confirmation.`;

    case 'confirmation':
      return `${basePrompt}

CURRENT STEP: Final Confirmation
Review all collected information and ask for final confirmation to create the project.
Summarize what will be created.`;

    default:
      return basePrompt;
  }
}

/**
 * Get user prompt based on template and current data
 */
function getTemplateUserPrompt(
  template: BusinessNicheTemplate,
  step: string,
  templateData: any,
  userMessage?: string
): string {
  let prompt = `Business Type: ${template.name}\n`;
  prompt += `Current Step: ${step}\n`;
  prompt += `Template Step Question: ${template.conversationFlow.stepQuestions[step] || 'Continue the conversation'}\n`;
  
  if (userMessage) {
    prompt += `User's latest message: "${userMessage}"\n`;
  }

  switch (step) {
    case 'basic_info':
      prompt += `\nBasic Info Collected So Far:\n`;
      template.basicFields.forEach(field => {
        const value = templateData.basicInfo[field.id];
        prompt += `- ${field.label}: ${value || 'Not provided'}\n`;
      });
      break;

    case 'catalog_selection':
      prompt += `\nProject: ${templateData.basicInfo.name || 'Unnamed project'}\n`;
      prompt += `Description: ${templateData.basicInfo.description || 'No description'}\n`;
      prompt += `Ask specifically about catalog items needed for this ${template.name} project.\n`;
      break;

    case 'team_assignment':
      prompt += `\nProject: ${templateData.basicInfo.name || 'Unnamed project'}\n`;
      prompt += `Ask about which team members will work on this ${template.name} project.\n`;
      break;

    case 'task_assignment':
      prompt += `\nProject: ${templateData.basicInfo.name || 'Unnamed project'}\n`;
      prompt += `Ask about specific fabrication tasks for this ${template.name} project.\n`;
      break;

    case 'logistics_setup':
      prompt += `\nProject: ${templateData.basicInfo.name || 'Unnamed project'}\n`;
      prompt += `Ask about logistics needs for this ${template.name} project.\n`;
      break;
  }

  return prompt;
}

/**
 * Get fallback response for template-based conversation
 */
function getTemplateFallbackResponse(template: BusinessNicheTemplate, step: string): AIConversationResponse {
  const stepQuestion = template.conversationFlow.stepQuestions[step];
  
  return {
    type: 'question',
    message: stepQuestion || `Let's continue with your ${template.name} project.`,
    needsMoreInfo: true
  };
}

/**
 * Process user response and extract data based on template
 */
export async function processTemplateBasedResponse(
  session: TemplateBasedSession,
  userMessage: string
): Promise<{ updatedSession: TemplateBasedSession; shouldAdvanceStep: boolean }> {
  const { businessTemplate, currentTemplateStep } = session;
  
  // Extract data based on current step
  const extractedData = await extractTemplateData(userMessage, businessTemplate, currentTemplateStep);
  
  // Update session data
  if (currentTemplateStep === 'basic_info') {
    session.templateData.basicInfo = { ...session.templateData.basicInfo, ...extractedData };
  } else {
    session.templateData.moduleData[currentTemplateStep] = extractedData;
  }
  
  // Check if step is complete
  const isStepComplete = checkTemplateStepCompletion(businessTemplate, currentTemplateStep, session.templateData);

  return {
    updatedSession: session,
    shouldAdvanceStep: isStepComplete
  };
}

/**
 * Extract data from user message based on template and step
 */
async function extractTemplateData(
  userMessage: string,
  template: BusinessNicheTemplate,
  step: string
): Promise<Record<string, any>> {
  // TODO: Migrate to backend - temporarily disabled
  return {};

  /*
  try {
    let fieldsToExtract = '';
    
    if (step === 'basic_info') {
      fieldsToExtract = template.basicFields.map(field => field.id).join(', ');
    } else {
      fieldsToExtract = 'relevant information for this step';
    }

    let systemPrompt = `Extract structured data from the user's message for a ${template.name} project.
Return only a JSON object with the extracted fields. If no relevant data is found, return {}.`;

    if (step === 'basic_info') {
      systemPrompt += `

For basic_info, extract these specific fields:
- name: Project name (string)
- client: Client name (string)
- startDate: Start date in YYYY-MM-DD format (string)
- endDate: End date in YYYY-MM-DD format (string, optional)
- description: Project description (string)

Only extract fields that are explicitly mentioned or can be clearly inferred from the user's message.`;
    } else {
      systemPrompt += `

For ${step}, extract these fields: ${fieldsToExtract}`;
    }

    const response = await getOpenAIClient().chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userMessage }
      ],
      temperature: 0.1,
      max_tokens: 200
    });

    const content = response.choices[0]?.message?.content;
    if (content) {
      try {
        return JSON.parse(content);
      } catch {
        return {};
      }
    }
    
    return {};
  } catch (error) {
    console.error('Error extracting template data:', error);
    return {};
  }
  */
}

/**
 * Check if current template step is complete
 */
function checkTemplateStepCompletion(
  template: BusinessNicheTemplate,
  step: string,
  templateData: any
): boolean {
  switch (step) {
    case 'basic_info':
      const requiredFields = template.basicFields.filter(field => field.required);
      return requiredFields.every(field => templateData.basicInfo[field.id]);
    
    case 'catalog_selection':
      return true; // Always advance after user responds
    
    case 'team_assignment':
      return true; // Optional step, always advance

    case 'task_assignment':
      return true; // Optional step, always advance

    case 'logistics_setup':
      return true; // Optional step, always advance
    
    case 'confirmation':
      return true; // Final step
    
    default:
      return false;
  }
}

/**
 * Advance to next step in template flow
 */
export function advanceTemplateStep(session: TemplateBasedSession): TemplateBasedSession {
  const { businessTemplate, currentTemplateStep } = session;
  const steps = businessTemplate.conversationFlow.steps;
  const currentIndex = steps.indexOf(currentTemplateStep);

  if (currentIndex < steps.length - 1) {
    const nextStep = steps[currentIndex + 1];
    if (nextStep) {
      session.currentTemplateStep = nextStep;
    }
  }

  return session;
}

/**
 * Generate form fields from business template for current step
 */
export function generateTemplateFormFields(session: TemplateBasedSession): any[] {
  const { businessTemplate, currentTemplateStep } = session;

  // Basic info step shows form fields
  if (currentTemplateStep === 'basic_info') {
    return businessTemplate.basicFields.map(field => ({
      id: field.id,
      label: field.label,
      type: field.type === 'textarea' ? 'text' : field.type,
      required: field.required,
      placeholder: field.placeholder || '',
      options: field.options || [],
      value: session.templateData.basicInfo[field.id] || ''
    }));
  }

  // Catalog selection step shows catalog interface (handled separately)
  if (currentTemplateStep === 'catalog_selection') {
    return []; // Catalog selection uses custom interface, not form fields
  }

  // All other steps (team, tasks, logistics) are conversation-based
  return [];
}

/**
 * Check if current step should show catalog selection interface
 */
export function shouldShowCatalogSelection(session: TemplateBasedSession): boolean {
  return session.currentTemplateStep === 'catalog_selection';
}
