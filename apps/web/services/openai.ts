// Frontend wrapper for OpenAI services - calls backend API endpoints
// This ensures API keys and sensitive operations stay on the server

// Supported entity types for data creation
export type EntityType = 'employee' | 'project' | 'inventory' | 'financial' | 'catalog' | 'task';

// Intent extraction result
export interface ParsedIntent {
  action: 'create' | 'update' | 'delete' | 'remove' | 'edit' | 'modify' | 'add_to' | 'unknown';
  entityType: EntityType | null;
  extractedData: Record<string, any>;
  confidence: number;
  originalCommand: string;
  // For edit actions, specify the target project and what to modify
  targetProject?: string; // Project name or ID
  editType?: 'add_team_member' | 'add_task' | 'add_product' | 'add_service' | 'add_material' | 'enable_module' | 'update_info';
}

// Entity field definitions for form generation
export interface EntityField {
  id: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'select' | 'date' | 'currency';
  required: boolean;
  options?: string[];
  placeholder?: string;
  defaultValue?: any;
}

// Entity schema definitions
export const ENTITY_SCHEMAS: Record<EntityType, EntityField[]> = {
  employee: [
    { id: 'name', label: 'Full Name', type: 'text', required: true, placeholder: 'Enter full name' },
    { id: 'role', label: 'Job Role', type: 'text', required: true, placeholder: 'e.g., Senior Developer' },
    { id: 'email', label: 'Email', type: 'email', required: true, placeholder: '<EMAIL>' },
    { id: 'salary', label: 'Salary', type: 'currency', required: true, placeholder: '50000' },
    { id: 'currency', label: 'Currency', type: 'select', required: true, options: ['USD', 'EUR', 'MXN'], defaultValue: 'USD' },
    { id: 'status', label: 'Status', type: 'select', required: true, options: ['active', 'inactive'], defaultValue: 'active' }
  ],
  project: [
    // Basic project information only - modules will be added via edit operations
    { id: 'name', label: 'Project Name', type: 'text', required: true, placeholder: 'Enter project name' },
    { id: 'client', label: 'Client Name', type: 'text', required: true, placeholder: 'Enter client name' },
    { id: 'description', label: 'Description', type: 'text', required: false, placeholder: 'Project description' },
    { id: 'startDate', label: 'Start Date', type: 'date', required: true },
    { id: 'endDate', label: 'End Date', type: 'date', required: false }
  ],
  inventory: [
    { id: 'name', label: 'Item Name', type: 'text', required: true, placeholder: 'Enter item name' },
    { id: 'description', label: 'Description', type: 'text', required: true, placeholder: 'Item description' },
    { id: 'quantity', label: 'Quantity', type: 'number', required: true, placeholder: '0' },
    { id: 'unit', label: 'Unit', type: 'select', required: true, options: ['piezas', 'kg', 'litros', 'metros'], defaultValue: 'piezas' },
    { id: 'category', label: 'Category', type: 'select', required: true, options: ['Materia Prima', 'Herramientas y Equipos', 'Consumibles', 'Productos Terminados'], defaultValue: 'Materia Prima' },
    { id: 'cost', label: 'Cost per unit', type: 'currency', required: true, placeholder: '0.00', defaultValue: '0' },
    { id: 'location', label: 'Location', type: 'text', required: false, placeholder: 'Storage location' },
    { id: 'supplier', label: 'Supplier', type: 'text', required: false, placeholder: 'Supplier name' },
    // Payment method fields for purchase tracking (always shown in Financial Registry section)
    { id: 'paymentMethod', label: 'Payment Method', type: 'select', required: false, options: ['cash', 'credit_card', 'loan', 'bank_transfer', 'check'], defaultValue: 'cash' },
    { id: 'isRecurring', label: 'Recurring Payment', type: 'select', required: false, options: ['yes', 'no'], defaultValue: 'no' },
    { id: 'monthlyAmount', label: 'Monthly Payment', type: 'currency', required: false, placeholder: '0' },
    { id: 'totalMonths', label: 'Total Months', type: 'number', required: false, placeholder: '12' },
    { id: 'interestRate', label: 'Interest Rate (%)', type: 'number', required: false, placeholder: '5.5' },
    { id: 'dueDate', label: 'Due Date', type: 'date', required: false }
  ],
  financial: [
    { id: 'type', label: 'Type', type: 'select', required: true, options: ['income', 'expense'], defaultValue: 'expense' },
    { id: 'amount', label: 'Amount', type: 'currency', required: true, placeholder: '0' },
    { id: 'description', label: 'Description', type: 'text', required: true, placeholder: 'Transaction description' },
    { id: 'date', label: 'Date', type: 'date', required: true },
    { id: 'category', label: 'Category', type: 'text', required: true, placeholder: 'e.g., Materials, Sales' },
    { id: 'paymentMethod', label: 'Payment Method', type: 'select', required: true, options: ['cash', 'credit_card', 'loan', 'bank_transfer', 'check'], defaultValue: 'cash' },
    { id: 'isRecurring', label: 'Recurring Payment', type: 'select', required: false, options: ['yes', 'no'], defaultValue: 'no' },
    { id: 'monthlyAmount', label: 'Monthly Payment', type: 'currency', required: false, placeholder: '0' },
    { id: 'totalMonths', label: 'Total Months', type: 'number', required: false, placeholder: '12' },
    { id: 'interestRate', label: 'Interest Rate (%)', type: 'number', required: false, placeholder: '5.5' },
    { id: 'dueDate', label: 'Due Date', type: 'date', required: false }
  ],
  catalog: [
    { id: 'productName', label: 'Product Name', type: 'text', required: true, placeholder: 'Enter product name' },
    { id: 'productDescription', label: 'Description', type: 'text', required: true, placeholder: 'Product description' },
    { id: 'categoryLabel', label: 'Category', type: 'select', required: true, options: ['Product', 'Service'], defaultValue: 'Product' }
  ],
  task: [
    { id: 'name', label: 'Task Concept', type: 'text', required: true, placeholder: 'Enter task concept or description' },
    { id: 'assignedToId', label: 'Assign to Team Member', type: 'text', required: false, placeholder: 'Team member name (optional)' },
    { id: 'projectId', label: 'Assign to Project', type: 'text', required: false, placeholder: 'Project name (optional)' },
    { id: 'startDate', label: 'Start Date', type: 'date', required: false },
    { id: 'endDate', label: 'End Date', type: 'date', required: false }
  ]
};

// AI conversation response types
export interface AIConversationResponse {
  type: 'question' | 'confirmation' | 'data_ready' | 'error' | 'catalog_selection';
  message: string;
  data?: Record<string, any>;
  followUpQuestions?: string[];
  needsMoreInfo?: boolean;
  extractedData?: Record<string, any>;
  businessTemplate?: any;
  showModuleSelection?: boolean;
}

/**
 * Generate a conversational AI response for data creation
 */
export async function generateConversationalResponse(
  command: string,
  intent: ParsedIntent,
  entityType: EntityType,
  extractedData: Record<string, any>
): Promise<AIConversationResponse> {
  try {
    const response = await fetch('http://localhost:3001/api/ai/conversational-response', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command,
        intent,
        entityType,
        extractedData
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result as AIConversationResponse;

  } catch (error) {
    console.error('Error generating conversational response:', error);
    return {
      type: 'question',
      message: `I'll help you create a new ${entityType}. Could you tell me more about what you'd like to create?`,
      needsMoreInfo: true,
      followUpQuestions: [`What details should I know about this ${entityType}?`]
    };
  }
}

/**
 * Parse natural language command to extract intent and data
 */
export async function parseCommand(command: string): Promise<ParsedIntent> {
  try {
    const response = await fetch('http://localhost:3001/api/ai/parse-command', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ command }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result as ParsedIntent;

  } catch (error) {
    console.error('Error parsing command:', error);
    // Return a fallback response
    return {
      action: 'unknown',
      entityType: null,
      extractedData: {},
      confidence: 0,
      originalCommand: command
    };
  }
}

/**
 * Refine existing data based on follow-up command
 */
export async function refineData(
  originalData: Record<string, any>,
  entityType: EntityType,
  refinementCommand: string
): Promise<Record<string, any>> {
  try {
    const response = await fetch('http://localhost:3001/api/ai/refine-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        originalData,
        entityType,
        refinementCommand
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;

  } catch (error) {
    console.error('Error refining data:', error);
    return originalData; // Return original data if refinement fails
  }
}
