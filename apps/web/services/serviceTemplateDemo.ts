/**
 * Demo and Usage Examples for Service Templates
 * 
 * This file demonstrates how to use the service template system
 * and provides examples for testing and development.
 */

import { 
  fetchServiceTemplate, 
  fetchAvailableServiceTypes, 
  fetchServiceTemplatesByCategory,
  ServiceTemplate,
  ServiceField 
} from './serviceTemplates';

/**
 * Demo function to showcase service template usage
 */
export async function demonstrateServiceTemplates() {
  console.log('=== Service Template Demo ===\n');

  try {
    // 1. Get all available service types
    console.log('1. Available Service Types:');
    const serviceTypes = await fetchAvailableServiceTypes();
    console.log(serviceTypes.join(', '));
    console.log('');

    // 2. Fetch a specific service template
    console.log('2. Laser Cutting Service Template:');
    const laserCuttingTemplate = await fetchServiceTemplate('laser_cutting');
    if (laserCuttingTemplate) {
      console.log(`Display Name: ${laserCuttingTemplate.display_name}`);
      console.log(`Description: ${laserCuttingTemplate.description}`);
      console.log(`Category: ${laserCuttingTemplate.category}`);
      console.log(`Duration: ${laserCuttingTemplate.typical_duration}`);
      console.log(`Pricing Model: ${laserCuttingTemplate.pricing_model}`);
      console.log('Fields:');
      laserCuttingTemplate.fields.forEach((field, index) => {
        console.log(`  ${index + 1}. ${field.name} (${field.type}${field.required ? ', required' : ''})`);
        if (field.description) console.log(`     ${field.description}`);
        if (field.options) console.log(`     Options: ${field.options.join(', ')}`);
        if (field.unit) console.log(`     Unit: ${field.unit}`);
      });
    }
    console.log('');

    // 3. Get services by category
    console.log('3. Cutting Services:');
    const cuttingServices = await fetchServiceTemplatesByCategory('cutting');
    Object.entries(cuttingServices).forEach(([key, template]) => {
      console.log(`  - ${template.display_name} (${key})`);
    });
    console.log('');

    // 4. Generate form fields for a service
    console.log('4. Form Fields for Welding Service:');
    const weldingTemplate = await fetchServiceTemplate('welding_service');
    if (weldingTemplate) {
      const formFields = generateFormFieldsFromTemplate(weldingTemplate);
      console.log('Generated form structure:');
      console.log(JSON.stringify(formFields, null, 2));
    }

  } catch (error) {
    console.error('Demo error:', error);
  }
}

/**
 * Generate form field configuration from service template
 * This simulates how an AI system might use the templates
 */
export function generateFormFieldsFromTemplate(template: ServiceTemplate) {
  return {
    serviceName: template.display_name,
    description: template.description,
    estimatedDuration: template.typical_duration,
    pricingModel: template.pricing_model,
    formFields: template.fields.map(field => ({
      id: field.name.toLowerCase().replace(/\s+/g, '_'),
      label: field.name.replace(/_/g, ' '),
      type: field.type,
      required: field.required,
      placeholder: field.description || `Ingrese ${field.name.toLowerCase()}`,
      options: field.options || undefined,
      unit: field.unit || undefined,
      validation: {
        required: field.required,
        type: field.type
      }
    }))
  };
}

/**
 * Simulate AI-driven service specification
 * This shows how an AI might use templates to build service requests
 */
export async function simulateAIServiceBuilder(serviceType: string, userInput: Record<string, any>) {
  console.log(`\n=== AI Service Builder Simulation ===`);
  console.log(`Building service specification for: ${serviceType}`);
  
  try {
    const template = await fetchServiceTemplate(serviceType);
    if (!template) {
      throw new Error(`Template not found for ${serviceType}`);
    }

    console.log(`\nTemplate: ${template.display_name}`);
    console.log(`Category: ${template.category}`);
    
    // Validate user input against template
    const specification: Record<string, any> = {};
    const errors: string[] = [];
    
    template.fields.forEach(field => {
      const value = userInput[field.name];
      
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.name} is required`);
      } else if (value !== undefined) {
        // Basic validation
        if (field.type === 'number' && isNaN(Number(value))) {
          errors.push(`${field.name} must be a number`);
        } else if (field.options && !field.options.includes(value)) {
          errors.push(`${field.name} must be one of: ${field.options.join(', ')}`);
        } else {
          specification[field.name] = value;
        }
      }
    });

    if (errors.length > 0) {
      console.log('\nValidation Errors:');
      errors.forEach(error => console.log(`  - ${error}`));
      return null;
    }

    console.log('\nGenerated Service Specification:');
    console.log(JSON.stringify({
      serviceType: template.display_name,
      category: template.category,
      estimatedDuration: template.typical_duration,
      pricingModel: template.pricing_model,
      specifications: specification
    }, null, 2));

    return specification;

  } catch (error) {
    console.error('AI Builder error:', error);
    return null;
  }
}

/**
 * Example usage scenarios
 */
export async function runExamples() {
  // Example 1: Basic template fetching
  await demonstrateServiceTemplates();

  // Example 2: AI service building with valid input
  await simulateAIServiceBuilder('laser_cutting', {
    'Material': 'Acero inoxidable',
    'Espesor': 3,
    'Dimensiones': '100x200mm',
    'Cantidad': 10,
    'Acabado_Bordes': 'Desbarbado',
    'Tolerancia': '±0.2mm'
  });

  // Example 3: AI service building with invalid input
  await simulateAIServiceBuilder('welding_service', {
    'Tipo_Soldadura': 'MIG',
    'Material_Base': 'Acero al carbón',
    // Missing required fields to show validation
  });
}

// Uncomment to run examples in development
// runExamples();

/**
 * Quick usage example for console testing
 */
export async function quickExample() {
  console.log('=== Quick Service Template Example ===');

  // Fetch laser cutting template
  const template = await fetchServiceTemplate('laser_cutting');
  console.log(`Service: ${template?.display_name}`);
  console.log(`Fields: ${template?.fields.map(f => f.name).join(', ')}`);

  // Generate form structure
  if (template) {
    const form = generateFormFieldsFromTemplate(template);
    console.log('Generated form has', form.formFields.length, 'fields');
  }
}

// Run quick example (uncomment for testing)
// quickExample();
