/**
 * Finished Product Templates for Different Product Types
 * 
 * This module provides table column configurations based on finished product type.
 * Each product type has specific fields that are relevant for tracking finished goods.
 * These templates will be used to dynamically generate table headers and data structure.
 */

export interface TableColumn {
  id: string;
  header: string;
  description: string;
  type: 'text' | 'number' | 'currency' | 'date' | 'select' | 'status';
  required: boolean;
  unit?: string;
}

export interface ProductTypeTemplate {
  product_type: string;
  display_name: string;
  description: string;
  typical_categories: string[]; // Common categories for this product type
  columns: TableColumn[];
}

/**
 * Finished Product Templates Dictionary
 * Maps product types to their relevant table columns
 */
export const finishedProductTemplates: Record<string, ProductTypeTemplate> = {
  custom_structure: {
    product_type: 'custom_structure',
    display_name: 'Estructura Personalizada',
    description: 'Estructuras metálicas y construcciones personalizadas',
    typical_categories: ['Puertas', 'Ventanas', 'Marcos', 'Estructuras', 'Barandales'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote de producción',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Medidas del producto terminado',
        type: 'text',
        required: true
      },
      {
        id: 'quantity',
        header: 'Cantidad',
        description: 'Cantidad en inventario',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'quality_status',
        header: 'Estado Calidad',
        description: 'Estado de control de calidad',
        type: 'status',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo de producción por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'sale_price',
        header: 'Precio Venta',
        description: 'Precio de venta al cliente',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'completion_date',
        header: 'Fecha Terminado',
        description: 'Fecha de finalización',
        type: 'date',
        required: true
      },
      {
        id: 'customer',
        header: 'Cliente',
        description: 'Cliente destinatario',
        type: 'text',
        required: false
      }
    ]
  },

  custom_furniture: {
    product_type: 'custom_furniture',
    display_name: 'Mobiliario Personalizado',
    description: 'Muebles y mobiliario fabricado a medida',
    typical_categories: ['Mesas', 'Sillas', 'Escritorios', 'Estanterías', 'Gabinetes'],
    columns: [
      {
        id: 'batch',
        header: 'Lote',
        description: 'Número de lote de producción',
        type: 'text',
        required: true
      },
      {
        id: 'dimensions',
        header: 'Dimensiones',
        description: 'Medidas del mueble',
        type: 'text',
        required: true
      },
      {
        id: 'material_finish',
        header: 'Acabado',
        description: 'Tipo de acabado aplicado',
        type: 'text',
        required: true
      },
      {
        id: 'quantity',
        header: 'Cantidad',
        description: 'Cantidad en inventario',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'quality_status',
        header: 'Estado Calidad',
        description: 'Estado de control de calidad',
        type: 'status',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo de producción por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'sale_price',
        header: 'Precio Venta',
        description: 'Precio de venta al cliente',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'completion_date',
        header: 'Fecha Terminado',
        description: 'Fecha de finalización',
        type: 'date',
        required: true
      }
    ]
  },

  electronic_device: {
    product_type: 'electronic_device',
    display_name: 'Dispositivo Electrónico',
    description: 'Dispositivos y equipos electrónicos ensamblados',
    typical_categories: ['Controladores', 'Sensores', 'Paneles', 'Equipos de Control'],
    columns: [
      {
        id: 'serial_number',
        header: 'Número Serie',
        description: 'Número de serie único',
        type: 'text',
        required: true
      },
      {
        id: 'model',
        header: 'Modelo',
        description: 'Modelo del dispositivo',
        type: 'text',
        required: true
      },
      {
        id: 'firmware_version',
        header: 'Versión FW',
        description: 'Versión de firmware',
        type: 'text',
        required: true
      },
      {
        id: 'quantity',
        header: 'Cantidad',
        description: 'Cantidad en inventario',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'test_status',
        header: 'Estado Pruebas',
        description: 'Estado de pruebas funcionales',
        type: 'status',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo de producción por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'sale_price',
        header: 'Precio Venta',
        description: 'Precio de venta al cliente',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'assembly_date',
        header: 'Fecha Ensamble',
        description: 'Fecha de ensamblaje',
        type: 'date',
        required: true
      },
      {
        id: 'warranty_period',
        header: 'Garantía',
        description: 'Período de garantía',
        type: 'text',
        required: true
      }
    ]
  },

  machinery_equipment: {
    product_type: 'machinery_equipment',
    display_name: 'Maquinaria y Equipo',
    description: 'Maquinaria industrial y equipos especializados',
    typical_categories: ['Máquinas CNC', 'Equipos de Soldadura', 'Herramientas', 'Sistemas'],
    columns: [
      {
        id: 'asset_tag',
        header: 'Etiqueta',
        description: 'Etiqueta de activo',
        type: 'text',
        required: true
      },
      {
        id: 'specifications',
        header: 'Especificaciones',
        description: 'Especificaciones técnicas',
        type: 'text',
        required: true
      },
      {
        id: 'power_rating',
        header: 'Potencia',
        description: 'Potencia nominal',
        type: 'text',
        required: true
      },
      {
        id: 'quantity',
        header: 'Cantidad',
        description: 'Cantidad en inventario',
        type: 'number',
        required: true
      },
      {
        id: 'unit',
        header: 'Unidad',
        description: 'Unidad de medida',
        type: 'text',
        required: true
      },
      {
        id: 'operational_status',
        header: 'Estado Operativo',
        description: 'Estado operativo del equipo',
        type: 'status',
        required: true
      },
      {
        id: 'unit_cost',
        header: 'Costo Unit.',
        description: 'Costo de producción por unidad',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'sale_price',
        header: 'Precio Venta',
        description: 'Precio de venta al cliente',
        type: 'currency',
        required: true,
        unit: 'MXN'
      },
      {
        id: 'completion_date',
        header: 'Fecha Terminado',
        description: 'Fecha de finalización',
        type: 'date',
        required: true
      },
      {
        id: 'maintenance_schedule',
        header: 'Mantenimiento',
        description: 'Programa de mantenimiento',
        type: 'text',
        required: false
      }
    ]
  }
};

/**
 * Mock fetch function to simulate API call for product type template
 * @param productType - The product type to get template for
 * @returns Promise resolving to the product type template
 */
export async function fetchProductTypeTemplate(productType: string): Promise<ProductTypeTemplate | null> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
  
  const template = finishedProductTemplates[productType];
  
  if (!template) {
    console.warn(`Product type template for '${productType}' not found, using default`);
    return finishedProductTemplates.custom_structure || null; // Default fallback
  }
  
  return template;
}

/**
 * Get all available product types
 * @returns Promise resolving to array of product type keys
 */
export async function fetchAvailableProductTypes(): Promise<string[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 30));
  
  return Object.keys(finishedProductTemplates);
}
