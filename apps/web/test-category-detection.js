/**
 * Test script to verify inventory category detection improvements
 * Run this with: node test-category-detection.js
 */

// Import the detection function (you'll need to adjust the import path)
const { detectInventoryCategory } = require('./services/inventoryFieldGenerator.ts');

// Test cases for finished products
const testCases = [
  // English finished products
  { name: 'iPad', description: 'Apple tablet device', expected: 'Productos Terminados' },
  { name: 'Custom Door', description: 'Finished wooden door', expected: 'Productos Terminados' },
  { name: 'Office Chair', description: 'Ergonomic office furniture', expected: 'Productos Terminados' },
  { name: 'Steel Table', description: 'Finished metal table', expected: 'Productos Terminados' },
  { name: 'Computer Monitor', description: 'LCD display device', expected: 'Productos Terminados' },
  
  // Spanish finished products
  { name: 'Mesa de Acero', description: 'Mesa terminada de metal', expected: 'Productos Terminados' },
  { name: 'Puerta Personalizada', description: 'Puerta de madera acabada', expected: 'Productos Terminados' },
  { name: 'Silla de Oficina', description: 'Mueble ergonómico completo', expected: 'Productos Terminados' },
  { name: 'Estructura Metálica', description: 'Estructura fabricada lista', expected: 'Productos Terminados' },
  
  // Raw materials (should still work)
  { name: 'Steel Sheet', description: 'Raw metal sheet material', expected: 'Materia Prima' },
  { name: 'Lámina de Acero', description: 'Material base de metal', expected: 'Materia Prima' },
  
  // Tools/Equipment
  { name: 'Welding Machine', description: 'Industrial welding equipment', expected: 'Herramientas y Equipos' },
  { name: 'Soldadora', description: 'Equipo de soldadura industrial', expected: 'Herramientas y Equipos' },
  
  // Consumables
  { name: 'Welding Electrodes', description: 'Consumable welding supplies', expected: 'Consumibles' },
  { name: 'Electrodos', description: 'Insumos consumibles para soldadura', expected: 'Consumibles' }
];

console.log('Testing Inventory Category Detection Improvements\n');
console.log('='.repeat(60));

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  try {
    const result = detectInventoryCategory(testCase.name, testCase.description, 'fabrication');
    const passed = result.category === testCase.expected;
    
    console.log(`\nTest ${index + 1}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Item: ${testCase.name}`);
    console.log(`  Description: ${testCase.description}`);
    console.log(`  Expected: ${testCase.expected}`);
    console.log(`  Got: ${result.category}`);
    console.log(`  Confidence: ${result.confidence.toFixed(2)}`);
    console.log(`  Known Category: ${result.isKnownCategory}`);
    
    if (result.subcategory) {
      console.log(`  Subcategory: ${result.subcategory}`);
    }
    
    if (passed) {
      passedTests++;
    }
  } catch (error) {
    console.log(`\nTest ${index + 1}: ❌ ERROR`);
    console.log(`  Item: ${testCase.name}`);
    console.log(`  Error: ${error.message}`);
  }
});

console.log('\n' + '='.repeat(60));
console.log(`\nResults: ${passedTests}/${totalTests} tests passed (${((passedTests/totalTests)*100).toFixed(1)}%)`);

if (passedTests === totalTests) {
  console.log('🎉 All tests passed! Category detection is working correctly.');
} else {
  console.log('⚠️  Some tests failed. Review the detection logic.');
}
