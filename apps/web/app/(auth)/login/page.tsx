"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle, Input } from "@admin/ui";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement login logic
    console.log("Login attempt:", { email, password });
  };

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">Welcome Back</CardTitle>
        <CardDescription>
          Sign in to your admin account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-[var(--spacing-16)]">
          <div className="space-y-[var(--spacing-8)]">
            <label htmlFor="email" className="text-sm font-medium text-[var(--color-text-primary)]">
              Email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-[var(--spacing-8)]">
            <label htmlFor="password" className="text-sm font-medium text-[var(--color-text-primary)]">
              Password
            </label>
            <Input
              id="password"
              type="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
              required
            />
          </div>

          <Button type="submit" className="w-full">
            Sign In
          </Button>
        </form>

        <div className="mt-[var(--spacing-24)] text-center">
          <p className="text-sm text-[var(--color-text-secondary)]">
            Don&apos;t have an account?{" "}
            <Link
              href="/signup"
              className="text-[var(--color-frame-primary)] hover:underline font-medium"
            >
              Sign up
            </Link>
          </p>
        </div>

        <div className="mt-[var(--spacing-16)] text-center">
          <Link
            href="/"
            className="text-sm text-[var(--color-text-secondary)] hover:text-[var(--color-text-primary)] hover:underline"
          >
            ← Back to Dashboard
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
