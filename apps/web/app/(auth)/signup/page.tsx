"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle, Input } from "@admin/ui";

export default function SignupPage() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      alert("Passwords don't match!");
      return;
    }
    
    // TODO: Implement signup logic
    console.log("Signup attempt:", { name, email, password });
  };

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">Create Account</CardTitle>
        <CardDescription>
          Sign up for a new admin account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-[var(--spacing-16)]">
          <div className="space-y-[var(--spacing-8)]">
            <label htmlFor="name" className="text-sm font-medium text-[var(--color-text-primary)]">
              Full Name
            </label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>

          <div className="space-y-[var(--spacing-8)]">
            <label htmlFor="email" className="text-sm font-medium text-[var(--color-text-primary)]">
              Email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-[var(--spacing-8)]">
            <label htmlFor="password" className="text-sm font-medium text-[var(--color-text-primary)]">
              Password
            </label>
            <Input
              id="password"
              type="password"
              placeholder="Create a password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          <div className="space-y-[var(--spacing-8)]">
            <label htmlFor="confirmPassword" className="text-sm font-medium text-[var(--color-text-primary)]">
              Confirm Password
            </label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </div>

          <Button type="submit" className="w-full">
            Create Account
          </Button>
        </form>

        <div className="mt-[var(--spacing-24)] text-center">
          <p className="text-sm text-[var(--color-text-secondary)]">
            Already have an account?{" "}
            <Link
              href="/login"
              className="text-[var(--color-frame-primary)] hover:underline font-medium"
            >
              Sign in
            </Link>
          </p>
        </div>

        <div className="mt-[var(--spacing-16)] text-center">
          <Link
            href="/"
            className="text-sm text-[var(--color-text-secondary)] hover:text-[var(--color-text-primary)] hover:underline"
          >
            ← Back to Dashboard
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
