"use client";

import { CalendarTimeline, type CalendarTimelineTask } from "@admin/ui";
import { useEffect, useState } from "react";

export default function DebugCalendar() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  // Exact same data as Storybook default story
  const tasks: CalendarTimelineTask[] = [
    {
      id: '1',
      name: 'Dise<PERSON> inicial',
      startDate: 2,
      endDate: 8,
      employees: [
        {
          id: 'emp1',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      startDate: 10,
      endDate: 20,
      employees: [
        {
          id: 'emp2',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        }
      ]
    }
  ];

  useEffect(() => {
    // Debug function to inspect the DOM
    const inspectCalendarTimeline = () => {
      const calendarElement = document.querySelector('.calendar-timeline');
      if (calendarElement) {
        const taskContainers = calendarElement.querySelectorAll('.task-container');
        const gridContainers = calendarElement.querySelectorAll('.grid-container');
        const dayHeaders = calendarElement.querySelectorAll('.day-header');
        
        const computedStyles = window.getComputedStyle(calendarElement);
        const taskStyles = taskContainers.length > 0 ? window.getComputedStyle(taskContainers[0] as Element) : null;
        const gridStyles = gridContainers.length > 0 ? window.getComputedStyle(gridContainers[0] as Element) : null;

        setDebugInfo({
          calendarElement: {
            className: calendarElement.className,
            border: computedStyles.border,
            borderWidth: computedStyles.borderWidth,
            borderStyle: computedStyles.borderStyle,
            borderColor: computedStyles.borderColor,
            display: computedStyles.display,
            position: computedStyles.position,
            isolation: computedStyles.isolation,
            contain: computedStyles.contain
          },
          taskContainer: taskStyles ? {
            border: taskStyles.border,
            borderWidth: taskStyles.borderWidth,
            borderStyle: taskStyles.borderStyle,
            borderColor: taskStyles.borderColor,
            position: taskStyles.position,
            zIndex: taskStyles.zIndex,
            pointerEvents: taskStyles.pointerEvents,
            gridColumn: taskStyles.gridColumn
          } : null,
          gridContainer: gridStyles ? {
            display: gridStyles.display,
            gridTemplateColumns: gridStyles.gridTemplateColumns,
            gap: gridStyles.gap,
            border: gridStyles.border,
            position: gridStyles.position
          } : null,
          counts: {
            taskContainers: taskContainers.length,
            gridContainers: gridContainers.length,
            dayHeaders: dayHeaders.length
          }
        });
      }
    };

    // Run inspection after component mounts
    const timer = setTimeout(inspectCalendarTimeline, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-2xl font-bold mb-8">CalendarTimeline Debug Page</h1>
      
      {/* Component under test */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Component (inspect with browser dev tools)</h2>
        <div className="border-2 border-red-500 p-4" style={{ backgroundColor: '#f9f9f9' }}>
          <CalendarTimeline
            month="Junio"
            daysInMonth={30}
            tasks={tasks}
            onTaskClick={(task) => {
              console.log('Task clicked:', task);
              alert(`Clicked: ${task.name}`);
            }}
          />
        </div>
      </div>

      {/* Debug information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Calendar Element Styles</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(debugInfo.calendarElement, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Task Container Styles</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(debugInfo.taskContainer, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Grid Container Styles</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(debugInfo.gridContainer, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Element Counts</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(debugInfo.counts, null, 2)}
          </pre>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-8 p-4 bg-blue-50 rounded">
        <h3 className="font-semibold mb-2">Debug Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Open browser dev tools (F12)</li>
          <li>Compare this page with Storybook side by side</li>
          <li>Inspect the .calendar-timeline element</li>
          <li>Check computed styles for .task-container elements</li>
          <li>Look for differences in grid-template-columns</li>
          <li>Test hover behavior on task containers</li>
          <li>Check if pointer-events are working</li>
        </ol>
      </div>

      {/* Test buttons */}
      <div className="mt-6 space-x-4">
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Reload Page
        </button>
        <button 
          onClick={() => {
            const timer = setTimeout(() => {
              const inspectCalendarTimeline = () => {
                const calendarElement = document.querySelector('.calendar-timeline');
                if (calendarElement) {
                  console.log('Calendar Timeline Element:', calendarElement);
                  console.log('Computed Styles:', window.getComputedStyle(calendarElement));
                  const taskContainers = calendarElement.querySelectorAll('.task-container');
                  taskContainers.forEach((task, index) => {
                    console.log(`Task ${index}:`, task);
                    console.log(`Task ${index} Styles:`, window.getComputedStyle(task));
                  });
                }
              };
              inspectCalendarTimeline();
            }, 100);
          }} 
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Log to Console
        </button>
      </div>
    </div>
  );
}
