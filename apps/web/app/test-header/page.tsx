"use client";

import { Header } from "@admin/ui";

export default function TestHeader() {
  return (
    <div className="min-h-screen bg-white">
      <Header
        logoText="LOGO"
        addButtonText="Añadir"
        onAddClick={() => console.log('Add clicked')}
        onProfileClick={() => console.log('Profile clicked')}
        className="border-b border-gray-200"
      />
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Header Test Page</h1>
        <p className="text-gray-600">This page tests the Header component to match the provided design.</p>
        <div className="mt-8 p-4 bg-gray-50 rounded">
          <h2 className="font-semibold mb-2">Component Features:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Logo text on the left</li>
            <li>Add button with icon and "Añadir" text</li>
            <li>Profile image on the right</li>
            <li>Exact styling to match the design</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
