"use client";

import React, { useState } from 'react';
import { parseCommand } from '../../services/aiCommandParser';

export default function TestTaskAssignmentPage() {
  const [command, setCommand] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testCommands = [
    "assign a new task to <PERSON>",
    "create task for <PERSON> in project ABC", 
    "assign installation task to <PERSON>",
    "add task to <PERSON> project",
    "create new employee named <PERSON>",
    "add employee <PERSON>",
    "assign task to <PERSON> in Kitchen Renovation project"
  ];

  const handleTest = async (testCommand?: string) => {
    const commandToTest = testCommand || command;
    if (!commandToTest.trim()) return;

    setLoading(true);
    try {
      const parseResult = await parseCommand(commandToTest);
      setResult(parseResult);
    } catch (error) {
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const handleQuickTest = (testCommand: string) => {
    setCommand(testCommand);
    handleTest(testCommand);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Task Assignment Command Parser Test
        </h1>

        {/* Manual Test Input */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Manual Test</h2>
          <div className="flex gap-4 mb-4">
            <input
              type="text"
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              placeholder="Enter a command to test..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={() => handleTest()}
              disabled={loading || !command.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Testing...' : 'Test'}
            </button>
          </div>
        </div>

        {/* Quick Test Commands */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Quick Test Commands</h2>
          <div className="grid gap-2">
            {testCommands.map((testCommand, index) => (
              <button
                key={index}
                onClick={() => handleQuickTest(testCommand)}
                className="text-left px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                "{testCommand}"
              </button>
            ))}
          </div>
        </div>

        {/* Results */}
        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Parse Result</h2>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
            
            {/* Analysis */}
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Analysis:</h3>
              <div className="text-blue-800 space-y-1">
                {result.success === false && (
                  <div className="text-red-600">❌ Command failed: {result.error}</div>
                )}
                {result.success !== false && result.intent && (
                  <>
                    <div>✅ Action: {result.intent.action}</div>
                    <div>✅ Entity Type: {result.intent.entityType}</div>
                    {result.intent.editType && (
                      <div>✅ Edit Type: {result.intent.editType}</div>
                    )}
                    {result.intent.targetProject && (
                      <div>✅ Target Project: {result.intent.targetProject}</div>
                    )}
                    {result.intent.extractedData && (
                      <div>✅ Extracted Data: {JSON.stringify(result.intent.extractedData)}</div>
                    )}
                    <div>✅ Confidence: {result.intent.confidence}</div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
