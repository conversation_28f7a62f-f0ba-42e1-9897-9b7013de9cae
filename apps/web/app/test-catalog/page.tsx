"use client";

import { useState } from "react";
import { CatalogSelection } from "@admin/ui";
import { useCatalog } from "../../contexts";

export default function TestCatalogPage() {
  const { items: catalogItems } = useCatalog();
  const [selectedItems, setSelectedItems] = useState([]);

  const handleSelectionChange = (items: any) => {
    setSelectedItems(items);
    console.log('Selected items:', items);
  };

  return (
    <div className="min-h-screen bg-[var(--color-background-primary)] p-[var(--spacing-16)]">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-8">Catalog Selection Test</h1>
        
        <div className="mb-4">
          <p>Catalog items loaded: {catalogItems.length}</p>
          <p>Selected items: {selectedItems.length}</p>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-lg">
          <CatalogSelection
            catalogItems={catalogItems}
            selectedItems={selectedItems}
            onSelectionChange={handleSelectionChange}
            loading={false}
          />
        </div>

        {selectedItems.length > 0 && (
          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4">Selected Items:</h2>
            <pre>{JSON.stringify(selectedItems, null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
