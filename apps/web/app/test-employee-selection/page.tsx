"use client";

import { useState } from "react";
import { DataCreationModal } from "@admin/ui";
import { ContextDataResult, SelectableItem } from "@admin/ui";

// Mock employee data
const mockEmployees: SelectableItem[] = [
  {
    id: 'emp-1',
    name: '<PERSON>',
    description: 'Ingeniero',
    type: 'team_member',
    category: 'Engineering',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 45
    },
    isSelected: false
  },
  {
    id: 'emp-2',
    name: '<PERSON>',
    description: 'Supervisor de Producción',
    type: 'team_member',
    category: 'Production',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 50
    },
    isSelected: false
  },
  {
    id: 'emp-3',
    name: '<PERSON>',
    description: 'Ingeniera de Diseño',
    type: 'team_member',
    category: 'Design',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 48
    },
    isSelected: false
  },
  {
    id: 'emp-4',
    name: 'Roberto Silva',
    description: 'Técnico Especialista',
    type: 'team_member',
    category: 'Technical',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 42
    },
    isSelected: false
  },
  {
    id: 'emp-5',
    name: 'Laura Martínez',
    description: 'Coordinadora de Calidad',
    type: 'team_member',
    category: 'Quality',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 46
    },
    isSelected: false
  },
  {
    id: 'emp-6',
    name: 'Fernando Torres',
    description: 'Electricista Senior',
    type: 'team_member',
    category: 'Electrical',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 44
    },
    isSelected: false
  },
  {
    id: 'emp-7',
    name: 'Isabel Moreno',
    description: 'Arquitecta de Proyectos',
    type: 'team_member',
    category: 'Architecture',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 55
    },
    isSelected: false
  },
  {
    id: 'emp-8',
    name: 'Diego Ramírez',
    description: 'Soldador Certificado',
    type: 'team_member',
    category: 'Welding',
    metadata: {
      email: '<EMAIL>',
      hourlyRate: 40
    },
    isSelected: false
  },
  {
    id: 'emp-9',
    name: 'Carmen López',
    description: 'Gestora de Proyectos',
    type: 'team_member',
    category: 'Management',
    metadata: {
      avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=100&h=100&fit=crop&crop=face',
      email: '<EMAIL>',
      hourlyRate: 52
    },
    isSelected: false
  },
  {
    id: 'emp-10',
    name: 'Miguel Santos',
    description: 'Operador de Maquinaria',
    type: 'team_member',
    category: 'Operations',
    metadata: {
      email: '<EMAIL>',
      hourlyRate: 38
    },
    isSelected: false
  }
];

export default function TestEmployeeSelectionPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [employees, setEmployees] = useState<SelectableItem[]>(mockEmployees);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);

  const contextData: ContextDataResult = {
    items: employees,
    totalCount: employees.length,
    categories: ['Engineering', 'Production', 'Design', 'Technical', 'Quality', 'Electrical', 'Architecture', 'Welding', 'Management', 'Operations']
  };

  const handleSelectionChange = (itemId: string, isSelected: boolean) => {
    setEmployees(prev => 
      prev.map(emp => 
        emp.id === itemId ? { ...emp, isSelected } : emp
      )
    );

    if (isSelected) {
      setSelectedEmployees(prev => [...prev, itemId]);
    } else {
      setSelectedEmployees(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleConfirmSelection = () => {
    const selected = employees.filter(emp => emp.isSelected);
    console.log('Selected employees:', selected);
    alert(`Selected ${selected.length} employees:\n${selected.map(emp => emp.name).join('\n')}`);
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-[var(--color-background-secondary)] p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-[var(--color-text-primary)] mb-8">
          Employee Selection Modal Test
        </h1>
        
        <div className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] p-6 mb-8">
          <h2 className="text-lg font-semibold mb-4">Test the Employee Selection Modal</h2>
          <p className="text-[var(--color-text-secondary)] mb-6">
            This test page verifies that the employee selection modal:
          </p>
          <ul className="list-disc list-inside text-[var(--color-text-secondary)] mb-6 space-y-2">
            <li>Shows compact employee cards (like the design you provided)</li>
            <li>Maintains proper container height with scrollable overflow</li>
            <li>Displays employee avatars, names, and roles correctly</li>
            <li>Handles selection state properly</li>
          </ul>
          
          <button
            onClick={() => setIsModalOpen(true)}
            className="px-6 py-3 bg-[var(--color-primary)] text-white rounded-[var(--radius-4)] font-medium hover:bg-[var(--color-primary-hover)] transition-colors"
          >
            Open Employee Selection Modal
          </button>
          
          {selectedEmployees.length > 0 && (
            <div className="mt-4 p-4 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <p className="text-sm text-[var(--color-text-secondary)]">
                Currently selected: {selectedEmployees.length} employees
              </p>
            </div>
          )}
        </div>

        <DataCreationModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          entityType="project"
          fields={[]}
          onFieldChange={() => {}}
          onSubmit={() => {}}
          aiMessage="I found 10 team members available. Please select who you'd like to add to the project:"
          showContextSelection={true}
          contextData={contextData}
          onContextSelectionChange={handleSelectionChange}
          onContextSelectionConfirm={handleConfirmSelection}
        />
      </div>
    </div>
  );
}
