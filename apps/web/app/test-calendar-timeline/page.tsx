"use client";

import { CalendarTimeline, type CalendarTimelineTask } from "@admin/ui";

const sampleTasks: CalendarTimelineTask[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON> financiero',
    startDate: 2,
    endDate: 5,
    employees: [
      {
        id: 'emp1',
        name: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
  {
    id: '2',
    name: '<PERSON><PERSON> planos',
    startDate: 3,
    endDate: 8,
    employees: [
      {
        id: 'emp2',
        name: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp3',
        name: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON> <PERSON> material<PERSON>',
    startDate: 10,
    endDate: 15,
    employees: [
      {
        id: 'emp4',
        name: '<PERSON>',
      },
      {
        id: 'emp5',
        name: '<PERSON> <PERSON>',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
  {
    id: '4',
    name: 'Instalación eléctrica',
    startDate: 18,
    endDate: 25,
    employees: [
      {
        id: 'emp7',
        name: 'Fernando Torres',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      },
      {
        id: 'emp8',
        name: 'Isabel Moreno',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
      },
    ],
  },
];

export default function TestCalendarTimelinePage() {
  return (
    <div className="min-h-screen bg-[var(--color-background-secondary)] p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-[var(--color-text-primary)] mb-8">
          CalendarTimeline Component Test
        </h1>
        
        <div className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] p-8 shadow-lg">
          <CalendarTimeline
            month="Junio"
            nextMonth="Julio"
            daysInMonth={30}
            tasks={sampleTasks}
            onTaskClick={(task) => {
              alert(`Clicked on: ${task.name}\nEmployees: ${task.employees.map(e => e.name).join(', ')}`);
            }}
          />
        </div>

        <div className="mt-8 bg-[var(--color-background-primary)] rounded-[var(--radius-8)] p-8 shadow-lg">
          <h2 className="text-lg font-semibold text-[var(--color-text-primary)] mb-4">
            Usage Example
          </h2>
          <pre className="bg-[var(--color-background-secondary)] p-4 rounded-[var(--radius-4)] text-sm overflow-x-auto">
{`import { CalendarTimeline, type CalendarTimelineTask } from "@admin/ui";

const tasks: CalendarTimelineTask[] = [
  {
    id: '1',
    name: 'Anticipo financiero',
    startDate: 2,
    endDate: 5,
    employees: [
      {
        id: 'emp1',
        name: 'Ana García',
        avatar: 'https://example.com/avatar.jpg',
      },
    ],
  },
];

<CalendarTimeline
  month="Junio"
  nextMonth="Julio"
  daysInMonth={30}
  tasks={tasks}
  onTaskClick={(task) => console.log('Clicked:', task)}
/>`}
          </pre>
        </div>
      </div>
    </div>
  );
}
