"use client";

import {
  CalendarTimeline,
  type CalendarTimelineTask
} from "@admin/ui";

export default function TestTimeline() {

  // Sample data for CalendarTimeline component
  const calendarTasks: CalendarTimelineTask[] = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON> inicial',
      startDate: 2,
      endDate: 8,
      employees: [
        {
          id: 'emp1',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '2',
      name: '<PERSON>arroll<PERSON>',
      startDate: 10,
      endDate: 20,
      employees: [
        {
          id: 'emp2',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        },
        {
          id: 'emp3',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '3',
      name: 'Testing',
      startDate: 22,
      endDate: 28,
      employees: [
        {
          id: 'emp4',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white p-8">
      <div className="max-w-7xl mx-auto space-y-12">
        <div>
          <h1 className="text-3xl font-bold mb-4">CalendarTimeline Test Page</h1>
          <p className="text-gray-600 mb-8">
            This page tests the CalendarTimeline component to ensure consistent alignment and styling
            between Storybook and the web app.
          </p>
        </div>

        {/* CalendarTimeline Component */}
        <section className="space-y-4">
          <div className="border-b pb-4">
            <h2 className="text-2xl font-semibold mb-2">Calendar Timeline Component</h2>
            <p className="text-gray-600">
              Grid-based calendar view with task spans and employee avatars
            </p>
          </div>
          <div className="border border-gray-200 rounded-lg p-6">
            <CalendarTimeline
              month="Junio"
              nextMonth="Julio"
              daysInMonth={30}
              tasks={calendarTasks}
              onTaskClick={(task) => console.log('Calendar task clicked:', task)}
            />
          </div>
        </section>

        {/* Component Features Summary */}
        <section className="bg-gray-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Component Features Tested:</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Layout & Positioning:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>CSS Grid alignment</li>
                <li>Task bar positioning</li>
                <li>Avatar and text alignment</li>
                <li>Date header alignment</li>
                <li>Bottom border separation</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Interactive Elements:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Click handlers for tasks</li>
                <li>Hover effects and date highlighting</li>
                <li>Task container transitions</li>
                <li>Employee avatar display</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
