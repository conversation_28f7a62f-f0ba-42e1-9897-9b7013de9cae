@import '@admin/design-tokens/css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  /* Global border rule with CalendarTimeline exception */
  *:not(.calendar-timeline):not(.calendar-timeline *) {
    @apply border-[var(--color-stroke)];
  }
  body {
    @apply bg-[var(--color-background-primary)] text-[var(--color-text-primary)];
  }

  /* Material Symbols styling */
  .material-symbols-outlined {
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    word-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: 'liga';
  }
}

@layer components {
  .modal-sub-header {
    @apply font-medium text-sm text-[var(--color-text-secondary)] font-sans mb-[var(--spacing-8)];
  }

  .modal-section {
    @apply mb-[var(--spacing-40)];
  }

  .modal-main-header {
    @apply mb-[var(--spacing-52)];
  }

  /* Transition Utilities */
  .transition-smooth {
    transition: var(--transition-common-all);
  }

  .transition-fade {
    transition: var(--transition-common-fade);
  }

  .transition-transform-smooth {
    transition: var(--transition-common-transform);
  }

  .transition-colors-smooth {
    transition: var(--transition-common-colors);
  }

  .transition-modal {
    transition: var(--transition-common-modal);
  }

  .transition-button {
    transition: var(--transition-common-button);
  }

  .transition-card {
    transition: var(--transition-common-card);
  }

  /* Enhanced hover states */
  .hover-lift {
    @apply transition-card;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-outer2);
  }

  .hover-bg {
    @apply transition-colors-smooth;
  }

  .hover-bg:hover {
    background-color: color-mix(in srgb, var(--color-background-primary) 50%, var(--color-background-secondary) 50%);
  }

  .hover-scale {
    @apply transition-transform-smooth;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  .hover-fade {
    @apply transition-fade;
  }

  .hover-fade:hover {
    opacity: 0.8;
  }

  /* Modal animations */
  .modal-backdrop {
    @apply transition-fade;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .modal-backdrop.entering {
    opacity: 0;
  }

  .modal-backdrop.entered {
    opacity: 1;
  }

  .modal-backdrop.exiting {
    opacity: 0;
  }

  .modal-content {
    @apply transition-modal;
  }

  .modal-content.entering {
    opacity: 0;
    transform: scale(0.95);
  }

  .modal-content.entered {
    opacity: 1;
    transform: scale(1);
  }

  .modal-content.exiting {
    opacity: 0;
    transform: scale(0.95);
  }

  /* Reduce Next.js/Turbopack indicator visibility */
  div[data-nextjs-toast="true"],
  div[data-nextjs-toast="true"] *,
  div[data-nextjs-toast-wrapper="true"],
  div[data-nextjs-toast-wrapper="true"] *,
  div[data-next-badge-root="true"],
  div[data-next-badge-root="true"] *,
  div[data-next-badge="true"],
  div[data-next-badge="true"] *,
  button[data-next-mark="true"],
  button[data-next-mark="true"] * {
    opacity: 0.2 !important;
    transition: opacity 0.3s ease !important;
  }

  /* On hover, make it more visible for interaction */
  div[data-nextjs-toast="true"]:hover,
  div[data-nextjs-toast="true"]:hover *,
  div[data-nextjs-toast-wrapper="true"]:hover,
  div[data-nextjs-toast-wrapper="true"]:hover *,
  div[data-next-badge-root="true"]:hover,
  div[data-next-badge-root="true"]:hover *,
  div[data-next-badge="true"]:hover,
  div[data-next-badge="true"]:hover *,
  button[data-next-mark="true"]:hover,
  button[data-next-mark="true"]:hover * {
    opacity: 0.8 !important;
  }
}
