"use client";

import React, { useState } from 'react';
import { detectInventoryCategory, generateInventoryFields } from '../../services/inventoryFieldGenerator';
import { BusinessType } from '../../contexts/types';

interface TestCase {
  name: string;
  description: string;
  expected: string;
  language: 'en' | 'es';
}

const testCases: TestCase[] = [
  // English finished products
  { name: 'iPad', description: 'Apple tablet device', expected: 'Productos Terminados', language: 'en' },
  { name: 'Custom Door', description: 'Finished wooden door', expected: 'Productos Terminados', language: 'en' },
  { name: 'Office Chair', description: 'Ergonomic office furniture', expected: 'Productos Terminados', language: 'en' },
  { name: 'Steel Table', description: 'Finished metal table', expected: 'Productos Terminados', language: 'en' },
  { name: 'Computer Monitor', description: 'LCD display device', expected: 'Productos Terminados', language: 'en' },
  
  // Spanish finished products
  { name: 'Mesa de Acero', description: 'Mesa terminada de metal', expected: 'Productos Terminados', language: 'es' },
  { name: 'Puerta Personalizada', description: 'Puerta de madera acabada', expected: 'Productos Terminados', language: 'es' },
  { name: 'Silla de Oficina', description: 'Mueble ergonómico completo', expected: 'Productos Terminados', language: 'es' },
  { name: 'Estructura Metálica', description: 'Estructura fabricada lista', expected: 'Productos Terminados', language: 'es' },
  
  // Raw materials (should still work)
  { name: 'Steel Sheet', description: 'Raw metal sheet material', expected: 'Materia Prima', language: 'en' },
  { name: 'Lámina de Acero', description: 'Material base de metal', expected: 'Materia Prima', language: 'es' },
  
  // Tools/Equipment
  { name: 'Welding Machine', description: 'Industrial welding equipment', expected: 'Herramientas y Equipos', language: 'en' },
  { name: 'Soldadora', description: 'Equipo de soldadura industrial', expected: 'Herramientas y Equipos', language: 'es' },
  
  // Consumables
  { name: 'Welding Electrodes', description: 'Consumable welding supplies', expected: 'Consumibles', language: 'en' },
  { name: 'Electrodos', description: 'Insumos consumibles para soldadura', expected: 'Consumibles', language: 'es' }
];

export default function TestInventoryDetection() {
  const [businessType, setBusinessType] = useState<BusinessType>('fabrication');
  const [customName, setCustomName] = useState('');
  const [customDescription, setCustomDescription] = useState('');
  const [customResult, setCustomResult] = useState<any>(null);

  const runCustomTest = () => {
    if (!customName.trim() || !customDescription.trim()) return;
    
    const result = detectInventoryCategory(customName, customDescription, businessType);
    const fields = generateInventoryFields(customName, customDescription, businessType);
    setCustomResult({ detection: result, fields });
  };

  const getResultColor = (actual: string, expected: string) => {
    return actual === expected ? 'text-green-600' : 'text-red-600';
  };

  const getResultIcon = (actual: string, expected: string) => {
    return actual === expected ? '✅' : '❌';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Inventory Category Detection Test
        </h1>

        {/* Custom Test Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Custom Test</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Type
              </label>
              <select
                value={businessType}
                onChange={(e) => setBusinessType(e.target.value as BusinessType)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="fabrication">Fabrication</option>
                <option value="retail">Retail</option>
                <option value="services">Services</option>
                <option value="manufacturing">Manufacturing</option>
                <option value="construction">Construction</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Item Name
              </label>
              <input
                type="text"
                value={customName}
                onChange={(e) => setCustomName(e.target.value)}
                placeholder="Enter item name"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={customDescription}
              onChange={(e) => setCustomDescription(e.target.value)}
              placeholder="Enter item description"
              rows={3}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          <button
            onClick={runCustomTest}
            disabled={!customName.trim() || !customDescription.trim()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          >
            Test Detection
          </button>

          {customResult && (
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <h3 className="font-semibold mb-2">Detection Result:</h3>
              <div className="space-y-1 text-sm">
                <p><strong>Category:</strong> {customResult.detection.category}</p>
                <p><strong>Subcategory:</strong> {customResult.detection.subcategory || 'None'}</p>
                <p><strong>Confidence:</strong> {customResult.detection.confidence.toFixed(2)}</p>
                <p><strong>Known Category:</strong> {customResult.detection.isKnownCategory ? 'Yes' : 'No'}</p>
                {customResult.detection.missingCategoryInfo && (
                  <p className="text-orange-600">
                    <strong>Warning:</strong> {customResult.detection.missingCategoryInfo.message}
                  </p>
                )}
                <p><strong>Generated Fields:</strong> {customResult.fields.fields.length} fields</p>
              </div>
            </div>
          )}
        </div>

        {/* Automated Test Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Automated Test Results</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-2 text-left">Result</th>
                  <th className="px-4 py-2 text-left">Item Name</th>
                  <th className="px-4 py-2 text-left">Description</th>
                  <th className="px-4 py-2 text-left">Expected</th>
                  <th className="px-4 py-2 text-left">Detected</th>
                  <th className="px-4 py-2 text-left">Confidence</th>
                  <th className="px-4 py-2 text-left">Lang</th>
                </tr>
              </thead>
              <tbody>
                {testCases.map((testCase, index) => {
                  const result = detectInventoryCategory(testCase.name, testCase.description, businessType);
                  const passed = result.category === testCase.expected;
                  
                  return (
                    <tr key={index} className={`border-t ${passed ? 'bg-green-50' : 'bg-red-50'}`}>
                      <td className="px-4 py-2">
                        <span className="text-lg">
                          {getResultIcon(result.category, testCase.expected)}
                        </span>
                      </td>
                      <td className="px-4 py-2 font-medium">{testCase.name}</td>
                      <td className="px-4 py-2 text-sm text-gray-600">{testCase.description}</td>
                      <td className="px-4 py-2 text-sm">{testCase.expected}</td>
                      <td className={`px-4 py-2 text-sm font-medium ${getResultColor(result.category, testCase.expected)}`}>
                        {result.category}
                      </td>
                      <td className="px-4 py-2 text-sm">{result.confidence.toFixed(2)}</td>
                      <td className="px-4 py-2 text-sm">{testCase.language.toUpperCase()}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          <div className="mt-4 p-4 bg-blue-50 rounded-md">
            <h3 className="font-semibold text-blue-800 mb-2">Test Summary</h3>
            <p className="text-blue-700 text-sm">
              {testCases.filter(testCase => {
                const result = detectInventoryCategory(testCase.name, testCase.description, businessType);
                return result.category === testCase.expected;
              }).length} / {testCases.length} tests passed
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
