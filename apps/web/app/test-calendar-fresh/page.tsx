"use client";

import { CalendarTimeline, type CalendarTimelineTask } from "@admin/ui";

export default function TestCalendarFresh() {
  // Minimal test data - exactly like Storybook
  const tasks: CalendarTimelineTask[] = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON> inicial',
      startDate: 2,
      endDate: 8,
      employees: [
        {
          id: 'emp1',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '2',
      name: '<PERSON>arroll<PERSON>',
      startDate: 10,
      endDate: 20,
      employees: [
        {
          id: 'emp2',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '3',
      name: 'Testing',
      startDate: 22,
      endDate: 28,
      employees: [
        {
          id: 'emp3',
          name: '<PERSON>',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Minimal container - no extra styling */}
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-8">Fresh CalendarTimeline Test</h1>
        
        {/* Test 1: Minimal container */}
        <div className="mb-12">
          <h2 className="text-lg font-semibold mb-4">Test 1: Minimal Container</h2>
          <div className="border border-gray-200 p-4">
            <CalendarTimeline
              month="Junio"
              daysInMonth={30}
              tasks={tasks}
              onTaskClick={(task) => console.log('Task clicked:', task)}
            />
          </div>
        </div>

        {/* Test 2: No container styling */}
        <div className="mb-12">
          <h2 className="text-lg font-semibold mb-4">Test 2: No Container</h2>
          <CalendarTimeline
            month="Junio"
            daysInMonth={30}
            tasks={tasks}
            onTaskClick={(task) => console.log('Task clicked:', task)}
          />
        </div>

        {/* Test 3: Storybook-like container */}
        <div className="mb-12">
          <h2 className="text-lg font-semibold mb-4">Test 3: Storybook-like Container</h2>
          <div style={{ padding: '1rem', backgroundColor: '#ffffff' }}>
            <CalendarTimeline
              month="Junio"
              daysInMonth={30}
              tasks={tasks}
              onTaskClick={(task) => console.log('Task clicked:', task)}
            />
          </div>
        </div>

        {/* Test 4: Isolated with CSS reset */}
        <div className="mb-12">
          <h2 className="text-lg font-semibold mb-4">Test 4: CSS Reset Container</h2>
          <div 
            style={{ 
              padding: '1rem', 
              backgroundColor: '#ffffff',
              border: 'none',
              boxSizing: 'border-box'
            }}
          >
            <div style={{ all: 'initial', fontFamily: 'inherit' }}>
              <CalendarTimeline
                month="Junio"
                daysInMonth={30}
                tasks={tasks}
                onTaskClick={(task) => console.log('Task clicked:', task)}
              />
            </div>
          </div>
        </div>

        {/* Debug info */}
        <div className="mt-12 p-4 bg-gray-50 rounded">
          <h3 className="font-semibold mb-2">Debug Information:</h3>
          <ul className="text-sm space-y-1">
            <li>• Tasks count: {tasks.length}</li>
            <li>• Month: Junio</li>
            <li>• Days in month: 30</li>
            <li>• Check browser console for click events</li>
            <li>• Compare hover behavior with Storybook</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
