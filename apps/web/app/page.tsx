"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, MessageBar, ExpandIcon, CollapseIcon, ChevronLeftIcon, ChevronRightIcon, FadeIn, ScaleIn, DataCreationModal, FormField } from "@admin/ui";
import { BusinessTypeSwitcher } from "../components/BusinessTypeSwitcher";
import { FinanceSection } from "../components/finance";
import { CatalogSection } from "../components/catalog";
import { TeamSection } from "../components/team";
import { InventorySection } from "../components/inventory";
import { ProjectSection } from "../components/project";
import { TimelineSection, type TimelineSectionRef } from "../components/timeline";
import { LogisticsSection } from "../components/logistics/LogisticsSection";
import { processCommand, sessionManager, generateFormFields, validateSession, getEntityDisplayName, DataCreationSession } from "../services/aiCommandParser";
import { ProjectEditSession } from "../services/projectEditingService";
import { taskService } from "../services/taskService";
// Removed generateConversationalResponse - no longer needed
// Removed createEntity import - using context methods directly for backend integration
import { useUser, useTeam, useProject, useInventory, useCatalog, useFinance } from "../contexts";
import {
  setContextFunctions,
  processConfirmationResponse,
  DeletionSession
} from "../services/aiDeletionProcessor";

type Section = 'financials' | 'inventory' | 'team' | 'project' | 'timeline' | 'catalog' | 'logistics' | null;

interface SelectedCatalogItem {
  item: {
    id: string;
    productName: string;
    productDescription: string;
    categoryLabel: 'Product' | 'Service';
    imageSrc?: string;
  };
  quantity: number;
}

export default function Home() {
  const [activeSection, setActiveSection] = useState<Section>(null);
  const [isModalExpanded, setIsModalExpanded] = useState(false);
  const [isDataCreationModalOpen, setIsDataCreationModalOpen] = useState(false);
  // Removed isTransitioning - no longer needed

  // Ref for timeline section to trigger refresh
  const timelineRef = useRef<TimelineSectionRef | null>(null);

  // AI Data Creation State
  const [currentSession, setCurrentSession] = useState<DataCreationSession | null>(null);
  const [formFields, setFormFields] = useState<FormField[]>([]);
  const [isProcessingCommand, setIsProcessingCommand] = useState(false);
  // Remove unused isRefining state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [aiMessage, setAiMessage] = useState<string>('');
  // Remove streaming state - no longer needed

  // AI Deletion State
  const [currentDeletionSession, setCurrentDeletionSession] = useState<DeletionSession | null>(null);
  const [isDeletionMode, setIsDeletionMode] = useState(false);

  // Project editing state
  const [isProjectEditMode, setIsProjectEditMode] = useState(false);
  const [currentEditSession, setCurrentEditSession] = useState<ProjectEditSession | null>(null);

  // Context selection state for project editing
  const [showContextSelection, setShowContextSelection] = useState(false);

  // Catalog selection state (removed - will be handled via edit operations)
  const [selectedCatalogItems, setSelectedCatalogItems] = useState<SelectedCatalogItem[]>([]);
  const [showCatalogSelection, setShowCatalogSelection] = useState(false);

  // Module selection state (no longer used but kept for potential future use)

  // Context hooks for data management
  const { user } = useUser();
  const {
    members,
    addTeamMember,
    removeTeamMember,
    findMemberByName,
    getMemberById
  } = useTeam();
  const {
    projects,
    addProject,
    removeProject,
    findProjectByName,
    getProjectById
  } = useProject();
  const {
    items: inventoryItems,
    addInventoryItem,
    removeInventoryItem,
    findItemByName,
    getItemById: getInventoryItemById
  } = useInventory();
  const {
    items: catalogItems,
    addCatalogItem,
    removeCatalogItem,
    findCatalogItemByName,
    getItemById: getCatalogItemById,
    loading: catalogLoading
  } = useCatalog();
  const {
    financialRecords,
    removeFinancialRecord,
    findFinancialRecordByDescription,
    getRecordById: getFinancialRecordById
  } = useFinance();





  // Initialize context functions for deletion service
  useEffect(() => {
    setContextFunctions({
      // Lookup functions
      findTeamMemberByName: findMemberByName,
      findProjectByName: findProjectByName,
      findInventoryItemByName: findItemByName,
      findFinancialRecordByDescription: findFinancialRecordByDescription,
      findCatalogItemByName: findCatalogItemByName,
      getTeamMemberById: getMemberById,
      getProjectById: getProjectById,
      getInventoryItemById: getInventoryItemById,
      getFinancialRecordById: getFinancialRecordById,
      getCatalogItemById: getCatalogItemById,
      // Get all functions
      getAllTeamMembers: () => members,
      getAllProjects: () => projects,
      getAllInventoryItems: () => inventoryItems,
      getAllFinancialRecords: () => financialRecords,
      getAllCatalogItems: () => catalogItems,
      // Deletion functions
      removeTeamMember: removeTeamMember,
      removeProject: removeProject,
      removeInventoryItem: removeInventoryItem,
      removeFinancialRecord: removeFinancialRecord,
      removeCatalogItem: removeCatalogItem,
    });
  }, [
    members, projects, inventoryItems, financialRecords, catalogItems,
    findMemberByName, findProjectByName, findItemByName, findFinancialRecordByDescription, findCatalogItemByName,
    getMemberById, getProjectById, getInventoryItemById, getFinancialRecordById, getCatalogItemById,
    removeTeamMember, removeProject, removeInventoryItem, removeFinancialRecord, removeCatalogItem
  ]);

  // Component-specific variables from global design tokens
  const modalHeaderTextColor = 'var(--color-text-primary)';

  // Removed handleSmoothExpansion - no longer needed

  // Helper function to get modal dimensions based on section and expansion state
  const getModalDimensions = (section: Section) => {
    if (isModalExpanded) {
      return { width: '100%', height: '100%' };
    }

    // Small modal dimensions for each section
    switch (section) {
      case 'inventory':
        return { width: '500px', height: '700px' };
      case 'catalog':
        return { width: '450px', height: '500px' };
      case 'financials':
        return { width: '400px', height: '600px' };
      case 'team':
        return { width: '420px', height: '600px' };
      case 'project':
        return { width: '480px', height: '700px' };
      case 'timeline':
        return { width: '450px', height: '650px' };
      case 'logistics':
        return { width: '700px', height: '700px' };
      default:
        return { width: '300px', height: '500px' };
    }
  };

  // Helper function to handle section change and reset modal expansion
  const handleSectionChange = (section: Section) => {
    setActiveSection(section);
    setIsModalExpanded(false); // Reset to compact view when switching sections
  };

  // AI Data Creation Functions
  const handleCommandSubmit = async (command: string) => {
    setIsProcessingCommand(true);

    try {
      // Check if we're in deletion confirmation mode
      if (isDeletionMode && currentDeletionSession) {
        const confirmationResult = await processConfirmationResponse(currentDeletionSession.id, command);

        if (confirmationResult.success) {
          setAiMessage(confirmationResult.message);

          if (confirmationResult.completed || confirmationResult.cancelled) {
            // Reset deletion mode
            setIsDeletionMode(false);
            setCurrentDeletionSession(null);
            setIsDataCreationModalOpen(false);
          }
        } else {
          setAiMessage(confirmationResult.message);
        }

        setIsProcessingCommand(false);
        return;
      }

      // If there's an active session, just close the modal and start a new command
      if (currentSession && isDataCreationModalOpen && !isDeletionMode) {
        // Close current session and start fresh
        setCurrentSession(null);
        setFormFields([]);
        setIsDataCreationModalOpen(false);
        setAiMessage('');
      }

      // Otherwise, treat this as a new creation or deletion command
      const result = await processCommand(command);

      if (result.success) {
        // Handle deletion commands
        if (result.isDeletion && result.deletionSession) {
          setCurrentDeletionSession(result.deletionSession);
          setIsDeletionMode(true);
          setIsDataCreationModalOpen(true);

          // Clear any existing session data
          setCurrentSession(null);
          setFormFields([]);
          setShowCatalogSelection(false);
          setSelectedCatalogItems([]);

          // Set AI response for deletion confirmation
          if (result.aiResponse) {
            setAiMessage(result.aiResponse.message);
          }
        }
        // Handle project editing commands
        else if (result.isProjectEdit && result.editSession) {
          setCurrentEditSession(result.editSession);
          setIsProjectEditMode(true);
          setIsDataCreationModalOpen(true);

          // Clear any existing session data
          setCurrentSession(null);
          setFormFields([]);
          setIsDeletionMode(false);
          setCurrentDeletionSession(null);
          setShowCatalogSelection(false);
          setSelectedCatalogItems([]);

          // Set context selection state based on edit session
          setShowContextSelection(result.editSession.showContextSelection || false);

          // Set AI response for project editing
          if (result.aiResponse) {
            setAiMessage(result.aiResponse.message);
          }
        }
        // Handle creation commands
        else if (result.session) {
          setCurrentSession(result.session);

          // Reset deletion mode and catalog selection state for new sessions
          setIsDeletionMode(false);
          setCurrentDeletionSession(null);
          setShowCatalogSelection(false);
          setSelectedCatalogItems([]);

          // Always use simple form generation for all commands
          // Project creation will only handle basic data, modules will be added via edit operations
          setFormFields(generateFormFields(result.session));

          setIsDataCreationModalOpen(true);

          // Set AI response if available
          if (result.aiResponse) {
            setAiMessage(result.aiResponse.message);
          }
        }
      } else {
        // Show error message (could be implemented as a toast notification)
        console.error('Command processing failed:', result.error);
        alert(result.error || 'Failed to process command');
      }
    } catch (error) {
      console.error('Error processing command:', error);
      alert('An error occurred while processing your command');
    } finally {
      setIsProcessingCommand(false);
    }
  };

  const handleFieldChange = async (fieldId: string, value: any) => {
    if (!currentSession) return;

    // For guided conversations, update template data
    if ((currentSession as any).businessTemplate) {
      // Update both regular session data and template data
      const updatedSession = sessionManager.updateSession(currentSession.id, { [fieldId]: value });
      if (updatedSession) {
        // Also update template data for guided conversations
        const templateSession = updatedSession as any;
        if (templateSession.templateData && templateSession.currentTemplateStep === 'basic_info') {
          templateSession.templateData.basicInfo[fieldId] = value;
        }

        setCurrentSession(templateSession);

        const { generateTemplateFormFields } = await import('../services/templateBasedGuidedConversation');
        setFormFields(generateTemplateFormFields(templateSession));
      }
    } else {
      // Regular conversation handling
      const updatedSession = sessionManager.updateSession(currentSession.id, { [fieldId]: value });
      if (updatedSession) {
        setCurrentSession(updatedSession);
        setFormFields(generateFormFields(updatedSession));
      }
    }
  };

  // Project creation now only handles basic data
  // Additional modules (team, tasks, catalog, logistics) will be added via edit operations

  // Removed convertFormDataToUserMessage - no longer needed

  // Module selection handlers (no longer used but kept for potential future use)

  // Simplified form submission - no guided conversation needed

  const handleSubmit = async () => {
    if (!currentSession) return;

    setIsSubmitting(true);

    try {
      // Simplified submission - no guided conversation logic needed

      // Validate the session data
      const validation = validateSession(currentSession);
      if (!validation.isValid) {
        alert('Please fix the validation errors before submitting');
        return;
      }

      // Use context methods directly to create entities via backend API
      let targetSection: Section = null;

      switch (currentSession.entityType) {
        case 'employee':
          await addTeamMember(currentSession.currentData);
          targetSection = 'team';
          break;
        case 'project':
          await addProject(currentSession.currentData);
          targetSection = 'project';
          break;
        case 'inventory':
          await addInventoryItem(currentSession.currentData);
          targetSection = 'inventory';
          break;
        case 'catalog':
          await addCatalogItem(currentSession.currentData);
          targetSection = 'catalog';
          break;
        case 'financial':
          // Note: Financial records would need a context hook when implemented
          console.log('Created financial entity:', currentSession.currentData);
          targetSection = 'financials';
          break;
        case 'task':
          // Create task using taskService
          await taskService.createTask({
            name: currentSession.currentData.name || 'New Task',
            startDate: currentSession.currentData.startDate,
            endDate: currentSession.currentData.endDate,
            assignedToId: currentSession.currentData.assignedToId,
            projectId: currentSession.currentData.projectId
          });
          targetSection = 'timeline';
          // Refresh timeline to show new task
          if (timelineRef.current?.refreshTasks) {
            timelineRef.current.refreshTasks();
          }
          break;
        default:
          console.log('Created entity:', currentSession.currentData);
      }

      // Clean up modal first
      sessionManager.completeSession(currentSession.id);
      setCurrentSession(null);
      setFormFields([]);
      setIsDataCreationModalOpen(false);

      // Open the appropriate section to show the new entity
      if (targetSection) {
        handleSectionChange(targetSection);
      }

    } catch (error) {
      console.error('Error creating entity:', error);
      alert('An error occurred while creating the item');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseModal = () => {
    if (currentSession) {
      sessionManager.deleteSession(currentSession.id);
    }
    if (currentEditSession) {
      // Clean up project editing session
      const { projectEditingService } = require('../services/projectEditingService');
      projectEditingService.deleteEditSession(currentEditSession.id);
    }
    setCurrentSession(null);
    setCurrentEditSession(null);
    setFormFields([]);
    setIsDataCreationModalOpen(false);
    setIsProjectEditMode(false);
    // Reset AI state
    setAiMessage('');
    // Reset catalog selection state
    setShowCatalogSelection(false);
    setSelectedCatalogItems([]);
    // Reset context selection state
    setShowContextSelection(false);
  };

  // Context selection handlers for project editing
  const handleContextSelectionChange = (itemId: string, isSelected: boolean, quantity?: number) => {
    if (!currentEditSession) return;

    const { projectEditingService } = require('../services/projectEditingService');
    const updatedSession = projectEditingService.updateSessionSelection(
      currentEditSession.id,
      itemId,
      isSelected,
      quantity
    );

    if (updatedSession) {
      setCurrentEditSession(updatedSession);
    }
  };

  const handleContextSelectionConfirm = () => {
    if (!currentEditSession) return;

    const { projectEditingService } = require('../services/projectEditingService');
    const updatedSession = projectEditingService.confirmSelection(currentEditSession.id);

    if (updatedSession) {
      setCurrentEditSession(updatedSession);
      setShowContextSelection(false);

      // Generate form fields based on selected items
      if (updatedSession.isReadyForForm) {
        // For now, we'll show a simple confirmation form
        // In the future, this could generate specific fields based on the edit type
        setFormFields([
          {
            id: 'confirmation',
            label: 'Confirm Addition',
            type: 'text',
            value: `Ready to add ${updatedSession.selectedItems?.length || 0} items to the project`,
            required: false
          }
        ]);
      }
    }
  };

  // Removed shouldShowCatalogSelection - no longer needed

  // Handle catalog selection changes
  const handleCatalogSelectionChange = (selectedItems: SelectedCatalogItem[]) => {
    setSelectedCatalogItems(selectedItems);
  };

  // Removed template conversation functions - no longer needed



  // Simplified message handling - no guided conversation needed
  return (
    <div className="min-h-screen bg-[var(--color-background-primary)] px-[var(--spacing-16)] pb-[var(--spacing-16)] flex flex-col">
      <div className="relative bg-[var(--color-background-primary)]">
        <Header
          logoText="LOGO"
          addButtonText="Añadir"
          onAddClick={() => console.log('Add clicked')}
          onProfileClick={() => console.log('Profile clicked')}
        />
        {/* Business Type Switcher positioned in the middle of the header */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <BusinessTypeSwitcher />
        </div>



      </div>

      <main
        className="bg-[var(--color-background-secondary)] flex-1 p-[var(--spacing-16)] rounded-[var(--radius-8)] flex flex-col"
        style={{ boxShadow: 'var(--shadow-inner)' }}
      >
        {/* Main content area - navigation map (always visible) */}
        <div className="text-[var(--color-text-primary)] flex-1 relative">
          {/* Navigation Squares - Always visible background map */}
          <div className="grid grid-cols-3 grid-rows-3 gap-[var(--spacing-16)] h-full">
            <button
              onClick={() => handleSectionChange('financials')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Financials
            </button>
            <button
              onClick={() => handleSectionChange('inventory')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Inventory
            </button>
            <button
              onClick={() => handleSectionChange('catalog')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Catalog
            </button>
            <button
              onClick={() => handleSectionChange('team')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Team
            </button>
            <button
              onClick={() => handleSectionChange('project')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Project
            </button>
            <button
              onClick={() => handleSectionChange('timeline')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Timeline
            </button>
            <button
              onClick={() => handleSectionChange('logistics')}
              className="bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-lg font-semibold text-[var(--color-text-primary)] hover-bg"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              Logistics
            </button>
            {/* Empty grid cells */}
            <div></div>
            <div></div>
          </div>

          {/* Modal/Container overlay - Top layer */}
          <FadeIn show={!!activeSection} duration="normal" easing="smooth" unmountOnExit>
            <ScaleIn
              show={!!activeSection}
              duration="normal"
              easing="smooth"
              initialScale={0.95}
              className="absolute bg-[var(--color-background-primary)] rounded-[var(--radius-8)] p-[var(--spacing-16)] flex flex-col z-10 transition-modal"
              style={{
                boxShadow: 'var(--shadow-outer1)',
                ...(isModalExpanded ? {
                  top: 0,
                  left: 0,
                  right: 'calc(40px + var(--spacing-8))', // 40px (toggle button width) + 8px spacing
                  bottom: 'calc(var(--spacing-16) + 60px)' // 16px from message bar + approximate message bar height
                } : {
                  ...getModalDimensions(activeSection),
                  top: 0, // Position at the top next to toggle button
                  right: 'calc(40px + var(--spacing-8))' // 40px (toggle button width) + 8px spacing
                })
              }}
            >
              {/* Header */}
              <div className="flex justify-between items-center mb-[var(--spacing-16)]">
                <h2
                  className="text-base font-semibold capitalize"
                  style={{ color: modalHeaderTextColor }}
                >
                  {activeSection}
                </h2>
                <button
                  onClick={() => setIsModalExpanded(!isModalExpanded)}
                  className="text-[var(--color-text-primary)] hover-scale"
                  title={isModalExpanded ? 'Collapse' : 'Expand'}
                >
                  {isModalExpanded ? (
                    <CollapseIcon size={16} />
                  ) : (
                    <ExpandIcon size={16} />
                  )}
                </button>
              </div>

              {/* Section content */}
              <div className="flex-1 text-[var(--color-text-primary)] overflow-hidden">
                {activeSection === 'financials' && (
                  <FinanceSection
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                  />
                )}
                {activeSection === 'inventory' && (
                  <InventorySection
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                )}
                {activeSection === 'team' && (
                  <TeamSection
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                )}
                {activeSection === 'project' && (
                  <ProjectSection
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                )}
                {activeSection === 'timeline' && (
                  <TimelineSection
                    ref={timelineRef}
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                )}
                {activeSection === 'catalog' && (
                  <CatalogSection
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                )}
                {activeSection === 'logistics' && (
                  <LogisticsSection
                    isModalExpanded={isModalExpanded}
                    setIsModalExpanded={setIsModalExpanded}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                )}
              </div>
            </ScaleIn>
          </FadeIn>



          {/* Data Creation Modal overlay - Left side */}
          {(currentSession || currentEditSession || currentDeletionSession) && (
            <DataCreationModal
              isOpen={isDataCreationModalOpen}
              onClose={handleCloseModal}
              entityType={
                isProjectEditMode && currentEditSession
                  ? `Edit Project: ${currentEditSession.projectName}`
                  : isDeletionMode && currentDeletionSession
                  ? "Delete Confirmation"
                  : currentSession
                  ? getEntityDisplayName(currentSession.entityType)
                  : "AI Assistant"
              }
              fields={formFields}
              loading={isSubmitting}
              submitDisabled={isSubmitting}
              onFieldChange={handleFieldChange}
              onSubmit={handleSubmit}
              originalCommand={
                isProjectEditMode && currentEditSession
                  ? currentEditSession.intent.originalCommand
                  : isDeletionMode && currentDeletionSession
                  ? currentDeletionSession.intent.originalCommand
                  : currentSession?.intent.originalCommand || ''
              }
              aiMessage={aiMessage}
              aiNeedsMoreInfo={
                isProjectEditMode && currentEditSession
                  ? currentEditSession.needsMoreInfo
                  : isDeletionMode && currentDeletionSession
                  ? true // Always needs confirmation for deletion
                  : currentSession?.needsMoreInfo || false
              }
              showForm={
                isProjectEditMode && currentEditSession
                  ? currentEditSession.isReadyForForm
                  : isDeletionMode && currentDeletionSession
                  ? false // Don't show form for deletion, just chat
                  : currentSession?.isReadyForForm || false
              }
              conversationHistory={
                isProjectEditMode && currentEditSession
                  ? currentEditSession.conversationHistory
                  : isDeletionMode && currentDeletionSession
                  ? [] // No conversation history for deletion
                  : currentSession?.conversationHistory || []
              }
              onQuickFormSubmit={handleCommandSubmit}
              // Catalog selection props
              showCatalogSelection={showCatalogSelection}
              catalogItems={catalogItems}
              selectedCatalogItems={selectedCatalogItems}
              onCatalogSelectionChange={handleCatalogSelectionChange}
              catalogLoading={catalogLoading}
              // Context selection props for testing inside modal
              showContextSelection={isProjectEditMode && showContextSelection}
              contextData={currentEditSession?.contextData}
              onContextSelectionChange={handleContextSelectionChange}
              onContextSelectionConfirm={handleContextSelectionConfirm}
            />
          )}

          {/* Toggle Button overlay - Top layer */}
          <div className="absolute top-0 right-0 z-20">
            <button
              onClick={() => {
                if (activeSection) {
                  setActiveSection(null);
                  setIsModalExpanded(false);
                } else {
                  handleSectionChange('financials');
                }
              }}
              className="w-10 h-10 bg-[var(--color-background-primary)] rounded-[var(--radius-8)] flex items-center justify-center text-[var(--color-text-primary)] hover-scale"
              style={{ boxShadow: 'var(--shadow-outer1)' }}
            >
              {activeSection ? (
                <ChevronLeftIcon size={20} />
              ) : (
                <ChevronRightIcon size={20} />
              )}
            </button>
          </div>

          {/* Message Bar overlay - Bottom of content area */}
          <div
            className="absolute bottom-0 right-0 z-10"
            style={{
              left: isDataCreationModalOpen ? 'calc(400px + var(--spacing-16))' : '0' // Modal width + gap spacing
            }}
          >
            <MessageBar
              placeholder={
                currentSession && isDataCreationModalOpen
                  ? "Refine the data above..."
                  : "Ask me to create something..."
              }
              onSubmit={handleCommandSubmit}
              loading={isProcessingCommand}
              disabled={isSubmitting}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
