# Context Architecture

This directory contains the React Context providers and mock data for the application. The structure is designed to be easily replaceable with real backend integration later.

## Structure

```
contexts/
├── types.ts                 # TypeScript interfaces for all data types
├── mockData.ts             # Mock data that simulates API responses
├── CatalogContext.tsx      # Catalog items context and provider
├── InventoryContext.tsx    # Inventory items context and provider
├── UserContext.tsx         # User authentication and business type context
├── AppContextProvider.tsx  # Main provider that wraps all contexts
├── index.ts               # Clean exports for easy importing
└── README.md              # This documentation
```

## Usage

### 1. Setup (Already done in layout.tsx)

```tsx
import { AppContextProvider } from '../contexts';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AppContextProvider>
          {children}
        </AppContextProvider>
      </body>
    </html>
  );
}
```

### 2. Using Contexts in Components

```tsx
import { useCatalog, useInventory, useUser } from '../contexts';

function MyComponent() {
  const { items, selectedItem, selectItem, loading } = useCatalog();
  const { items: inventoryItems } = useInventory();
  const { user, setBusinessType } = useUser();

  // Use the data...
}
```

## Available Contexts

### CatalogContext
- **Data**: Product catalog items with details
- **Actions**: `selectItem`, `getItemById`, `refreshItems`
- **State**: `items`, `selectedItem`, `loading`, `error`

### InventoryContext
- **Data**: Inventory items with quantities
- **Actions**: `getItemById`, `refreshItems`, `updateQuantity`
- **State**: `items`, `loading`, `error`

### UserContext
- **Data**: User information and business type
- **Actions**: `setBusinessType`, `updateUser`, `signOut`
- **State**: `user`, `loading`, `error`

## Mock Data

All mock data is centralized in `mockData.ts` and includes:
- Catalog items with detailed product information
- Inventory items with quantities and categories
- Financial records (ready for future implementation)
- Team members (ready for future implementation)
- Projects (ready for future implementation)

## Backend Integration

To replace mock data with real backend calls:

1. **Update the fetch functions** in each context (e.g., `fetchCatalogItems`)
2. **Replace mock imports** with actual API calls
3. **Keep the same interface** - components won't need to change
4. **Add error handling** for network failures
5. **Add authentication** if needed

### Example Backend Integration

```tsx
// In CatalogContext.tsx
const fetchCatalogItems = async (): Promise<CatalogItem[]> => {
  const response = await fetch('/api/catalog');
  if (!response.ok) {
    throw new Error('Failed to fetch catalog items');
  }
  return response.json();
};
```

## Benefits

- **Separation of Concerns**: Data logic separated from UI components
- **Easy Testing**: Mock data can be easily swapped for tests
- **Type Safety**: Full TypeScript support with proper interfaces
- **Scalable**: Easy to add new contexts and data types
- **Backend Ready**: Designed for easy backend integration
- **Performance**: Built-in loading states and error handling
