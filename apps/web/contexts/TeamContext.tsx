"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TeamContextState, TeamMember } from './types';
import { backendDataStore } from '../services/backendDataStore';
import { useUser } from './UserContext';

interface TeamContextActions {
  getMemberById: (id: string) => TeamMember | undefined;
  refreshMembers: () => Promise<void>;
  updateMemberStatus: (id: string, status: 'active' | 'inactive') => void;
  selectMember: (member: TeamMember | null) => void;
  addTeamMember: (member: TeamMember) => Promise<void>;
  removeTeamMember: (id: string) => Promise<TeamMember | null>;
  findMemberByName: (name: string) => TeamMember | null;
}

type TeamContextType = TeamContextState & TeamContextActions & {
  selectedMember: TeamMember | null;
};

const TeamContext = createContext<TeamContextType | undefined>(undefined);

interface TeamProviderProps {
  children: ReactNode;
}

export const TeamProvider: React.FC<TeamProviderProps> = ({ children }) => {
  const { user, loading: userLoading } = useUser();
  const [state, setState] = useState<TeamContextState>({
    members: [],
    loading: true,
    error: null,
  });
  
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);

  // Fetch team members from backend
  const fetchTeamMembers = async (): Promise<TeamMember[]> => {
    return await backendDataStore.getTeamMembers();
  };

  const loadMembers = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const members = await fetchTeamMembers();
      setState(prev => ({
        ...prev,
        members,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load team members',
      }));
    }
  };

  const getMemberById = (id: string): TeamMember | undefined => {
    return state.members.find(member => member.id === id);
  };

  const refreshMembers = async (): Promise<void> => {
    await loadMembers();
  };

  const updateMemberStatus = (id: string, status: 'active' | 'inactive') => {
    setState(prev => ({
      ...prev,
      members: prev.members.map(member =>
        member.id === id ? { ...member, status } : member
      ),
    }));
  };

  const selectMember = (member: TeamMember | null) => {
    setSelectedMember(member);
  };

  const addTeamMember = async (member: TeamMember) => {
    try {
      const newMember = await backendDataStore.addTeamMember(member);
      setState(prev => ({
        ...prev,
        members: [...prev.members, newMember],
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to add team member',
      }));
    }
  };

  const removeTeamMember = async (id: string): Promise<TeamMember | null> => {
    const memberToRemove = state.members.find(member => member.id === id);
    if (!memberToRemove) return null;

    try {
      await backendDataStore.deleteTeamMember(id);
      setState(prev => ({
        ...prev,
        members: prev.members.filter(member => member.id !== id),
        selectedMember: selectedMember?.id === id ? null : selectedMember,
      }));
      if (selectedMember?.id === id) {
        setSelectedMember(null);
      }
      return memberToRemove;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to remove team member',
      }));
      return null;
    }
  };

  const findMemberByName = (name: string): TeamMember | null => {
    if (!name || !state.members.length) return null;

    const normalizedSearch = name.toLowerCase().trim();

    // First try exact match
    let found = state.members.find(member =>
      member.name.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = state.members.find(member =>
      member.name.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(member.name.toLowerCase())
    );

    return found || null;
  };

  // Load initial team data only after user is loaded
  useEffect(() => {
    if (!userLoading && user) {
      loadMembers();
    }
  }, [userLoading, user]);

  const contextValue: TeamContextType = {
    ...state,
    selectedMember,
    getMemberById,
    refreshMembers,
    updateMemberStatus,
    selectMember,
    addTeamMember,
    removeTeamMember,
    findMemberByName,
  };

  return (
    <TeamContext.Provider value={contextValue}>
      {children}
    </TeamContext.Provider>
  );
};

export const useTeam = (): TeamContextType => {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeam must be used within a TeamProvider');
  }
  return context;
};
