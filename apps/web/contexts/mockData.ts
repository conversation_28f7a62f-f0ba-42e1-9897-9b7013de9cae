import { 
  CatalogItem, 
  InventoryItem, 
  FinancialRecord, 
  TeamMember, 
  Project 
} from './types';

// Mock Catalog Data
export const mockCatalogItems: CatalogItem[] = [
  {
    id: 'lamina-acero',
    productName: 'Lámina Acero',
    productDescription: '4x8 | Calibre 12 | Inoxidable',
    categoryLabel: 'Product',
    details: {
      title: 'Lámina Acero Inoxidable',
      type: 'Product',
      fabrication: 'Laminado en frío',
      materials: [
        {
          id: 'acero-inox',
          name: 'Acero Inoxidable 304',
          material: 'Acero Inoxidable',
          quantity: 1,
          unit: 'plancha'
        },
        {
          id: 'acabado',
          name: 'Acabado Pulido',
          material: 'Tratamiento superficial',
          quantity: 1,
          unit: 'aplicación'
        }
      ],
      documents: [
        {
          id: 'doc-1',
          name: 'Especificaciones técnicas - Lámina Acero Inoxidable.pdf',
          url: '/documents/lamina-acero-specs.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 10 }, (_, i) => `/images/lamina-acero-${i + 1}.jpg`)
    }
  },
  {
    id: 'silla-467',
    productName: 'Silla',
    productDescription: 'Madera | Alta | 3×8×2',
    categoryLabel: 'Product',
    details: {
      title: 'Silla 467',
      type: 'Product',
      fabrication: 'Ensamblaje y acabado',
      materials: [
        {
          id: 'perfil-1x2',
          name: 'Perfil 1 x 2',
          material: 'Acero',
          quantity: 3,
          unit: 'metros'
        },
        {
          id: 'perfil-2x2',
          name: 'Perfil 2 x 2',
          material: 'Acero',
          quantity: 3,
          unit: 'metros'
        },
        {
          id: 'madera-roble',
          name: 'Madera de Roble',
          material: 'Madera',
          quantity: 0.5,
          unit: 'm²'
        }
      ],
      documents: [
        {
          id: 'doc-silla',
          name: 'Planos de fabricación - Silla 467.pdf',
          url: '/documents/silla-467-planos.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 8 }, (_, i) => `/images/silla-467-${i + 1}.jpg`)
    }
  },
  {
    id: 'tubo-pvc',
    productName: 'Tubo PVC',
    productDescription: '2" | 6m | Presión',
    categoryLabel: 'Product',
    details: {
      title: 'Tubo PVC Presión',
      type: 'Product',
      fabrication: 'Extrusión',
      materials: [
        {
          id: 'pvc-resina',
          name: 'Resina PVC',
          material: 'PVC',
          quantity: 2.5,
          unit: 'kg'
        }
      ],
      documents: [
        {
          id: 'doc-2',
          name: 'Especificaciones técnicas PVC.pdf',
          url: '/documents/pvc-specs.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 5 }, (_, i) => `/images/pvc-${i + 1}.jpg`)
    }
  },
  // Services
  {
    id: 'diseno-estructural',
    productName: 'Diseño Estructural',
    productDescription: 'Análisis | Cálculo | Planos',
    categoryLabel: 'Service',
    details: {
      title: 'Servicio de Diseño Estructural',
      type: 'Service',
      duration: '2-4 semanas',
      serviceType: 'structural_design',
      serviceSpecifications: {
        'Tipo_Proyecto': 'Edificio comercial',
        'Material_Estructura': 'Concreto armado',
        'Area_Construccion': 1200,
        'Numero_Niveles': 4,
        'Carga_Viva': 250,
        'Zona_Sismica': 'Zona B (intermedia)',
        'Tipo_Suelo': 'Suelo firme',
        'Normativa': 'ACI 318',
        'Incluye_Cimentacion': true,
        'Software_Analisis': 'ETABS'
      },
      requirements: [
        'Planos arquitectónicos',
        'Especificaciones de cargas',
        'Normativa aplicable',
        'Condiciones del terreno'
      ],
      documents: [
        {
          id: 'doc-servicio-1',
          name: 'Metodología de Diseño Estructural.pdf',
          url: '/documents/metodologia-diseno.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 6 }, (_, i) => `/images/diseno-estructural-${i + 1}.jpg`)
    }
  },
  {
    id: 'consultoria-fabricacion',
    productName: 'Consultoría',
    productDescription: 'Optimización | Procesos | Calidad',
    categoryLabel: 'Service',
    details: {
      title: 'Consultoría en Fabricación',
      type: 'Service',
      duration: '1-3 meses',
      serviceType: 'consulting',
      serviceSpecifications: {
        'Area_Consultoria': ['Optimización de procesos', 'Control de calidad', 'Lean Manufacturing'],
        'Tipo_Industria': 'Metalmecánica',
        'Tamaño_Empresa': 'Mediana (51-250 empleados)',
        'Duracion_Proyecto': '2-3 meses',
        'Modalidad': 'Híbrido',
        'Incluye_Capacitacion': true,
        'Entregables_Esperados': ['Diagnóstico inicial', 'Plan de mejora', 'Procedimientos documentados', 'Indicadores KPI']
      },
      requirements: [
        'Acceso a instalaciones',
        'Información de procesos',
        'Disponibilidad del equipo',
        'Datos históricos de producción'
      ],
      documents: [
        {
          id: 'doc-servicio-2',
          name: 'Metodología de Consultoría.pdf',
          url: '/documents/metodologia-consultoria.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 4 }, (_, i) => `/images/consultoria-${i + 1}.jpg`)
    }
  },
  // Fabrication Services using templates
  {
    id: 'corte-laser',
    productName: 'Corte Láser',
    productDescription: 'Precisión | Acero | Aluminio',
    categoryLabel: 'Service',
    details: {
      title: 'Servicio de Corte Láser',
      type: 'Service',
      duration: '1-3 días',
      serviceType: 'laser_cutting',
      serviceSpecifications: {
        'Material': 'Acero inoxidable',
        'Espesor': 3,
        'Dimensiones': '500x300mm',
        'Cantidad': 25,
        'Acabado': 'Desbarbado',
        'Tolerancia': '±0.2mm'
      },
      requirements: [
        'Archivo DXF o DWG',
        'Especificaciones de material',
        'Tolerancias dimensionales',
        'Cantidad requerida'
      ],
      documents: [
        {
          id: 'doc-laser',
          name: 'Especificaciones Corte Láser.pdf',
          url: '/documents/laser-cutting-specs.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 8 }, (_, i) => `/images/laser-cutting-${i + 1}.jpg`)
    }
  },
  {
    id: 'soldadura-mig',
    productName: 'Soldadura MIG',
    productDescription: 'Unión | Estructural | Certificada',
    categoryLabel: 'Service',
    details: {
      title: 'Servicio de Soldadura MIG',
      type: 'Service',
      duration: '3-7 días',
      serviceType: 'welding_service',
      serviceSpecifications: {
        'Proceso': 'MIG',
        'Material': 'Acero al carbón',
        'Espesor': 6,
        'Junta': 'A tope',
        'Longitud': 2.5,
        'Posición': 'Plana',
        'Certificación': true
      },
      requirements: [
        'Planos de soldadura',
        'Especificación de procedimiento',
        'Material certificado',
        'Acceso para soldadura'
      ],
      documents: [
        {
          id: 'doc-welding',
          name: 'Procedimiento de Soldadura MIG.pdf',
          url: '/documents/welding-procedure.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 6 }, (_, i) => `/images/welding-${i + 1}.jpg`)
    }
  },
  {
    id: 'pintura-electrostatica',
    productName: 'Pintura Electrostática',
    productDescription: 'Recubrimiento | Durabilidad | Color',
    categoryLabel: 'Service',
    details: {
      title: 'Servicio de Pintura Electrostática',
      type: 'Service',
      duration: '5-10 días',
      serviceType: 'powder_coating',
      serviceSpecifications: {
        'Material': 'Acero',
        'Color': 'Azul',
        'Acabado': 'Semibrillo',
        'Área': 15.5,
        'Espesor': 80,
        'Cantidad': 50
      },
      requirements: [
        'Piezas limpias y desengrasadas',
        'Especificación de color',
        'Área total a recubrir',
        'Condiciones de uso final'
      ],
      documents: [
        {
          id: 'doc-coating',
          name: 'Proceso Pintura Electrostática.pdf',
          url: '/documents/powder-coating-process.pdf',
          type: 'pdf'
        }
      ],
      images: Array.from({ length: 7 }, (_, i) => `/images/powder-coating-${i + 1}.jpg`)
    }
  }
];

// Mock Inventory Data - Fabrication Business
export const mockInventoryItems: InventoryItem[] = [
  // Raw Materials
  {
    id: 'inv-1',
    name: 'Lámina Acero A36',
    description: 'Lámina de acero al carbón A36 - 4x8 pies, calibre 12',
    quantity: 25,
    unit: 'piezas',
    category: 'Materia Prima',
    categoryId: 'raw_materials',
    minStock: 10,
    maxStock: 50,
    cost: 2500.00,
    location: 'Almacén A - Rack 1',
    supplier: 'Aceros del Norte',
    lastUpdated: '2024-01-15',
    additionalFields: {
      material_grade: 'A36',
      material_type: 'metal_sheet',
      dimensions: '4x8 pies x 3.05mm',
      lot_number: 'AN-2024-001',
      supplier: 'Aceros del Norte'
    }
  },
  {
    id: 'inv-2',
    name: 'Tubo Cuadrado 2x2',
    description: 'Tubo estructural cuadrado 2"x2" calibre 14',
    quantity: 150,
    unit: 'metros',
    category: 'Materia Prima',
    categoryId: 'raw_materials',
    minStock: 50,
    maxStock: 300,
    cost: 85.00,
    location: 'Almacén A - Rack 3',
    supplier: 'Tubacero SA',
    lastUpdated: '2024-01-14',
    additionalFields: {
      material_grade: 'A500',
      material_type: 'metal_tube',
      dimensions: '2"x2" x 1.9mm',
      lot_number: 'TB-2024-045',
      supplier: 'Tubacero SA'
    }
  },
  {
    id: 'inv-3',
    name: 'Barra Redonda Acero 1045',
    description: 'Barra redonda de acero al carbón 1045 - Diámetro 1"',
    quantity: 80,
    unit: 'metros',
    category: 'Materia Prima',
    categoryId: 'raw_materials',
    minStock: 30,
    maxStock: 150,
    cost: 120.00,
    location: 'Almacén A - Rack 2',
    supplier: 'Aceros Especiales',
    lastUpdated: '2024-01-16',
    additionalFields: {
      material_grade: '1045',
      material_type: 'metal_bar',
      dimensions: '1" diámetro',
      lot_number: 'AE-2024-012',
      supplier: 'Aceros Especiales'
    }
  },
  {
    id: 'inv-9',
    name: 'Lámina PVC Transparente',
    description: 'Lámina de PVC transparente 2mm - 1.22x2.44m',
    quantity: 20,
    unit: 'piezas',
    category: 'Materia Prima',
    categoryId: 'raw_materials',
    minStock: 5,
    maxStock: 40,
    cost: 450.00,
    location: 'Almacén B - Rack 1',
    supplier: 'Plásticos Industriales',
    lastUpdated: '2024-01-17',
    additionalFields: {
      material_grade: 'PVC',
      material_type: 'plastic_sheet',
      dimensions: '1.22x2.44m x 2mm',
      lot_number: 'PI-2024-008',
      supplier: 'Plásticos Industriales'
    }
  },

  // Finished Products
  {
    id: 'inv-4',
    name: 'Puerta Metálica Industrial',
    description: 'Puerta corredera industrial 4x3 metros con marco',
    quantity: 8,
    unit: 'unidades',
    category: 'Productos Terminados',
    categoryId: 'finished_products',
    cost: 12000.00,
    location: 'Almacén de Productos Terminados',
    lastUpdated: '2024-01-12',
    additionalFields: {
      product_type: 'custom_structure',
      quality_status: 'Aprobado',
      customer: 'Industrias del Bajío',
      delivery_date: '2024-01-25',
      warranty_period: '12 meses'
    }
  },
  {
    id: 'inv-10',
    name: 'Mesa de Trabajo Acero Inoxidable',
    description: 'Mesa de trabajo 2x1m con superficie de acero inoxidable',
    quantity: 5,
    unit: 'unidades',
    category: 'Productos Terminados',
    categoryId: 'finished_products',
    cost: 8500.00,
    location: 'Almacén de Productos Terminados',
    lastUpdated: '2024-01-18',
    additionalFields: {
      product_type: 'custom_furniture',
      quality_status: 'Aprobado',
      material_finish: 'Pulido espejo',
      customer: 'Restaurante El Buen Sabor'
    }
  },
  {
    id: 'inv-11',
    name: 'Controlador PLC Industrial',
    description: 'Controlador programable para automatización industrial',
    quantity: 12,
    unit: 'unidades',
    category: 'Productos Terminados',
    categoryId: 'finished_products',
    cost: 3500.00,
    location: 'Almacén de Productos Terminados',
    lastUpdated: '2024-01-19',
    additionalFields: {
      product_type: 'electronic_device',
      test_status: 'Aprobado',
      firmware_version: 'v2.1.3',
      warranty_period: '24 meses'
    }
  },
  // Consumables
  {
    id: 'inv-5',
    name: 'Electrodo 6013',
    description: 'Electrodo para soldadura 6013 - 3.2mm x 350mm',
    quantity: 25,
    unit: 'kg',
    category: 'Consumibles',
    categoryId: 'consumables',
    minStock: 15,
    maxStock: 100,
    cost: 180.00,
    location: 'Almacén de Consumibles',
    supplier: 'Soldaduras Técnicas',
    lastUpdated: '2024-01-16',
    additionalFields: {
      consumable_type: 'welding_supplies',
      initial_quantity: 100,
      usage_rate: '25%',
      expiry_date: '2026-01-01',
      reorder_point: 20,
      safety_stock: 10
    }
  },
  {
    id: 'inv-6',
    name: 'Disco de Corte 9"',
    description: 'Disco abrasivo para corte de metal 9" x 1/8"',
    quantity: 45,
    unit: 'piezas',
    category: 'Consumibles',
    categoryId: 'consumables',
    minStock: 20,
    maxStock: 100,
    cost: 35.00,
    location: 'Almacén de Consumibles',
    supplier: 'Abrasivos Industriales',
    lastUpdated: '2024-01-15',
    additionalFields: {
      consumable_type: 'cutting_tools',
      initial_quantity: 60,
      usage_rate: '25%',
      estimated_life: '50 cortes promedio',
      reorder_point: 25,
      safety_stock: 15
    }
  },
  {
    id: 'inv-12',
    name: 'Lija de Agua Grano 320',
    description: 'Lija de agua para acabado fino grano 320 - 230x280mm',
    quantity: 180,
    unit: 'hojas',
    category: 'Consumibles',
    categoryId: 'consumables',
    minStock: 50,
    maxStock: 300,
    cost: 8.50,
    location: 'Almacén C - Estante 3',
    supplier: 'Abrasivos Especializados',
    lastUpdated: '2024-01-22',
    additionalFields: {
      consumable_type: 'abrasives_sandpaper',
      initial_quantity: 250,
      usage_rate: '28%',
      grit_size: '320'
    }
  },
  {
    id: 'inv-13',
    name: 'Aceite de Corte Soluble',
    description: 'Aceite de corte soluble en agua para maquinado',
    quantity: 15,
    unit: 'litros',
    category: 'Consumibles',
    categoryId: 'consumables',
    minStock: 5,
    maxStock: 25,
    cost: 120.00,
    location: 'Almacén C - Área Química',
    supplier: 'Químicos Industriales',
    lastUpdated: '2024-01-23',
    additionalFields: {
      consumable_type: 'chemicals_fluids',
      initial_quantity: 20,
      usage_rate: '25%',
      expiry_date: '2024-12-31',
      concentration: '5% en agua'
    }
  },
  {
    id: 'inv-14',
    name: 'Tornillos Hex M8x25 Grado 8.8',
    description: 'Tornillos hexagonales M8x25mm grado 8.8 zincados',
    quantity: 850,
    unit: 'piezas',
    category: 'Consumibles',
    categoryId: 'consumables',
    minStock: 200,
    maxStock: 1000,
    cost: 2.50,
    location: 'Almacén C - Cajones Ferretería',
    supplier: 'Ferretería Industrial',
    lastUpdated: '2024-01-24',
    additionalFields: {
      consumable_type: 'fasteners_hardware',
      initial_quantity: 1000,
      usage_rate: '15%',
      material_grade: '8.8',
      specifications: 'M8x25mm hexagonal'
    }
  },
  // Tools and Equipment
  {
    id: 'inv-7',
    name: 'Soldadora MIG 250A',
    description: 'Máquina de soldar MIG/MAG 250 amperios trifásica',
    quantity: 2,
    unit: 'unidades',
    category: 'Herramientas y Equipos',
    categoryId: 'tools_equipment',
    cost: 85000.00,
    location: 'Área de Soldadura - Estación 1',
    lastUpdated: '2024-01-10',
    additionalFields: {
      equipment_type: 'welding_equipment',
      condition: 'Excelente',
      maintenance_date: '2024-01-05',
      calibration_date: '2024-01-05',
      operating_hours: 1250,
      serial_number: 'MIG250-2023-001'
    }
  },
  {
    id: 'inv-15',
    name: 'Sierra Cinta Horizontal',
    description: 'Sierra cinta horizontal para corte de metales - capacidad 250mm',
    quantity: 1,
    unit: 'unidades',
    category: 'Herramientas y Equipos',
    categoryId: 'tools_equipment',
    cost: 45000.00,
    location: 'Área de Corte - Estación 2',
    lastUpdated: '2024-01-25',
    additionalFields: {
      equipment_type: 'cutting_machinery',
      condition: 'Bueno',
      maintenance_date: '2024-01-20',
      operating_hours: 850,
      serial_number: 'SC250-2022-003',
      cutting_capacity: '250mm diámetro'
    }
  },
  {
    id: 'inv-16',
    name: 'Calibrador Digital 150mm',
    description: 'Calibrador digital de precisión 0-150mm resolución 0.01mm',
    quantity: 5,
    unit: 'piezas',
    category: 'Herramientas y Equipos',
    categoryId: 'tools_equipment',
    cost: 850.00,
    location: 'Área de Medición',
    lastUpdated: '2024-01-26',
    additionalFields: {
      equipment_type: 'measuring_tools',
      condition: 'Excelente',
      calibration_date: '2024-01-15',
      serial_number: 'CAL150-2023-005',
      measurement_range: '0-150mm',
      accuracy: '±0.02mm'
    }
  },
  {
    id: 'inv-17',
    name: 'Amoladora Angular 4.5"',
    description: 'Amoladora angular 4.5" 850W con regulador de velocidad',
    quantity: 8,
    unit: 'piezas',
    category: 'Herramientas y Equipos',
    categoryId: 'tools_equipment',
    cost: 1200.00,
    location: 'Almacén de Herramientas',
    lastUpdated: '2024-01-27',
    additionalFields: {
      equipment_type: 'power_tools',
      condition: 'Bueno',
      maintenance_date: '2024-01-22',
      operating_hours: 320,
      serial_number: 'ANG45-2023-008',
      power_rating: '850W',
      voltage: '110V'
    }
  },
  {
    id: 'inv-8',
    name: 'Casco de Soldadura',
    description: 'Casco de soldadura con careta fotosensible',
    quantity: 12,
    unit: 'piezas',
    category: 'Herramientas y Equipos',
    categoryId: 'tools_equipment',
    minStock: 8,
    maxStock: 20,
    cost: 450.00,
    location: 'Almacén de EPP',
    supplier: 'Seguridad Industrial',
    lastUpdated: '2024-01-14',
    additionalFields: {
      equipment_type: 'safety_equipment',
      supplier: 'Seguridad Industrial',
      reorder_point: 10,
      storage_location: 'Estante EPP-A',
      usage_department: 'Soldadura',
      certification: 'ANSI Z87.1',
      protection_level: 'DIN 9-13',
      last_inspection: '2024-01-10'
    }
  }
];

// Mock Financial Data
export const mockFinancialRecords: FinancialRecord[] = [
  {
    id: 'fin-1',
    type: 'income',
    amount: 15000,
    description: 'Venta de sillas modelo 467',
    date: '2025-06-01',
    category: 'Ventas'
  },
  {
    id: 'fin-2',
    type: 'expense',
    amount: 3500,
    description: 'Compra de material acero',
    date: '2025-06-01',
    category: 'Materiales'
  },
  {
    id: 'fin-3',
    type: 'income',
    amount: 32385.69,
    description: 'Depósito cliente proyecto #182',
    date: '2025-06-01',
    category: 'Depósito'
  },
  {
    id: 'fin-4',
    type: 'expense',
    amount: 1532.21,
    description: 'Compra de metal para proyecto',
    date: '2025-06-01',
    category: 'Materiales'
  }
];

// Mock Team Data
export const mockTeamMembers: TeamMember[] = [
  {
    id: 'team-1',
    name: 'Franco Eduardo',
    role: 'Supervisor de Producción',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    status: 'active',
    salary: 45000,
    currency: 'USD'
  },
  {
    id: 'team-2',
    name: 'Ana García',
    role: 'Ingeniera de Diseño',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    status: 'active',
    salary: 52000,
    currency: 'USD'
  },
  {
    id: 'team-3',
    name: 'Mark Márquez',
    role: 'Diseñador',
    email: '<EMAIL>',
    status: 'active',
    salary: 38000,
    currency: 'USD'
  },
  {
    id: 'team-4',
    name: 'María González',
    role: 'Coordinadora de Calidad',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    status: 'active',
    salary: 41000,
    currency: 'USD'
  },
  {
    id: 'team-5',
    name: 'Roberto Silva',
    role: 'Técnico de Mantenimiento',
    email: '<EMAIL>',
    status: 'active',
    salary: 35000,
    currency: 'USD'
  },
  {
    id: 'team-6',
    name: 'Laura Fernández',
    role: 'Analista de Procesos',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
    status: 'active',
    salary: 43000,
    currency: 'USD'
  }
];

// Mock Project Data
export const mockProjects: Project[] = [
  {
    id: 'proj-1',
    name: 'Tommy Hilfiger Cuernavaca',
    description: 'Construcción de nueva tienda flagship en Cuernavaca',
    status: 'in-progress',
    startDate: '2024-08-08',
    endDate: '2024-08-31',
    teamMembers: ['team-1', 'team-2'],
    modules: {
      enabled: ['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'],
      configuration: {
        catalog: {
          enableProducts: true,
          enableServices: true,
          productCategories: ['Construcción', 'Materiales'],
          serviceTypes: ['Instalación', 'Diseño']
        },
        team: {
          trackHours: true,
          enableSkillsManagement: true,
          enableCostTracking: true
        },
        timeline: {
          enableGanttView: true,
          enableMilestones: true,
          trackDependencies: true
        },
        inventory: {
          trackMaterials: true,
          enableBOM: true,
          trackSuppliers: true
        },
        logistics: {
          trackShipments: true,
          enableSupplyChain: true,
          trackDeliveries: true
        }
      },
      aiRecommended: ['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'],
      userOverrides: []
    },
    tasks: [
      {
        id: 'task-1-1',
        name: 'Anticipo financiero',
        startDate: 2,
        endDate: 5,
        completed: true,
        assignedTo: ['team-1'],
        projectId: 'proj-1'
      },
      {
        id: 'task-1-2',
        name: 'Diseño de planos',
        startDate: 6,
        endDate: 10,
        completed: true,
        assignedTo: ['team-2'],
        projectId: 'proj-1'
      },
      {
        id: 'task-1-3',
        name: 'Compra de materiales',
        startDate: 12,
        endDate: 16,
        completed: false,
        assignedTo: ['team-1'],
        projectId: 'proj-1'
      },
      {
        id: 'task-1-4',
        name: 'Construcción estructura',
        startDate: 18,
        endDate: 25,
        completed: false,
        assignedTo: ['team-1', 'team-2'],
        projectId: 'proj-1'
      },
      {
        id: 'task-1-5',
        name: 'Instalaciones finales',
        startDate: 26,
        endDate: 30,
        completed: false,
        assignedTo: ['team-2'],
        projectId: 'proj-1'
      }
    ]
  },
  {
    id: 'proj-2',
    name: 'Centro Comercial Plaza Norte',
    description: 'Desarrollo de estructura metálica para centro comercial',
    status: 'planning',
    startDate: '2024-09-15',
    endDate: '2024-12-20',
    teamMembers: ['team-1', 'team-3'],
    modules: {
      enabled: ['finance', 'catalog', 'team', 'timeline'],
      configuration: {
        catalog: {
          enableProducts: true,
          enableServices: false,
          productCategories: ['Estructuras Metálicas', 'Materiales de Construcción'],
          serviceTypes: []
        },
        team: {
          trackHours: true,
          enableSkillsManagement: false,
          enableCostTracking: true
        },
        timeline: {
          enableGanttView: true,
          enableMilestones: true,
          trackDependencies: false
        }
      },
      aiRecommended: ['finance', 'catalog', 'team', 'timeline', 'inventory'],
      userOverrides: ['inventory'] // User decided not to include inventory module
    },
    tasks: [
      {
        id: 'task-2-1',
        name: 'Estudio de factibilidad',
        startDate: 1,
        endDate: 7,
        completed: false,
        assignedTo: ['team-3'],
        projectId: 'proj-2'
      },
      {
        id: 'task-2-2',
        name: 'Diseño estructural',
        startDate: 8,
        endDate: 15,
        completed: false,
        assignedTo: ['team-1'],
        projectId: 'proj-2'
      },
      {
        id: 'task-2-3',
        name: 'Permisos y licencias',
        startDate: 16,
        endDate: 22,
        completed: false,
        assignedTo: ['team-3'],
        projectId: 'proj-2'
      }
    ]
  },
  {
    id: 'proj-3',
    name: 'Renovación Oficinas Corporativas',
    description: 'Modernización de espacios de trabajo y áreas comunes',
    status: 'in-progress',
    startDate: '2024-07-01',
    endDate: '2024-10-15',
    teamMembers: ['team-2', 'team-3'],
    modules: {
      enabled: ['finance', 'catalog', 'team'],
      configuration: {
        catalog: {
          enableProducts: false,
          enableServices: true,
          productCategories: [],
          serviceTypes: ['Renovación', 'Diseño Interior', 'Instalaciones']
        },
        team: {
          trackHours: true,
          enableSkillsManagement: true,
          enableCostTracking: true
        }
      },
      aiRecommended: ['finance', 'catalog', 'team', 'timeline'],
      userOverrides: ['timeline'] // User decided timeline not needed for this simple project
    },
    tasks: [
      {
        id: 'task-3-1',
        name: 'Demolición selectiva',
        startDate: 3,
        endDate: 8,
        completed: true,
        assignedTo: ['team-2'],
        projectId: 'proj-3'
      },
      {
        id: 'task-3-2',
        name: 'Instalación eléctrica',
        startDate: 10,
        endDate: 18,
        completed: true,
        assignedTo: ['team-3'],
        projectId: 'proj-3'
      },
      {
        id: 'task-3-3',
        name: 'Acabados interiores',
        startDate: 20,
        endDate: 28,
        completed: false,
        assignedTo: ['team-2', 'team-3'],
        projectId: 'proj-3'
      }
    ]
  },
  {
    id: 'proj-4',
    name: 'Ampliación Planta Industrial',
    description: 'Expansión de capacidad de producción en 40%',
    status: 'in-progress',
    startDate: '2024-06-10',
    endDate: '2024-08-25',
    teamMembers: ['team-1', 'team-2', 'team-3'],
    modules: {
      enabled: ['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'],
      configuration: {
        catalog: {
          enableProducts: true,
          enableServices: true,
          productCategories: ['Maquinaria', 'Equipos Industriales'],
          serviceTypes: ['Instalación', 'Mantenimiento', 'Capacitación']
        },
        team: {
          trackHours: true,
          enableSkillsManagement: true,
          enableCostTracking: true
        },
        timeline: {
          enableGanttView: true,
          enableMilestones: true,
          trackDependencies: true
        },
        inventory: {
          trackMaterials: true,
          enableBOM: true,
          trackSuppliers: true
        },
        logistics: {
          trackShipments: true,
          enableSupplyChain: true,
          trackDeliveries: true
        }
      },
      aiRecommended: ['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'],
      userOverrides: []
    }
  },
  {
    id: 'proj-5',
    name: 'Construcción Almacén Norte',
    description: 'Nuevo almacén de distribución en zona norte',
    status: 'completed',
    startDate: '2024-05-01',
    endDate: '2024-07-30',
    teamMembers: ['team-1', 'team-2'],
    modules: {
      enabled: ['finance', 'catalog', 'team', 'logistics'],
      configuration: {
        catalog: {
          enableProducts: true,
          enableServices: false,
          productCategories: ['Construcción', 'Materiales'],
          serviceTypes: []
        },
        team: {
          trackHours: true,
          enableSkillsManagement: false,
          enableCostTracking: true
        },
        logistics: {
          trackShipments: false,
          enableSupplyChain: false,
          trackDeliveries: true
        }
      },
      aiRecommended: ['finance', 'catalog', 'team', 'timeline', 'logistics'],
      userOverrides: ['timeline'] // User decided timeline not needed for completed project
    }
  },
  {
    id: 'proj-6',
    name: 'Sistema Automatización',
    description: 'Implementación de sistema de control automatizado',
    status: 'on-hold',
    startDate: '2024-03-01',
    endDate: '2024-09-30',
    teamMembers: ['team-3'],
    modules: {
      enabled: ['finance', 'catalog'],
      configuration: {
        catalog: {
          enableProducts: false,
          enableServices: true,
          productCategories: [],
          serviceTypes: ['Automatización', 'Software', 'Consultoría']
        }
      },
      aiRecommended: ['finance', 'catalog', 'team'],
      userOverrides: ['team'] // User decided team module not needed for this service-only project
    }
  }
];

// Mock Raw Material Batches Data
export const mockRawMaterialBatches: Record<string, any[]> = {
  'inv-1': [
    {
      id: 'batch-1',
      batch: 'AN-2024-001',
      dimensions: '4x8 pies x 3.05mm',
      quantity: 15,
      unit: 'piezas',
      unit_cost: 2500.00,
      total_cost: 37500.00,
      purchase_date: '2024-01-10',
      supplier: 'Aceros del Norte'
    },
    {
      id: 'batch-2',
      batch: 'AN-2024-002',
      dimensions: '4x8 pies x 3.05mm',
      quantity: 10,
      unit: 'piezas',
      unit_cost: 2500.00,
      total_cost: 25000.00,
      purchase_date: '2024-01-15',
      supplier: 'Aceros del Norte'
    }
  ],
  'inv-2': [
    {
      id: 'batch-3',
      batch: 'TB-2024-045',
      dimensions: '2"x2" x 1.9mm',
      quantity: 100,
      unit: 'metros',
      unit_cost: 85.00,
      total_cost: 8500.00,
      purchase_date: '2024-01-12',
      supplier: 'Tubacero SA'
    },
    {
      id: 'batch-4',
      batch: 'TB-2024-046',
      dimensions: '2"x2" x 1.9mm',
      quantity: 50,
      unit: 'metros',
      unit_cost: 85.00,
      total_cost: 4250.00,
      purchase_date: '2024-01-14',
      supplier: 'Tubacero SA'
    }
  ],
  'inv-3': [
    {
      id: 'batch-5',
      batch: 'AE-2024-012',
      dimensions: '1" diámetro',
      quantity: 80,
      length: 80,
      unit: 'metros',
      unit_cost: 120.00,
      total_cost: 9600.00,
      purchase_date: '2024-01-16',
      supplier: 'Aceros Especiales'
    }
  ],
  'inv-9': [
    {
      id: 'batch-6',
      batch: 'PI-2024-008',
      dimensions: '1.22x2.44m x 2mm',
      quantity: 20,
      unit: 'piezas',
      unit_cost: 450.00,
      total_cost: 9000.00,
      purchase_date: '2024-01-17',
      supplier: 'Plásticos Industriales'
    }
  ]
};

// Mock Finished Product Batches Data
export const mockFinishedProductBatches: Record<string, any[]> = {
  'inv-4': [
    {
      id: 'fp-batch-1',
      batch: 'PD-2024-001',
      dimensions: '4x3m x 2.5m alto',
      quantity: 5,
      unit: 'unidades',
      quality_status: 'Aprobado',
      unit_cost: 12000.00,
      sale_price: 18000.00,
      completion_date: '2024-01-12',
      customer: 'Industrias del Bajío'
    },
    {
      id: 'fp-batch-2',
      batch: 'PD-2024-002',
      dimensions: '4x3m x 2.5m alto',
      quantity: 3,
      unit: 'unidades',
      quality_status: 'En Revisión',
      unit_cost: 12000.00,
      sale_price: 18000.00,
      completion_date: '2024-01-15',
      customer: 'Constructora Norte'
    }
  ],
  'inv-10': [
    {
      id: 'fp-batch-3',
      batch: 'MT-2024-001',
      dimensions: '2x1m x 0.9m alto',
      material_finish: 'Pulido espejo',
      quantity: 5,
      unit: 'unidades',
      quality_status: 'Aprobado',
      unit_cost: 8500.00,
      sale_price: 12500.00,
      completion_date: '2024-01-18'
    }
  ],
  'inv-11': [
    {
      id: 'fp-batch-4',
      batch: 'PLC-2024-001',
      serial_number: 'PLC-001-024',
      model: 'IC-PLC-300',
      firmware_version: 'v2.1.3',
      quantity: 8,
      unit: 'unidades',
      test_status: 'Aprobado',
      unit_cost: 3500.00,
      sale_price: 5200.00,
      assembly_date: '2024-01-19',
      warranty_period: '24 meses'
    },
    {
      id: 'fp-batch-5',
      batch: 'PLC-2024-002',
      serial_number: 'PLC-002-024',
      model: 'IC-PLC-300',
      firmware_version: 'v2.1.3',
      quantity: 4,
      unit: 'unidades',
      test_status: 'En Pruebas',
      unit_cost: 3500.00,
      sale_price: 5200.00,
      assembly_date: '2024-01-20',
      warranty_period: '24 meses'
    }
  ]
};

// Mock Consumable Batches Data
export const mockConsumableBatches: Record<string, any[]> = {
  'inv-5': [
    {
      id: 'cons-batch-1',
      batch: 'E6013-2024-001',
      specifications: 'E6013 3.2mm x 350mm',
      initial_quantity: 50,
      current_quantity: 37.5,
      unit: 'kg',
      usage_percentage: 25,
      unit_cost: 180.00,
      purchase_date: '2024-01-16',
      expiry_date: '2026-01-01',
      supplier: 'Soldaduras Técnicas'
    },
    {
      id: 'cons-batch-2',
      batch: 'E6013-2024-002',
      specifications: 'E6013 3.2mm x 350mm',
      initial_quantity: 50,
      current_quantity: 37.5,
      unit: 'kg',
      usage_percentage: 25,
      unit_cost: 180.00,
      purchase_date: '2024-01-20',
      expiry_date: '2026-01-01',
      supplier: 'Soldaduras Técnicas'
    }
  ],
  'inv-6': [
    {
      id: 'cons-batch-3',
      batch: 'DC9-2024-001',
      tool_specifications: '9" x 1/8" corte metal',
      initial_quantity: 30,
      current_quantity: 22,
      unit: 'piezas',
      usage_percentage: 27,
      unit_cost: 35.00,
      purchase_date: '2024-01-15',
      estimated_life: '50 cortes promedio',
      supplier: 'Abrasivos Industriales'
    },
    {
      id: 'cons-batch-4',
      batch: 'DC9-2024-002',
      tool_specifications: '9" x 1/8" corte metal',
      initial_quantity: 30,
      current_quantity: 23,
      unit: 'piezas',
      usage_percentage: 23,
      unit_cost: 35.00,
      purchase_date: '2024-01-18',
      estimated_life: '50 cortes promedio',
      supplier: 'Abrasivos Industriales'
    }
  ],
  'inv-12': [
    {
      id: 'cons-batch-5',
      batch: 'LJ320-2024-001',
      grit_size: '320',
      dimensions: '230x280mm',
      initial_quantity: 150,
      current_quantity: 108,
      unit: 'hojas',
      usage_percentage: 28,
      unit_cost: 8.50,
      purchase_date: '2024-01-22',
      supplier: 'Abrasivos Especializados'
    },
    {
      id: 'cons-batch-6',
      batch: 'LJ320-2024-002',
      grit_size: '320',
      dimensions: '230x280mm',
      initial_quantity: 100,
      current_quantity: 72,
      unit: 'hojas',
      usage_percentage: 28,
      unit_cost: 8.50,
      purchase_date: '2024-01-25',
      supplier: 'Abrasivos Especializados'
    }
  ],
  'inv-13': [
    {
      id: 'cons-batch-7',
      batch: 'AC-SOL-2024-001',
      chemical_composition: 'Aceite mineral soluble',
      concentration: '5% en agua',
      initial_quantity: 20,
      current_quantity: 15,
      unit: 'litros',
      usage_percentage: 25,
      unit_cost: 120.00,
      purchase_date: '2024-01-23',
      expiry_date: '2024-12-31',
      safety_notes: 'Usar EPP, evitar contacto con piel',
      supplier: 'Químicos Industriales'
    }
  ],
  'inv-14': [
    {
      id: 'cons-batch-8',
      batch: 'THX-M8-2024-001',
      specifications: 'M8x25mm hexagonal',
      material_grade: '8.8',
      initial_quantity: 500,
      current_quantity: 425,
      unit: 'piezas',
      usage_percentage: 15,
      unit_cost: 2.50,
      purchase_date: '2024-01-24',
      supplier: 'Ferretería Industrial'
    },
    {
      id: 'cons-batch-9',
      batch: 'THX-M8-2024-002',
      specifications: 'M8x25mm hexagonal',
      material_grade: '8.8',
      initial_quantity: 500,
      current_quantity: 425,
      unit: 'piezas',
      usage_percentage: 15,
      unit_cost: 2.50,
      purchase_date: '2024-01-26',
      supplier: 'Ferretería Industrial'
    }
  ]
};

// Mock Equipment Records Data
export const mockEquipmentRecords: Record<string, any[]> = {
  'inv-7': [
    {
      id: 'eq-record-1',
      asset_tag: 'WLD-001',
      model: 'MIG-250A Pro',
      serial_number: 'MIG250-2023-001',
      amperage_rating: '250A @ 60% duty cycle',
      condition_status: 'Excelente',
      operating_hours: 1250,
      last_maintenance: '2024-01-05',
      next_maintenance: '2024-04-05',
      acquisition_cost: 85000.00,
      location: 'Área de Soldadura - Estación 1'
    }
  ],
  'inv-15': [
    {
      id: 'eq-record-2',
      asset_tag: 'CUT-002',
      model: 'SC-250H',
      serial_number: 'SC250-2022-003',
      cutting_capacity: '250mm diámetro',
      power_rating: '2.2kW',
      condition_status: 'Bueno',
      operating_hours: 850,
      last_maintenance: '2024-01-20',
      next_maintenance: '2024-04-20',
      acquisition_cost: 45000.00
    }
  ],
  'inv-16': [
    {
      id: 'eq-record-3',
      asset_tag: 'MED-003',
      model: 'Digital Caliper Pro',
      serial_number: 'CAL150-2023-005',
      measurement_range: '0-150mm',
      accuracy: '±0.02mm',
      calibration_status: 'Vigente',
      last_calibration: '2024-01-15',
      next_calibration: '2024-07-15',
      condition_status: 'Excelente',
      acquisition_cost: 850.00
    },
    {
      id: 'eq-record-4',
      asset_tag: 'MED-004',
      model: 'Digital Caliper Pro',
      serial_number: 'CAL150-2023-006',
      measurement_range: '0-150mm',
      accuracy: '±0.02mm',
      calibration_status: 'Vigente',
      last_calibration: '2024-01-15',
      next_calibration: '2024-07-15',
      condition_status: 'Bueno',
      acquisition_cost: 850.00
    }
  ],
  'inv-17': [
    {
      id: 'eq-record-5',
      asset_tag: 'PWR-005',
      model: 'AG-4.5-850',
      serial_number: 'ANG45-2023-008',
      power_rating: '850W',
      voltage: '110V',
      condition_status: 'Bueno',
      operating_hours: 320,
      last_maintenance: '2024-01-22',
      next_maintenance: '2024-04-22',
      assigned_to: 'Área de Acabados',
      acquisition_cost: 1200.00
    },
    {
      id: 'eq-record-6',
      asset_tag: 'PWR-006',
      model: 'AG-4.5-850',
      serial_number: 'ANG45-2023-009',
      power_rating: '850W',
      voltage: '110V',
      condition_status: 'Excelente',
      operating_hours: 180,
      last_maintenance: '2024-01-22',
      next_maintenance: '2024-04-22',
      assigned_to: 'Área de Soldadura',
      acquisition_cost: 1200.00
    }
  ],
  'inv-8': [
    {
      id: 'eq-record-7',
      asset_tag: 'SAF-007',
      model: 'Auto-Darkening Helmet Pro',
      certification: 'ANSI Z87.1',
      protection_level: 'DIN 9-13',
      condition_status: 'Bueno',
      last_inspection: '2024-01-10',
      next_inspection: '2024-04-10',
      expiry_date: '2026-01-01',
      assigned_to: 'Franco Eduardo',
      acquisition_cost: 450.00
    },
    {
      id: 'eq-record-8',
      asset_tag: 'SAF-008',
      model: 'Auto-Darkening Helmet Pro',
      certification: 'ANSI Z87.1',
      protection_level: 'DIN 9-13',
      condition_status: 'Excelente',
      last_inspection: '2024-01-10',
      next_inspection: '2024-04-10',
      expiry_date: '2026-01-01',
      assigned_to: 'Roberto Silva',
      acquisition_cost: 450.00
    }
  ]
};
