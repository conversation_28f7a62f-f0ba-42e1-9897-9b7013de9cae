"use client";

import React, { ReactNode } from 'react';
import { CatalogProvider } from './CatalogContext';
import { InventoryProvider } from './InventoryContext';
import { UserProvider } from './UserContext';
import { TeamProvider } from './TeamContext';
import { ProjectProvider } from './ProjectContext';
import { FinanceProvider } from './FinanceContext';

interface AppContextProviderProps {
  children: ReactNode;
}

/**
 * Main context provider that wraps all individual context providers
 * This makes it easy to add the contexts to the app and ensures proper nesting
 */
export const AppContextProvider: React.FC<AppContextProviderProps> = ({ children }) => {
  return (
    <UserProvider>
      <CatalogProvider>
        <InventoryProvider>
          <FinanceProvider>
            <TeamProvider>
              <ProjectProvider>
                {children}
              </ProjectProvider>
            </TeamProvider>
          </FinanceProvider>
        </InventoryProvider>
      </CatalogProvider>
    </UserProvider>
  );
};

// Export all context hooks for easy importing
export { useCatalog } from './CatalogContext';
export { useInventory } from './InventoryContext';
export { useUser } from './UserContext';
export { useTeam } from './TeamContext';
export { useProject } from './ProjectContext';
export { useFinance } from './FinanceContext';

// Export types for components that need them
export type {
  CatalogItem,
  CatalogItemDetails,
  InventoryItem,
  Material,
  Document,
} from './types';
