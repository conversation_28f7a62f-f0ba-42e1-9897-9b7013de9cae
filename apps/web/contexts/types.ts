// Types for the application data structures

// Business types that determine UI layout
export type BusinessType =
  | 'fabrication'
  | 'retail'
  | 'services'
  | 'manufacturing'
  | 'distribution'
  | 'construction';

// User and business context
export interface User {
  id: string;
  name: string;
  email: string;
  businessType: BusinessType;
  companyName: string;
}

export interface CatalogItem {
  id: string;
  productName: string;
  productDescription: string;
  categoryLabel: 'Product' | 'Service';
  imageSrc?: string;
  details?: CatalogItemDetails;
}

export interface CatalogItemDetails {
  title: string;
  type: 'Product' | 'Service';
  // Product-specific fields
  fabrication?: string;
  materials?: Material[];
  // Service-specific fields
  duration?: string;
  deliverables?: Deliverable[];
  requirements?: string[];
  serviceType?: string; // References service template (e.g., 'laser_cutting', 'welding_service')
  serviceSpecifications?: Record<string, any>; // Dynamic fields based on service template
  // Common fields
  documents: Document[];
  images: string[];
}

export interface Deliverable {
  id: string;
  name: string;
  description: string;
  timeline: string;
}

export interface Material {
  id: string;
  name: string;
  material: string;
  quantity: number;
  unit: string;
}

export interface Document {
  id: string;
  name: string;
  url: string;
  type: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  category: string;
  categoryId: string; // References inventory category from templates
  minStock?: number;
  maxStock?: number;
  cost: number;
  location?: string;
  supplier?: string;
  lastUpdated: string;
  // Dynamic fields based on category
  additionalFields?: Record<string, any>;
}

export interface FinancialRecord {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  date: string;
  category: string;
}

// Payment method types
export type PaymentMethod = 'cash' | 'credit_card' | 'loan' | 'bank_transfer' | 'check';

// Recurring payment details for loans and credit card payments
export interface RecurringPaymentDetails {
  monthlyAmount: number; // Monthly payment amount
  totalMonths: number; // Total number of months
  remainingMonths: number; // Remaining months to pay
  interestRate?: number; // Interest rate percentage
  startDate: string; // When payments started
  nextDueDate: string; // Next payment due date
}

// Extended interface for financial movements (used in finance section)
export interface FinancialMovement {
  id: string;
  fecha: string; // date
  concepto: string; // description/concept
  monto: number; // amount
  tipo: 'Entrada' | 'Salida'; // income/expense in Spanish
  asignacion?: string; // assignment (project, etc.)
  categoria: string; // category
  comportamiento?: string; // behavior (Fijo, Variable, etc.)
  comprobante?: string; // receipt/document
  paymentMethod?: PaymentMethod; // payment method used
  isRecurring?: boolean; // whether this is a recurring payment
  recurringDetails?: RecurringPaymentDetails; // details for recurring payments
  dueDate?: string; // due date for loans or credit card payments
}

// Forecast entry for future financial planning
export interface ForecastEntry {
  id: string;
  date: string; // YYYY-MM-DD format
  concepto: string; // description
  monto: number; // amount
  tipo: 'Entrada' | 'Salida'; // income/expense
  categoria: string; // category
  sourceType: 'recurring_payment' | 'scheduled_payment' | 'manual'; // source of forecast
  sourceId?: string; // ID of the original movement/record that generated this forecast
  isConfirmed: boolean; // whether this forecast has been confirmed/realized
  recurringPaymentInfo?: {
    monthNumber: number; // which month in the sequence (1, 2, 3...)
    totalMonths: number; // total months in the sequence
    remainingMonths: number; // remaining months after this one
  };
}

// Context state interfaces
export interface FinanceContextState {
  financialRecords: FinancialRecord[];
  movements: FinancialMovement[];
  forecasts: ForecastEntry[];
  loading: boolean;
  error: string | null;
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
  status: 'active' | 'inactive';
  salary: number;
  currency: string;
  hourlyRate?: number;
  skills?: string[];
}

export interface ProjectTask {
  id: string;
  name: string;
  startDate: number; // Day of month (1-31) - for timeline display
  endDate: number; // Day of month (1-31) - for timeline display
  completed: boolean;
  assignedTo: string[]; // Team member IDs
  projectId: string;
  description?: string; // Optional task description
  priority?: 'low' | 'medium' | 'high' | 'urgent'; // Task priority
  estimatedHours?: number; // Estimated hours to complete
  actualHours?: number; // Actual hours spent
  tags?: string[]; // Task tags for categorization
  dependencies?: string[]; // IDs of tasks this task depends on
  createdAt?: string; // Creation timestamp
  updatedAt?: string; // Last update timestamp
  dueDate?: string; // Actual due date (ISO string)
  startDateTime?: string; // Actual start date/time (ISO string)
}

// Project module types
export type ProjectModuleType =
  | 'finance'      // Always required - financial tracking and budgeting
  | 'catalog'      // Products and/or services catalog
  | 'team'         // Team management and resource allocation
  | 'timeline'     // Project timeline and task management
  | 'inventory'    // Materials and inventory management
  | 'logistics';   // Logistics and supply chain management

// Module configuration for specific modules
export interface ModuleConfiguration {
  // Catalog module specific settings
  catalog?: {
    enableProducts: boolean;
    enableServices: boolean;
    productCategories?: string[];
    serviceTypes?: string[];
  };
  // Team module specific settings
  team?: {
    trackHours: boolean;
    enableSkillsManagement: boolean;
    enableCostTracking: boolean;
  };
  // Timeline module specific settings
  timeline?: {
    enableGanttView: boolean;
    enableMilestones: boolean;
    trackDependencies: boolean;
  };
  // Inventory module specific settings
  inventory?: {
    trackMaterials: boolean;
    enableBOM: boolean; // Bill of Materials
    trackSuppliers: boolean;
  };
  // Logistics module specific settings
  logistics?: {
    trackShipments: boolean;
    enableSupplyChain: boolean;
    trackDeliveries: boolean;
  };
}

// Project module configuration
export interface ProjectModules {
  enabled: ProjectModuleType[];
  configuration: ModuleConfiguration;
  aiRecommended?: ProjectModuleType[]; // Modules recommended by AI
  userOverrides?: ProjectModuleType[]; // User manual overrides to AI recommendations
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'in-progress' | 'completed' | 'on-hold';
  startDate: string;
  endDate?: string;
  teamMembers: string[]; // Team member IDs
  tasks?: ProjectTask[]; // Project tasks
  modules: ProjectModules; // Modular configuration
}

// Module recommendation result from AI
export interface ModuleRecommendation {
  module: ProjectModuleType;
  confidence: number; // 0-1 confidence score
  reasoning: string; // Why this module was recommended
  configuration?: Partial<ModuleConfiguration>; // Suggested configuration
}

// AI module analysis result
export interface ModuleAnalysisResult {
  recommendations: ModuleRecommendation[];
  projectType: string; // Detected project type (e.g., "construction", "software", "service")
  complexity: 'simple' | 'medium' | 'complex';
  suggestedModules: ProjectModuleType[];
}

// Context state interfaces
export interface CatalogContextState {
  items: CatalogItem[];
  selectedItem: CatalogItem | null;
  loading: boolean;
  error: string | null;
}

export interface InventoryContextState {
  items: InventoryItem[];
  loading: boolean;
  error: string | null;
}

export interface FinancialContextState {
  records: FinancialRecord[];
  loading: boolean;
  error: string | null;
}

export interface TeamContextState {
  members: TeamMember[];
  loading: boolean;
  error: string | null;
}

export interface ProjectContextState {
  projects: Project[];
  loading: boolean;
  error: string | null;
}
