"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ProjectContextState, Project, TeamMember, ProjectTask, ProjectModuleType } from './types';
import { backendDataStore } from '../services/backendDataStore';
import { useUser } from './UserContext';

interface ProjectContextActions {
  getProjectById: (id: string) => Project | undefined;
  refreshProjects: () => Promise<void>;
  updateProjectStatus: (id: string, status: Project['status']) => void;
  selectProject: (project: Project | null) => void;
  toggleTaskCompletion: (projectId: string, taskId: string) => Promise<void>;
  addProject: (project: Partial<Project>) => Promise<void>;
  removeProject: (id: string) => Promise<Project | null>;
  findProjectByName: (name: string) => Project | null;
  // Project editing methods
  updateProject: (id: string, updates: Partial<Project>) => void;
  addTeamMemberToProject: (projectId: string, teamMemberId: string) => void;
  removeTeamMemberFromProject: (projectId: string, teamMemberId: string) => void;
  addTaskToProject: (projectId: string, task: Omit<ProjectTask, 'id' | 'projectId'>) => Promise<void>;
  updateProjectTask: (projectId: string, taskId: string, updates: Partial<ProjectTask>) => Promise<void>;
  removeTaskFromProject: (projectId: string, taskId: string) => Promise<void>;
  enableProjectModule: (projectId: string, module: ProjectModuleType) => void;
  disableProjectModule: (projectId: string, module: ProjectModuleType) => void;
  updateProjectModuleConfig: (projectId: string, moduleConfig: any) => void;
}

type ProjectContextType = ProjectContextState & ProjectContextActions & {
  selectedProject: Project | null;
};

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

interface ProjectProviderProps {
  children: ReactNode;
}

export const ProjectProvider: React.FC<ProjectProviderProps> = ({ children }) => {
  const { user, loading: userLoading } = useUser();
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate API call to load projects
  const loadProjects = async () => {
    try {
      setLoading(true);
      setError(null);

      const projects = await backendDataStore.getProjects();
      setProjects(projects);
    } catch (err) {
      setError('Failed to load projects');
      console.error('Error loading projects:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load projects only after user is loaded
  useEffect(() => {
    if (!userLoading && user) {
      loadProjects();
    }
  }, [userLoading, user]);

  // Context actions
  const getProjectById = (id: string): Project | undefined => {
    return projects.find(project => project.id === id);
  };

  const refreshProjects = async (): Promise<void> => {
    await loadProjects();
  };

  const updateProjectStatus = (id: string, status: Project['status']): void => {
    setProjects(prev => prev.map(project => 
      project.id === id ? { ...project, status } : project
    ));
  };

  const selectProject = (project: Project | null): void => {
    setSelectedProject(project);
  };

  const toggleTaskCompletion = async (projectId: string, taskId: string): Promise<void> => {
    try {
      // Find the current task to get its current completion status
      const project = projects.find(p => p.id === projectId);
      const task = project?.tasks?.find(t => t.id === taskId);

      if (!task) {
        console.error('Task not found');
        return;
      }

      // Update task completion status via API
      const updatedTask = await backendDataStore.updateProjectTask(projectId, taskId, {
        completed: !task.completed
      });

      // Update local state
      setProjects(prev => prev.map(project => {
        if (project.id === projectId && project.tasks) {
          const updatedTasks = project.tasks.map(t =>
            t.id === taskId ? updatedTask : t
          );
          const updatedProject = { ...project, tasks: updatedTasks };

          // Update selected project if it's the same one
          if (selectedProject?.id === projectId) {
            setSelectedProject(updatedProject);
          }

          return updatedProject;
        }
        return project;
      }));
    } catch (error) {
      console.error('Error toggling task completion:', error);
      setError('Failed to update task completion status');
    }
  };

  const addProject = async (project: Partial<Project>): Promise<void> => {
    try {
      const newProject = await backendDataStore.addProject(project);
      setProjects(prev => [...prev, newProject]);
    } catch (error) {
      console.error('Failed to add project:', error);
      // Could add error state handling here if needed
    }
  };

  const removeProject = async (id: string): Promise<Project | null> => {
    const projectToRemove = projects.find(project => project.id === id);
    if (!projectToRemove) return null;

    try {
      await backendDataStore.deleteProject(id);

      setProjects(prev => prev.filter(project => project.id !== id));

      // If the removed project is currently selected, clear the selection
      if (selectedProject?.id === id) {
        setSelectedProject(null);
      }

      return projectToRemove;
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to remove project');
      return null;
    }
  };

  const findProjectByName = (name: string): Project | null => {
    if (!name || !projects.length) return null;

    const normalizedSearch = name.toLowerCase().trim();

    // First try exact match
    let found = projects.find(project =>
      project.name.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = projects.find(project =>
      project.name.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(project.name.toLowerCase())
    );

    return found || null;
  };

  // Project editing methods
  const updateProject = (id: string, updates: Partial<Project>): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === id) {
        const updatedProject = { ...project, ...updates };

        // Update selected project if it's the same one
        if (selectedProject?.id === id) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const addTeamMemberToProject = (projectId: string, teamMemberId: string): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          teamMembers: [...project.teamMembers, teamMemberId]
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const removeTeamMemberFromProject = (projectId: string, teamMemberId: string): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          teamMembers: project.teamMembers.filter(id => id !== teamMemberId)
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const addTaskToProject = async (projectId: string, task: Omit<ProjectTask, 'id' | 'projectId'>): Promise<void> => {
    try {
      // Create task via API
      const newTask = await backendDataStore.addProjectTask(projectId, task);

      // Update local state
      setProjects(prev => prev.map(project => {
        if (project.id === projectId) {
          const updatedProject = {
            ...project,
            tasks: [...(project.tasks || []), newTask]
          };

          // Update selected project if it's the same one
          if (selectedProject?.id === projectId) {
            setSelectedProject(updatedProject);
          }

          return updatedProject;
        }
        return project;
      }));
    } catch (error) {
      console.error('Error adding task to project:', error);
      setError('Failed to add task to project');
    }
  };

  const updateProjectTask = async (projectId: string, taskId: string, updates: Partial<ProjectTask>): Promise<void> => {
    try {
      // Update task via API
      const updatedTask = await backendDataStore.updateProjectTask(projectId, taskId, updates);

      // Update local state
      setProjects(prev => prev.map(project => {
        if (project.id === projectId && project.tasks) {
          const updatedTasks = project.tasks.map(task =>
            task.id === taskId ? updatedTask : task
          );
          const updatedProject = { ...project, tasks: updatedTasks };

          // Update selected project if it's the same one
          if (selectedProject?.id === projectId) {
            setSelectedProject(updatedProject);
          }

          return updatedProject;
        }
        return project;
      }));
    } catch (error) {
      console.error('Error updating project task:', error);
      setError('Failed to update project task');
    }
  };

  const removeTaskFromProject = async (projectId: string, taskId: string): Promise<void> => {
    try {
      // Delete task via API
      await backendDataStore.deleteProjectTask(projectId, taskId);

      // Update local state
      setProjects(prev => prev.map(project => {
        if (project.id === projectId && project.tasks) {
          const updatedProject = {
            ...project,
            tasks: project.tasks.filter(task => task.id !== taskId)
          };

          // Update selected project if it's the same one
          if (selectedProject?.id === projectId) {
            setSelectedProject(updatedProject);
          }

          return updatedProject;
        }
        return project;
      }));
    } catch (error) {
      console.error('Error removing task from project:', error);
      setError('Failed to remove task from project');
    }
  };

  const enableProjectModule = (projectId: string, module: ProjectModuleType): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          modules: {
            ...project.modules,
            enabled: project.modules.enabled.includes(module)
              ? project.modules.enabled
              : [...project.modules.enabled, module]
          }
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const disableProjectModule = (projectId: string, module: ProjectModuleType): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          modules: {
            ...project.modules,
            enabled: project.modules.enabled.filter(m => m !== module)
          }
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const updateProjectModuleConfig = (projectId: string, moduleConfig: any): void => {
    setProjects(prev => prev.map(project => {
      if (project.id === projectId) {
        const updatedProject = {
          ...project,
          modules: {
            ...project.modules,
            configuration: { ...project.modules.configuration, ...moduleConfig }
          }
        };

        // Update selected project if it's the same one
        if (selectedProject?.id === projectId) {
          setSelectedProject(updatedProject);
        }

        return updatedProject;
      }
      return project;
    }));
  };

  const contextValue: ProjectContextType = {
    projects,
    selectedProject,
    loading,
    error,
    getProjectById,
    refreshProjects,
    updateProjectStatus,
    selectProject,
    toggleTaskCompletion,
    addProject,
    removeProject,
    findProjectByName,
    updateProject,
    addTeamMemberToProject,
    removeTeamMemberFromProject,
    addTaskToProject,
    updateProjectTask,
    removeTaskFromProject,
    enableProjectModule,
    disableProjectModule,
    updateProjectModuleConfig,
  };

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
};

export const useProject = (): ProjectContextType => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};
