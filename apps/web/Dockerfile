# Frontend Dockerfile
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY apps/web/package*.json ./apps/web/
COPY packages/ui/package*.json ./packages/ui/

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build UI package
WORKDIR /app/packages/ui
RUN npm run build

# Switch to web app
WORKDIR /app/apps/web

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "run", "dev"]
