"use client";

import { CalendarTimeline, type CalendarTimelineTask } from "@admin/ui";
import { useTeam } from "../../contexts";

interface TimelineSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}

export function TimelineSection({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }: TimelineSectionProps) {
  const { members: teamMembers } = useTeam();

  // Get current month info
  const currentDate = new Date();
  const currentMonth = currentDate.toLocaleDateString('es-ES', { month: 'long' });
  const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();

  // Sample Calendar Timeline tasks
  const calendarTasks: CalendarTimelineTask[] = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON> financiero',
      startDate: 2,
      endDate: 5,
      employees: [
        {
          id: 'emp1',
          name: '<PERSON> <PERSON>',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '2',
      name: 'Hacer planos',
      startDate: 6,
      endDate: 10,
      employees: [
        {
          id: 'emp2',
          name: 'Carlos Mendoza',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '3',
      name: 'Compra de materiales',
      startDate: 12,
      endDate: 16,
      employees: [
        {
          id: 'emp3',
          name: 'María López',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '4',
      name: 'Fabricación',
      startDate: 18,
      endDate: 25,
      employees: [
        {
          id: 'emp4',
          name: 'Roberto Silva',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        },
        {
          id: 'emp5',
          name: 'Laura Martínez',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '5',
      name: 'Control calidad',
      startDate: 26,
      endDate: 30,
      employees: [
        {
          id: 'emp5',
          name: 'Laura Martínez',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
        }
      ]
    }
  ];
  return (
    <div className="h-full overflow-hidden">
      {/* Calendar Timeline View - Fresh Integration */}
      <div className="h-full overflow-y-auto p-4">
        <CalendarTimeline
          month={currentMonth}
          daysInMonth={daysInMonth}
          tasks={calendarTasks}
          onTaskClick={(task) => {
            console.log('Calendar task clicked:', task);
          }}
        />
      </div>
    </div>
  );
}
