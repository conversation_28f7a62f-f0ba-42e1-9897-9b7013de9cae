"use client";

import { CalendarTimeline, type CalendarTimelineTask } from "@admin/ui";
import { useTeam } from "../../contexts";
import { useEffect, useState, useImperativeHandle, forwardRef } from "react";
import { taskService } from "../../services/taskService";
import { useAuth } from "../../contexts/AuthContext";
import { ProjectTask } from "../../contexts/types";

interface TimelineSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}

export interface TimelineSectionRef {
  refreshTasks: () => void;
}

export const TimelineSection = forwardRef<TimelineSectionRef, TimelineSectionProps>(
  ({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }, ref) => {
    const { members: teamMembers } = useTeam();
    const { user } = useAuth();
    const [tasks, setTasks] = useState<ProjectTask[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // Get current month info
    const currentDate = new Date();
    const currentMonth = currentDate.toLocaleDateString('es-ES', { month: 'long' });
    const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();

    // Fetch tasks from the database
    const fetchTasks = async () => {
      if (!user?.id) return;

      try {
        setIsLoading(true);
        const fetchedTasks = await taskService.getTasks(user.id);
        setTasks(fetchedTasks);
      } catch (error) {
        console.error('Error fetching tasks for timeline:', error);
      } finally {
        setIsLoading(false);
      }
    };

    useEffect(() => {
      fetchTasks();
    }, [user?.id]);

    // Expose refresh method to parent component
    useImperativeHandle(ref, () => ({
      refreshTasks: fetchTasks
    }), [user?.id]);

  // Convert database tasks to calendar timeline format
  const calendarTasks: CalendarTimelineTask[] = tasks.map(task => {
    // Convert ISO date strings to day of month numbers
    const startDay = task.startDate ? new Date(task.startDate).getDate() : 1;
    const endDay = task.endDate ? new Date(task.endDate).getDate() : startDay;

    return {
      id: task.id,
      name: task.name,
      startDate: startDay,
      endDate: endDay,
      completed: task.completed,
      employees: task.assignedTo ? [{
        id: task.assignedTo.id,
        name: task.assignedTo.name,
        avatar: task.assignedTo.avatar || undefined
      }] : []
    };
  });

  // Fallback sample tasks if no real tasks exist
  const sampleTasks: CalendarTimelineTask[] = [
    {
      id: '1',
      name: 'Anticipo financiero',
      startDate: 2,
      endDate: 5,
      employees: [
        {
          id: 'emp1',
          name: 'Ana García',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '2',
      name: 'Hacer planos',
      startDate: 6,
      endDate: 10,
      employees: [
        {
          id: 'emp2',
          name: 'Carlos Mendoza',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '3',
      name: 'Compra de materiales',
      startDate: 12,
      endDate: 16,
      employees: [
        {
          id: 'emp3',
          name: 'María López',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '4',
      name: 'Fabricación',
      startDate: 18,
      endDate: 25,
      employees: [
        {
          id: 'emp4',
          name: 'Roberto Silva',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        },
        {
          id: 'emp5',
          name: 'Laura Martínez',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
        }
      ]
    },
    {
      id: '5',
      name: 'Control calidad',
      startDate: 26,
      endDate: 30,
      employees: [
        {
          id: 'emp5',
          name: 'Laura Martínez',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
        }
      ]
    }
  ];

  // Use real tasks if available, otherwise use sample tasks
  const displayTasks = calendarTasks.length > 0 ? calendarTasks : sampleTasks;

  return (
    <div className="h-full overflow-hidden">
      {/* Calendar Timeline View - Fresh Integration */}
      <div className="h-full overflow-y-auto p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">Loading tasks...</div>
          </div>
        ) : (
          <CalendarTimeline
            month={currentMonth}
            daysInMonth={daysInMonth}
            tasks={displayTasks}
            onTaskClick={(task) => {
              console.log('Calendar task clicked:', task);
              // TODO: Add task interaction functionality
            }}
          />
        )}
      </div>
    </div>
  );
});
