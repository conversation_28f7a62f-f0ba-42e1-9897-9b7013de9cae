# Business-Type-Specific Catalog Detail Layouts

This directory contains the business-type-specific catalog detail layouts that provide tailored user experiences based on the user's business type.

## Overview

When users create an account, they specify their business type, which determines the UI layout they see for catalog item details. This approach ensures that each business type gets the most relevant information and functionality for their specific needs.

## Architecture

```
catalog-details/
├── CatalogDetailFactory.tsx     # Factory component that routes to appropriate layout
├── FabricationCatalogDetail.tsx # Layout for fabrication businesses
├── RetailCatalogDetail.tsx      # Layout for retail businesses
├── index.ts                     # Clean exports
└── README.md                    # This documentation
```

## Business Types Supported

### 1. **Fabrication** (Implemented)
- **Focus**: Manufacturing processes, materials, technical specifications
- **Key Features**:
  - Materials required with quantities and units
  - Manufacturing specifications (process, time, tools, finish)
  - Technical documents with file types
  - Process images with numbered placeholders
- **Use Case**: Metal fabrication, custom manufacturing, workshop operations

### 2. **Retail** (Implemented)
- **Focus**: Sales, pricing, inventory management
- **Key Features**:
  - Pricing information (sale price, cost, margin, max discount)
  - Inventory status (stock levels, restock dates)
  - Sales performance metrics (units sold, ratings, satisfaction)
  - Product gallery optimized for customer presentation
- **Use Case**: Stores, e-commerce, product resellers

### 3. **Services** (Placeholder)
- **Planned Features**: Service packages, time estimates, resource requirements
- **Use Case**: Consulting, maintenance, professional services

### 4. **Manufacturing** (Placeholder)
- **Planned Features**: Production capacity, quality metrics, supply chain
- **Use Case**: Large-scale production, industrial manufacturing

### 5. **Distribution** (Placeholder)
- **Planned Features**: Logistics, shipping, warehouse management
- **Use Case**: Wholesalers, distributors, logistics companies

### 6. **Construction** (Placeholder)
- **Planned Features**: Project timelines, safety requirements, compliance
- **Use Case**: Construction companies, contractors, builders

## Usage

### Basic Implementation

```tsx
import { CatalogDetailFactory } from '../components/catalog-details';
import { useUser, useCatalog } from '../contexts';

function CatalogModal() {
  const { user } = useUser();
  const { selectedItem } = useCatalog();

  return (
    <div>
      {selectedItem && user && (
        <CatalogDetailFactory
          businessType={user.businessType}
          item={selectedItem}
          modalHeaderTextColor="#333"
        />
      )}
    </div>
  );
}
```

### Direct Layout Usage

```tsx
import { FabricationCatalogDetail } from '../components/catalog-details';

function CustomCatalogView() {
  return (
    <FabricationCatalogDetail
      item={catalogItem}
      modalHeaderTextColor="#333"
    />
  );
}
```

## Adding New Business Types

### 1. Create the Layout Component

```tsx
// components/catalog-details/ServicesCatalogDetail.tsx
export const ServicesCatalogDetail: React.FC<ServicesCatalogDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  return (
    <div>
      {/* Services-specific layout */}
    </div>
  );
};
```

### 2. Update the Factory

```tsx
// In CatalogDetailFactory.tsx
case 'services':
  return (
    <ServicesCatalogDetail 
      item={item} 
      modalHeaderTextColor={modalHeaderTextColor} 
    />
  );
```

### 3. Export the Component

```tsx
// In index.ts
export { ServicesCatalogDetail } from './ServicesCatalogDetail';
```

## Design Principles

### 1. **Business-Specific Information**
Each layout shows information most relevant to that business type:
- **Fabrication**: Materials, processes, technical specs
- **Retail**: Pricing, inventory, sales metrics
- **Services**: Time estimates, resource requirements

### 2. **Consistent Interface**
All layouts implement the same props interface:
```tsx
interface CatalogDetailProps {
  item: CatalogItem;
  modalHeaderTextColor: string;
}
```

### 3. **Fallback Strategy**
Unknown business types fall back to the fabrication layout to ensure the app never breaks.

### 4. **Extensible Architecture**
New business types can be added without modifying existing code.

## Testing

Use the `BusinessTypeSwitcher` component (included in development) to test different layouts:

```tsx
import { BusinessTypeSwitcher } from '../components/BusinessTypeSwitcher';

// Add to your page for testing
<BusinessTypeSwitcher />
```

## Future Enhancements

1. **Dynamic Layout Configuration**: Allow users to customize their layout
2. **Industry-Specific Variants**: Sub-types within business categories
3. **Role-Based Views**: Different layouts for different user roles
4. **A/B Testing**: Test different layouts for optimization
5. **Analytics Integration**: Track which layouts perform best

## Benefits

- **Tailored User Experience**: Each business type gets relevant information
- **Improved Efficiency**: Users see only what they need
- **Scalable Architecture**: Easy to add new business types
- **Maintainable Code**: Separation of concerns by business type
- **Future-Proof**: Ready for business expansion and new features
