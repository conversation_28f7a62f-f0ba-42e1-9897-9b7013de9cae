"use client";

import React from 'react';
import { CatalogItem } from '../../contexts/types';

interface RetailCatalogDetailProps {
  item: CatalogItem;
  modalHeaderTextColor: string;
}

/**
 * Catalog detail layout specifically designed for retail businesses
 * Shows pricing, inventory, and sales-focused information
 */
export const RetailCatalogDetail: React.FC<RetailCatalogDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  if (!item.details) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary-text)]">
        No hay detalles disponibles para este producto
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4" style={{ color: modalHeaderTextColor }}>
        {item.details.title}
      </h3>
      
      {/* Product Category and Brand */}
      <div className="mb-6">
        <div className="text-sm text-[var(--color-text-secondary-text)] mb-1">
          Categoría: {item.categoryLabel}
        </div>
        <div className="text-sm text-[var(--color-text-secondary-text)]">
          Marca: Premium Brand
        </div>
      </div>

      {/* Pricing Information - Key for retail */}
      <div className="mb-6">
        <h4 className="font-semibold mb-3" style={{ color: modalHeaderTextColor }}>
          Información de Precios
        </h4>
        <div className="bg-green-50 p-4 rounded">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Precio de Venta:</span>
              <div className="text-lg font-bold text-green-600">$1,250.00</div>
            </div>
            <div>
              <span className="font-medium">Precio de Costo:</span>
              <div className="text-lg font-bold text-gray-600">$850.00</div>
            </div>
            <div>
              <span className="font-medium">Margen:</span>
              <div className="text-lg font-bold text-blue-600">32%</div>
            </div>
            <div>
              <span className="font-medium">Descuento Máx:</span>
              <div className="text-lg font-bold text-orange-600">15%</div>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory Status */}
      <div className="mb-6">
        <h4 className="font-semibold mb-3" style={{ color: modalHeaderTextColor }}>
          Estado de Inventario
        </h4>
        <div className="space-y-2">
          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
            <span className="text-sm">Stock Disponible:</span>
            <span className="font-bold text-green-600">25 unidades</span>
          </div>
          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
            <span className="text-sm">Stock Mínimo:</span>
            <span className="font-bold text-orange-600">5 unidades</span>
          </div>
          <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
            <span className="text-sm">Próximo Restock:</span>
            <span className="font-bold text-blue-600">15 Feb 2024</span>
          </div>
        </div>
      </div>

      {/* Sales Performance */}
      <div className="mb-6">
        <h4 className="font-semibold mb-3" style={{ color: modalHeaderTextColor }}>
          Rendimiento de Ventas
        </h4>
        <div className="grid grid-cols-3 gap-3 text-center">
          <div className="p-3 bg-blue-50 rounded">
            <div className="text-lg font-bold text-blue-600">12</div>
            <div className="text-xs text-gray-600">Vendidos este mes</div>
          </div>
          <div className="p-3 bg-green-50 rounded">
            <div className="text-lg font-bold text-green-600">4.8</div>
            <div className="text-xs text-gray-600">Rating promedio</div>
          </div>
          <div className="p-3 bg-purple-50 rounded">
            <div className="text-lg font-bold text-purple-600">89%</div>
            <div className="text-xs text-gray-600">Satisfacción</div>
          </div>
        </div>
      </div>

      {/* Product Images */}
      <div>
        <h4 className="font-semibold mb-3" style={{ color: modalHeaderTextColor }}>
          Galería del Producto
        </h4>
        <div className="grid grid-cols-4 gap-2">
          {item.details.images.slice(0, 8).map((_, i) => (
            <div key={i} className="w-20 h-20 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
              IMG {i + 1}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
