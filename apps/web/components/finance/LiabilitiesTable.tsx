"use client";

import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell
} from '@admin/ui';

interface Liability {
  id: string;
  name: string;
  category: 'current' | 'long-term';
  amount: number;
  dueDate: string;
  interestRate?: number;
  creditor: string;
  status: 'pending' | 'overdue' | 'paid';
}

export function LiabilitiesTable() {
  // Sample liabilities data
  const liabilities: Liability[] = [
    {
      id: 'liability-1',
      name: 'Cuentas por Pagar - Proveedores',
      category: 'current',
      amount: 75000,
      dueDate: '2024-07-15',
      creditor: 'Proveedores Varios',
      status: 'pending'
    },
    {
      id: 'liability-2',
      name: 'Nómina por Pagar',
      category: 'current',
      amount: 45000,
      dueDate: '2024-06-30',
      creditor: 'Empleados',
      status: 'pending'
    },
    {
      id: 'liability-3',
      name: 'Impuestos por Pagar',
      category: 'current',
      amount: 28000,
      dueDate: '2024-07-31',
      creditor: 'SAT',
      status: 'pending'
    },
    {
      id: 'liability-4',
      name: 'Préstamo Bancario Corto Plazo',
      category: 'current',
      amount: 120000,
      dueDate: '2024-12-31',
      interestRate: 8.5,
      creditor: 'Banco Nacional',
      status: 'pending'
    },
    {
      id: 'liability-5',
      name: 'Servicios Públicos',
      category: 'current',
      amount: 12000,
      dueDate: '2024-06-25',
      creditor: 'CFE/Telmex',
      status: 'overdue'
    },
    {
      id: 'liability-6',
      name: 'Hipoteca Inmueble',
      category: 'long-term',
      amount: 850000,
      dueDate: '2029-03-15',
      interestRate: 6.2,
      creditor: 'Banco Hipotecario',
      status: 'pending'
    },
    {
      id: 'liability-7',
      name: 'Préstamo Equipamiento',
      category: 'long-term',
      amount: 250000,
      dueDate: '2027-08-20',
      interestRate: 7.8,
      creditor: 'Financiera Industrial',
      status: 'pending'
    },
    {
      id: 'liability-8',
      name: 'Bonos Corporativos',
      category: 'long-term',
      amount: 500000,
      dueDate: '2030-12-31',
      interestRate: 5.5,
      creditor: 'Inversionistas',
      status: 'pending'
    }
  ];

  const getCategoryLabel = (category: Liability['category']) => {
    switch (category) {
      case 'current':
        return 'Pasivo Corriente';
      case 'long-term':
        return 'Pasivo a Largo Plazo';
      default:
        return category;
    }
  };

  const getStatusLabel = (status: Liability['status']) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'overdue':
        return 'Vencido';
      case 'paid':
        return 'Pagado';
      default:
        return status;
    }
  };

  const getStatusColor = (status: Liability['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatInterestRate = (rate?: number) => {
    return rate ? `${rate}%` : '-';
  };

  // Calculate totals by category
  const currentLiabilities = liabilities.filter(l => l.category === 'current');
  const longTermLiabilities = liabilities.filter(l => l.category === 'long-term');

  const currentLiabilitiesTotal = currentLiabilities.reduce((sum, liability) => sum + liability.amount, 0);
  const longTermLiabilitiesTotal = longTermLiabilities.reduce((sum, liability) => sum + liability.amount, 0);
  const totalLiabilities = currentLiabilitiesTotal + longTermLiabilitiesTotal;

  // Calculate overdue amount
  const overdueLiabilities = liabilities.filter(l => l.status === 'overdue');
  const overdueTotal = overdueLiabilities.reduce((sum, liability) => sum + liability.amount, 0);

  return (
    <div className="h-full">
      <div className="mb-6">
        <h3 className="modal-sub-header">Pasivos de la Empresa</h3>
        <p className="text-sm text-[var(--color-text-secondary)] leading-relaxed">
          Registro completo de todas las obligaciones financieras
        </p>
      </div>

      <div className="mb-6">
        <Table>
          <TableHeader>
            <TableHeaderRow>
              <TableHeaderCell variant="first">Pasivo</TableHeaderCell>
              <TableHeaderCell variant="middle">Categoría</TableHeaderCell>
              <TableHeaderCell variant="middle">Monto</TableHeaderCell>
              <TableHeaderCell variant="middle">Tasa Interés</TableHeaderCell>
              <TableHeaderCell variant="middle">Fecha Vencimiento</TableHeaderCell>
              <TableHeaderCell variant="middle">Acreedor</TableHeaderCell>
              <TableHeaderCell variant="last">Estado</TableHeaderCell>
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {liabilities.map((liability, index) => {
              const isFirst = index === 0;
              const isLast = index === liabilities.length - 1;
              const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';
              
              return (
                <TableBodyRow key={liability.id}>
                  <TableBodyCell variant="first" rowPosition={rowPosition}>
                    <div className="font-medium">{liability.name}</div>
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    <span className="text-xs px-2 py-1 rounded-full bg-[var(--color-background-secondary)] text-[var(--color-text-secondary)]">
                      {getCategoryLabel(liability.category)}
                    </span>
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    <span className="font-semibold">{formatCurrency(liability.amount)}</span>
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    {formatInterestRate(liability.interestRate)}
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    {formatDate(liability.dueDate)}
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    {liability.creditor}
                  </TableBodyCell>
                  <TableBodyCell variant="last" rowPosition={rowPosition}>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(liability.status)}`}>
                      {getStatusLabel(liability.status)}
                    </span>
                  </TableBodyCell>
                </TableBodyRow>
              );
            })}
          </TableBody>
        </Table>

        {/* Liabilities Summary */}
        <div className="mt-2 space-y-2">
          <div className="py-2 px-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-8)]">
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-[var(--color-text-secondary)]">Pasivos Corrientes:</span>
                <span className="font-medium">{formatCurrency(currentLiabilitiesTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[var(--color-text-secondary)]">Pasivos Largo Plazo:</span>
                <span className="font-medium">{formatCurrency(longTermLiabilitiesTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[var(--color-text-secondary)]">Vencidos:</span>
                <span className="font-medium text-red-600">{formatCurrency(overdueTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-bold text-[var(--color-text-primary)]">Total Pasivos:</span>
                <span className="font-bold text-lg">{formatCurrency(totalLiabilities)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
