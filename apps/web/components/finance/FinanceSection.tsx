"use client";

import { useState } from "react";
import { FinanceCard, FinanceTabButton, Calendar, FadeIn, cn } from "@admin/ui";
import { MovementsTable } from "./MovementsTable";
import { FinancialStatementsTable } from "./FinancialStatementsTable";
import { AssetsTable } from "./AssetsTable";
import { LiabilitiesTable } from "./LiabilitiesTable";
import { ForecastTable } from "./ForecastTable";

type FinanceTab = 'activos' | 'pasivos' | 'forecast' | 'movimientos' | 'estados-financieros';

interface FinanceSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
}

export function FinanceSection({ isModalExpanded, setIsModalExpanded }: FinanceSectionProps) {
  const [activeFinanceTab, setActiveFinanceTab] = useState<FinanceTab>('activos');

  // Helper function for smooth expansion
  const handleSmoothExpansion = (tab: FinanceTab) => {
    setActiveFinanceTab(tab);
    // Small delay for smooth transition
    setTimeout(() => {
      setIsModalExpanded(true);
    }, 150);
  };

  return (
    <div className="h-full">
      {!isModalExpanded ? (
        // Collapsed view - Finance cards grid
        <div className="grid grid-cols-2 gap-3 h-full">
          <FinanceCard
            title="Movimientos"
            onClick={() => handleSmoothExpansion('movimientos')}
          />
          <FinanceCard
            title="Activos"
            onClick={() => handleSmoothExpansion('activos')}
          />
          <FinanceCard
            title="Pasivos"
            onClick={() => handleSmoothExpansion('pasivos')}
          />
          <FinanceCard
            title="Pronósticos"
            onClick={() => handleSmoothExpansion('forecast')}
          />
          <div className="col-span-2">
            <FinanceCard
              title="Estados Financieros"
              onClick={() => handleSmoothExpansion('estados-financieros')}
            />
          </div>
        </div>
      ) : (
        // Expanded view - Finance tabs and content
        <div className="h-full flex flex-col">
          {/* Finance Tab Buttons */}
          <div className="flex gap-2 mb-6">
            <FinanceTabButton
              title="Activos"
              isActive={activeFinanceTab === 'activos'}
              onClick={() => setActiveFinanceTab('activos')}
            />
            <FinanceTabButton
              title="Pasivos"
              isActive={activeFinanceTab === 'pasivos'}
              onClick={() => setActiveFinanceTab('pasivos')}
            />
            <FinanceTabButton
              title="Forecast"
              isActive={activeFinanceTab === 'forecast'}
              onClick={() => setActiveFinanceTab('forecast')}
            />
            <FinanceTabButton
              title="Movimientos"
              isActive={activeFinanceTab === 'movimientos'}
              onClick={() => setActiveFinanceTab('movimientos')}
            />
            <FinanceTabButton
              title="Estados Financieros"
              isActive={activeFinanceTab === 'estados-financieros'}
              onClick={() => setActiveFinanceTab('estados-financieros')}
            />
          </div>

          {/* Tab Content Container */}
          <div className="flex-1 border border-[var(--color-stroke)] rounded-[var(--radius-8)] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
            <div className="flex-1 px-16 pt-20 pb-16 min-h-0 overflow-y-auto">
              <FadeIn key={activeFinanceTab} show={true} duration="fast" easing="smooth" className="h-full">
                {activeFinanceTab === 'forecast' ? (
                  // Forecast Table - Shows recurring payment forecasts
                  <ForecastTable />
                ) : activeFinanceTab === 'movimientos' ? (
                  // Movements Table
                  <MovementsTable />
                ) : activeFinanceTab === 'estados-financieros' ? (
                  // Financial Statements Table
                  <FinancialStatementsTable />
                ) : activeFinanceTab === 'activos' ? (
                  // Assets Table
                  <AssetsTable />
                ) : activeFinanceTab === 'pasivos' ? (
                  // Liabilities Table
                  <LiabilitiesTable />
                ) : (
                  // Placeholder for other tabs
                  <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                    <p className="text-sm">
                      Contenido de {activeFinanceTab} - por implementar
                    </p>
                  </div>
                )}
              </FadeIn>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
