"use client";

import { Table, TableHeader, TableHeaderRow, TableHeaderCell, TableBody, TableBodyRow, TableBodyCell } from "@admin/ui";
import { useFinance } from "../../contexts";
import { ForecastEntry } from "../../contexts/types";

// Format currency for display
function formatCurrency(amount: number, type: 'Entrada' | 'Salida'): string {
  const formatted = `$ ${amount.toLocaleString('es-ES', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  return type === 'Entrada' ? `+ ${formatted}` : formatted;
}

// Format date for display
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

// Format source type for display
function formatSourceType(sourceType: string): string {
  const sourceLabels: Record<string, string> = {
    'recurring_payment': 'Pago Recurrente',
    'scheduled_payment': 'Pago Programado',
    'manual': 'Manual'
  };
  return sourceLabels[sourceType] || sourceType;
}

// Format recurring payment info
function formatRecurringInfo(forecast: ForecastEntry): string {
  if (!forecast.recurringPaymentInfo) return '-';
  
  const { monthNumber, totalMonths, remainingMonths } = forecast.recurringPaymentInfo;
  return `${monthNumber}/${totalMonths} (${remainingMonths} restantes)`;
}

export function ForecastTable() {
  const { forecasts } = useFinance();

  // Sort forecasts by date (earliest first)
  const sortedForecasts = [...forecasts].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // Group forecasts by month for better organization
  const forecastsByMonth = sortedForecasts.reduce((acc, forecast) => {
    const monthKey = forecast.date.substring(0, 7); // YYYY-MM
    if (!acc[monthKey]) {
      acc[monthKey] = [];
    }
    acc[monthKey].push(forecast);
    return acc;
  }, {} as Record<string, ForecastEntry[]>);

  if (forecasts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-[var(--color-text-secondary)]">
        <div className="text-center">
          <p className="text-sm mb-2">No hay pronósticos disponibles</p>
          <p className="text-xs">Los pronósticos se generan automáticamente cuando agregas pagos recurrentes</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {Object.entries(forecastsByMonth).map(([monthKey, monthForecasts]) => {
        const [year, month] = monthKey.split('-');
        const monthName = new Date(parseInt(year || '0'), parseInt(month || '0') - 1).toLocaleDateString('es-ES', {
          year: 'numeric', 
          month: 'long' 
        });

        // Calculate totals for the month
        const totalIncome = monthForecasts
          .filter(f => f.tipo === 'Entrada')
          .reduce((sum, f) => sum + f.monto, 0);
        const totalExpenses = monthForecasts
          .filter(f => f.tipo === 'Salida')
          .reduce((sum, f) => sum + f.monto, 0);
        const netAmount = totalIncome - totalExpenses;

        return (
          <div key={monthKey} className="space-y-3">
            {/* Month Header with Totals */}
            <div className="flex justify-between items-center border-b border-[var(--color-stroke)] pb-2">
              <h3 className="text-sm font-medium text-[var(--color-text-primary)] capitalize">
                {monthName}
              </h3>
              <div className="flex gap-4 text-xs">
                <span className="text-green-600">
                  Ingresos: {formatCurrency(totalIncome, 'Entrada')}
                </span>
                <span className="text-red-600">
                  Gastos: {formatCurrency(totalExpenses, 'Salida')}
                </span>
                <span className={`font-medium ${netAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  Neto: {formatCurrency(Math.abs(netAmount), netAmount >= 0 ? 'Entrada' : 'Salida')}
                </span>
              </div>
            </div>

            {/* Forecast Table for the Month */}
            <Table>
              <TableHeader>
                <TableHeaderRow>
                  <TableHeaderCell>Fecha</TableHeaderCell>
                  <TableHeaderCell>Concepto</TableHeaderCell>
                  <TableHeaderCell>Monto</TableHeaderCell>
                  <TableHeaderCell>Tipo</TableHeaderCell>
                  <TableHeaderCell>Categoría</TableHeaderCell>
                  <TableHeaderCell>Origen</TableHeaderCell>
                  <TableHeaderCell>Progreso</TableHeaderCell>
                  <TableHeaderCell>Estado</TableHeaderCell>
                </TableHeaderRow>
              </TableHeader>
              <TableBody>
                {monthForecasts.map((forecast) => (
                  <TableBodyRow key={forecast.id}>
                    <TableBodyCell>{formatDate(forecast.date)}</TableBodyCell>
                    <TableBodyCell>{forecast.concepto}</TableBodyCell>
                    <TableBodyCell>
                      <span className={forecast.tipo === 'Entrada' ? 'text-green-600' : 'text-red-600'}>
                        {formatCurrency(forecast.monto, forecast.tipo)}
                      </span>
                    </TableBodyCell>
                    <TableBodyCell>
                      <span className={`px-2 py-1 rounded text-xs ${
                        forecast.tipo === 'Entrada' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {forecast.tipo}
                      </span>
                    </TableBodyCell>
                    <TableBodyCell>{forecast.categoria}</TableBodyCell>
                    <TableBodyCell>{formatSourceType(forecast.sourceType)}</TableBodyCell>
                    <TableBodyCell>{formatRecurringInfo(forecast)}</TableBodyCell>
                    <TableBodyCell>
                      <span className={`px-2 py-1 rounded text-xs ${
                        forecast.isConfirmed 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {forecast.isConfirmed ? 'Confirmado' : 'Pendiente'}
                      </span>
                    </TableBodyCell>
                  </TableBodyRow>
                ))}
              </TableBody>
            </Table>
          </div>
        );
      })}
    </div>
  );
}
