"use client";

import { Table, TableHeader, TableHeaderRow, TableHeaderCell, TableBody, TableBodyRow, TableBodyCell } from "@admin/ui";
import { useFinance } from "../../contexts";
import { FinancialMovement } from "../../contexts/types";

// Format currency for display
function formatCurrency(amount: number, type: 'Entrada' | 'Salida'): string {
  const formatted = `$ ${amount.toLocaleString('es-ES', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  return type === 'Entrada' ? `+ ${formatted}` : formatted;
}

// Format payment method for display
function formatPaymentMethod(method?: string): string {
  if (!method) return '-';

  const methodLabels: Record<string, string> = {
    'cash': 'Efectivo',
    'credit_card': 'Tarjeta de Crédito',
    'loan': 'Préstamo',
    'bank_transfer': 'Transferencia',
    'check': 'Cheque'
  };

  return methodLabels[method] || method;
}

// Format recurring payment info
function formatRecurringInfo(movement: FinancialMovement): string {
  if (!movement.isRecurring || !movement.recurringDetails) return '-';

  const { monthlyAmount, remainingMonths, totalMonths } = movement.recurringDetails;
  const monthlyFormatted = formatCurrency(monthlyAmount, 'Salida');
  return `${monthlyFormatted}/mes (${remainingMonths}/${totalMonths})`;
}

export function MovementsTable() {
  const { movements, loading } = useFinance();

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-[var(--color-text-secondary)]">Loading movements...</p>
      </div>
    );
  }

  return (
    <div className="h-full">
      <Table>
        <TableHeader>
          <TableHeaderRow>
            <TableHeaderCell variant="first">Fecha</TableHeaderCell>
            <TableHeaderCell variant="middle">Concepto</TableHeaderCell>
            <TableHeaderCell variant="middle">Monto</TableHeaderCell>
            <TableHeaderCell variant="middle">Tipo</TableHeaderCell>
            <TableHeaderCell variant="middle">Método Pago</TableHeaderCell>
            <TableHeaderCell variant="middle">Recurrente</TableHeaderCell>
            <TableHeaderCell variant="middle">Asignación</TableHeaderCell>
            <TableHeaderCell variant="middle">Categoría</TableHeaderCell>
            <TableHeaderCell variant="middle">Comportamiento</TableHeaderCell>
            <TableHeaderCell variant="last">Comprobante</TableHeaderCell>
          </TableHeaderRow>
        </TableHeader>
        <TableBody>
          {movements.map((movement, index) => {
            const isFirst = index === 0;
            const isLast = index === movements.length - 1;
            const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';

            return (
              <TableBodyRow key={movement.id}>
                <TableBodyCell variant="first" rowPosition={rowPosition}>{movement.fecha}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.concepto}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>
                  {formatCurrency(movement.monto, movement.tipo)}
                </TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.tipo}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{formatPaymentMethod(movement.paymentMethod)}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{formatRecurringInfo(movement)}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.asignacion || '-'}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.categoria}</TableBodyCell>
                <TableBodyCell variant="middle" rowPosition={rowPosition}>{movement.comportamiento || '-'}</TableBodyCell>
                <TableBodyCell variant="last" rowPosition={rowPosition}>{movement.comprobante || '🗂'}</TableBodyCell>
              </TableBodyRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
