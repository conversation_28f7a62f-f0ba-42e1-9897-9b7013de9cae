"use client";

import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell
} from '@admin/ui';

interface Asset {
  id: string;
  name: string;
  category: 'current' | 'fixed' | 'intangible';
  value: number;
  acquisitionDate: string;
  depreciation?: number;
  location?: string;
}

export function AssetsTable() {
  // Sample assets data
  const assets: Asset[] = [
    {
      id: 'asset-1',
      name: 'Efectivo en Caja',
      category: 'current',
      value: 45000,
      acquisitionDate: '2024-01-01',
      location: 'Oficina Principal'
    },
    {
      id: 'asset-2',
      name: 'Cuentas por Cobrar',
      category: 'current',
      value: 125000,
      acquisitionDate: '2024-06-01',
      location: 'Clientes Varios'
    },
    {
      id: 'asset-3',
      name: 'Inventario de Materiales',
      category: 'current',
      value: 85000,
      acquisitionDate: '2024-05-15',
      location: 'Almacén Principal'
    },
    {
      id: 'asset-4',
      name: 'Maquinaria Industrial',
      category: 'fixed',
      value: 350000,
      acquisitionDate: '2023-03-10',
      depreciation: 35000,
      location: 'Planta de Producción'
    },
    {
      id: 'asset-5',
      name: 'Vehículos de Transporte',
      category: 'fixed',
      value: 180000,
      acquisitionDate: '2023-08-20',
      depreciation: 18000,
      location: 'Flota Empresarial'
    },
    {
      id: 'asset-6',
      name: 'Equipo de Oficina',
      category: 'fixed',
      value: 25000,
      acquisitionDate: '2024-02-01',
      depreciation: 2500,
      location: 'Oficinas Administrativas'
    },
    {
      id: 'asset-7',
      name: 'Software y Licencias',
      category: 'intangible',
      value: 15000,
      acquisitionDate: '2024-01-15',
      location: 'Sistemas IT'
    },
    {
      id: 'asset-8',
      name: 'Marca Registrada',
      category: 'intangible',
      value: 50000,
      acquisitionDate: '2022-12-01',
      location: 'Propiedad Intelectual'
    }
  ];

  const getCategoryLabel = (category: Asset['category']) => {
    switch (category) {
      case 'current':
        return 'Activo Corriente';
      case 'fixed':
        return 'Activo Fijo';
      case 'intangible':
        return 'Activo Intangible';
      default:
        return category;
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateNetValue = (asset: Asset) => {
    return asset.value - (asset.depreciation || 0);
  };

  // Calculate totals by category
  const currentAssets = assets.filter(a => a.category === 'current');
  const fixedAssets = assets.filter(a => a.category === 'fixed');
  const intangibleAssets = assets.filter(a => a.category === 'intangible');

  const currentAssetsTotal = currentAssets.reduce((sum, asset) => sum + calculateNetValue(asset), 0);
  const fixedAssetsTotal = fixedAssets.reduce((sum, asset) => sum + calculateNetValue(asset), 0);
  const intangibleAssetsTotal = intangibleAssets.reduce((sum, asset) => sum + calculateNetValue(asset), 0);
  const totalAssets = currentAssetsTotal + fixedAssetsTotal + intangibleAssetsTotal;

  return (
    <div className="h-full">
      <div className="mb-6">
        <h3 className="modal-sub-header">Activos de la Empresa</h3>
        <p className="text-sm text-[var(--color-text-secondary)] leading-relaxed">
          Registro completo de todos los activos empresariales
        </p>
      </div>

      <div className="mb-6">
        <Table>
          <TableHeader>
            <TableHeaderRow>
              <TableHeaderCell variant="first">Activo</TableHeaderCell>
              <TableHeaderCell variant="middle">Categoría</TableHeaderCell>
              <TableHeaderCell variant="middle">Valor Original</TableHeaderCell>
              <TableHeaderCell variant="middle">Depreciación</TableHeaderCell>
              <TableHeaderCell variant="middle">Valor Neto</TableHeaderCell>
              <TableHeaderCell variant="middle">Fecha Adquisición</TableHeaderCell>
              <TableHeaderCell variant="last">Ubicación</TableHeaderCell>
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {assets.map((asset, index) => {
              const isFirst = index === 0;
              const isLast = index === assets.length - 1;
              const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';
              
              return (
                <TableBodyRow key={asset.id}>
                  <TableBodyCell variant="first" rowPosition={rowPosition}>
                    <div className="font-medium">{asset.name}</div>
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    <span className="text-xs px-2 py-1 rounded-full bg-[var(--color-background-secondary)] text-[var(--color-text-secondary)]">
                      {getCategoryLabel(asset.category)}
                    </span>
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    {formatCurrency(asset.value)}
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    {asset.depreciation ? formatCurrency(asset.depreciation) : '-'}
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    <span className="font-semibold">{formatCurrency(calculateNetValue(asset))}</span>
                  </TableBodyCell>
                  <TableBodyCell variant="middle" rowPosition={rowPosition}>
                    {formatDate(asset.acquisitionDate)}
                  </TableBodyCell>
                  <TableBodyCell variant="last" rowPosition={rowPosition}>
                    {asset.location}
                  </TableBodyCell>
                </TableBodyRow>
              );
            })}
          </TableBody>
        </Table>

        {/* Assets Summary */}
        <div className="mt-2 space-y-2">
          <div className="py-2 px-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-8)]">
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-[var(--color-text-secondary)]">Activos Corrientes:</span>
                <span className="font-medium">{formatCurrency(currentAssetsTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[var(--color-text-secondary)]">Activos Fijos:</span>
                <span className="font-medium">{formatCurrency(fixedAssetsTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[var(--color-text-secondary)]">Activos Intangibles:</span>
                <span className="font-medium">{formatCurrency(intangibleAssetsTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-bold text-[var(--color-text-primary)]">Total Activos:</span>
                <span className="font-bold text-lg">{formatCurrency(totalAssets)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
