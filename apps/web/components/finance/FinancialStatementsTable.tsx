"use client";

import { Document } from "@admin/ui";

interface FinancialStatement {
  type: 'balance' | 'income' | 'cashflow';
  name: string;
  month: string;
  year: number;
}

export function FinancialStatementsTable() {
  // Generate financial statements data for 2025
  const generateStatements = (): FinancialStatement[] => {
    const months = [
      '<PERSON><PERSON>', 'Febrero', '<PERSON><PERSON>', '<PERSON>bri<PERSON>', 'Mayo', '<PERSON><PERSON>',
      'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ];
    
    const statementTypes = [
      { type: 'balance' as const, name: 'Balance General' },
      { type: 'income' as const, name: 'Estado de Resultados' },
      { type: 'cashflow' as const, name: 'Flujo de Efectivo' }
    ];

    const statements: FinancialStatement[] = [];
    
    // Generate statements for first 4 months of 2025
    for (let monthIndex = 0; monthIndex < 4; monthIndex++) {
      for (const statementType of statementTypes) {
        statements.push({
          type: statementType.type,
          name: statementType.name,
          month: months[monthIndex] || 'Enero', // Fallback to prevent undefined
          year: 2025
        });
      }
    }
    
    return statements;
  };

  const statements = generateStatements();
  
  // Group statements by month
  const statementsByMonth = statements.reduce((acc, statement) => {
    const key = `${statement.month} ${statement.year}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(statement);
    return acc;
  }, {} as Record<string, FinancialStatement[]>);

  const handleDocumentClick = (statement: FinancialStatement) => {
    console.log(`Opening ${statement.name} for ${statement.month} ${statement.year}`);
  };

  return (
    <div className="h-full">
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4 text-[var(--color-text-primary)]">
          Estados Financieros
        </h3>
      </div>

      <div className="grid grid-cols-4 gap-8">
        {Object.entries(statementsByMonth).map(([monthYear, monthStatements]) => (
          <div key={monthYear} className="flex flex-col">
            {/* Month Header */}
            <h4 className="font-medium text-sm text-[var(--color-text-primary)] mb-4">
              {monthYear}
            </h4>

            {/* Documents for this month - arranged vertically in a column */}
            <div className="flex flex-col space-y-2">
              {monthStatements.map((statement, index) => (
                <Document
                  key={`${monthYear}-${statement.type}-${index}`}
                  name={statement.name}
                  onClick={() => handleDocumentClick(statement)}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
