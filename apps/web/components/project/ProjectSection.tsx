"use client";

import { useState } from "react";
import { ProjectCard, SearchBarSet, FilterDropdown, FilterPills, FinanceTabButton, FadeIn } from "@admin/ui";
import { useProject, useTeam } from "../../contexts";
import { InventoryItem, TeamMember } from "../../contexts/types";
import { ProjectTabFactory, type ProjectTabType, ProjectProduct, ProjectService, getProjectInitials, formatDate, calculateProgress, getStatusText, getTaskProgress, getAvailableTabs } from "./index";

// Local type definition for filter options
interface FilterOption {
  id: string;
  label: string;
  value: string;
}

interface ProjectSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}





export function ProjectSection({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }: ProjectSectionProps) {
  const { projects, selectedProject, selectProject, loading: projectLoading } = useProject();
  const { members: teamMembers } = useTeam();
  const [selectedFilters, setSelectedFilters] = useState<FilterOption[]>([]);

  // Project composition state
  const [projectProducts, setProjectProducts] = useState<ProjectProduct[]>([
    // Mock data for demonstration - Physical products with BOM
    {
      id: 'product-1',
      name: 'Estructura Metálica Principal',
      description: 'Estructura de acero para soporte principal | 12m x 8m',
      category: 'Producto',
      price: 85000,
      quantity: 1,
      bom: [
        { material: 'Vigas IPR 200', quantity: 12, unit: 'piezas', cost: 15000 },
        { material: 'Columnas HEB 160', quantity: 8, unit: 'piezas', cost: 20000 },
        { material: 'Placas de Conexión', quantity: 24, unit: 'piezas', cost: 8000 },
        { material: 'Tornillería Grado 8.8', quantity: 200, unit: 'piezas', cost: 5000 }
      ]
    },
    {
      id: 'product-2',
      name: 'Panel de Cerramiento',
      description: 'Paneles metálicos aislantes | Espesor 50mm',
      category: 'Producto',
      price: 45000,
      quantity: 25,
      bom: [
        { material: 'Lámina Galvanizada Cal. 26', quantity: 50, unit: 'm²', cost: 25000 },
        { material: 'Aislante Poliuretano', quantity: 125, unit: 'm²', cost: 15000 },
        { material: 'Perfiles de Sujeción', quantity: 100, unit: 'ml', cost: 5000 }
      ]
    }
  ]);
  const [projectServices, setProjectServices] = useState<ProjectService[]>([
    // Mock data for demonstration - Services without BOM
    {
      id: 'service-1',
      name: 'Diseño Estructural',
      description: 'Análisis | Cálculo | Planos',
      category: 'Servicio',
      price: 25000,
      duration: '2-4 semanas',
      serviceType: 'structural_design'
    },
    {
      id: 'service-2',
      name: 'Instalación y Montaje',
      description: 'Montaje completo de estructura | Incluye soldadura',
      category: 'Servicio',
      price: 35000,
      duration: '1-2 semanas',
      serviceType: 'installation_service'
    }
  ]);
  const [projectTeam, setProjectTeam] = useState<{member: TeamMember, days: number, hours: number}[]>([
    // Mock data for demonstration
    {
      member: {
        id: 'team-1',
        name: 'Roberto Silva',
        role: 'Director de Proyecto',
        email: '<EMAIL>',
        status: 'active',
        salary: 80000,
        currency: 'MXN',
        hourlyRate: 50,
        skills: ['Gestión', 'Construcción']
      },
      days: 30,
      hours: 8
    },
    {
      member: {
        id: 'team-2',
        name: 'Ana García',
        role: 'Arquitecta',
        email: '<EMAIL>',
        status: 'active',
        salary: 60000,
        currency: 'MXN',
        hourlyRate: 40,
        skills: ['Diseño', 'AutoCAD']
      },
      days: 15,
      hours: 6
    }
  ]);
  const [projectMaterials] = useState<InventoryItem[]>([]);
  const [activeCompositionTab, setActiveCompositionTab] = useState<ProjectTabType>('info');

  // Helper function for smooth expansion
  const handleSmoothExpansion = (project: any) => {
    selectProject(project);
    // Small delay for smooth transition
    setTimeout(() => {
      setIsModalExpanded(true);
    }, 150);
  };

  // Filter options for projects
  const projectFilterOptions: FilterOption[] = [
    { id: 'status-planning', label: 'Planificación', value: 'planning' },
    { id: 'status-in-progress', label: 'En proceso', value: 'in-progress' },
    { id: 'status-completed', label: 'Completado', value: 'completed' },
    { id: 'status-on-hold', label: 'En pausa', value: 'on-hold' },
  ];

  return (
    <div className={isModalExpanded ? "flex gap-12 h-full overflow-hidden" : "flex flex-col h-full overflow-hidden"}>
      {/* Left side - Project Grid */}
      <div className={isModalExpanded ? "w-[450px] flex-shrink-0 flex flex-col h-full" : "flex flex-col h-full overflow-hidden"}>

        {/* Search and Filter Controls */}
        <div className="mb-[var(--spacing-16)] flex-shrink-0">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <SearchBarSet
                placeholder="Buscar proyectos"
                exampleText="Tommy Hilfiger"
                showExample={false}
                onSearchChange={(value: string) => console.log('Searching projects:', value)}
              />
            </div>
            <div className="flex-shrink-0">
              <FilterDropdown
                options={projectFilterOptions}
                selectedOptions={selectedFilters}
                onOptionsChange={setSelectedFilters}
                onFilterClick={() => console.log('Filter clicked')}
              />
            </div>
          </div>

          {/* Selected Filter Pills */}
          <FilterPills
            selectedOptions={selectedFilters}
            onRemoveOption={(optionId: string) => {
              setSelectedFilters(prev => prev.filter(option => option.id !== optionId));
            }}
          />
        </div>

        {projectLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-[var(--color-text-secondary)]">Cargando proyectos...</div>
          </div>
        ) : (
          /* Project Cards Flex - Scrollable container */
          <div className={isModalExpanded ? "flex-1 overflow-y-auto min-h-0" : "flex-1 overflow-y-auto min-h-0"}>
            <div className="flex flex-col gap-2">
              {projects.length > 0 ? (
                projects.map((project) => {
                  const taskProgress = getTaskProgress(project);
                  const progressPercentage = calculateProgress(project);
                  
                  return (
                    <ProjectCard
                      key={project.id}
                      projectName={project.name}
                      initials={getProjectInitials(project.name)}
                      startDate={formatDate(project.startDate)}
                      endDate={project.endDate ? formatDate(project.endDate) : 'Sin fecha'}
                      currentProgress={taskProgress.current}
                      totalTasks={taskProgress.total}
                      status={getStatusText(project.status)}
                      progressPercentage={progressPercentage}
                      isActive={selectedProject?.id === project.id}
                      onCardClick={() => {
                        console.log('Selected project:', project);
                        if (!isModalExpanded) {
                          handleSmoothExpansion(project);
                        } else {
                          selectProject(project);
                        }
                      }}
                    />
                  );
                })
              ) : (
                <div className="text-center py-8 text-[var(--color-text-secondary)]">
                  No hay proyectos disponibles
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Right side - Project Composition Interface (only when expanded) */}
      {isModalExpanded && (
        <div className="flex-1 border rounded-[8px] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
          {selectedProject ? (
            <>
              {/* Project Header */}
              <div className="mb-[12px] px-16 py-20">
                <h3 className="text-xl font-semibold" style={{ color: modalHeaderTextColor }}>
                  {selectedProject.name}
                </h3>
              </div>

              {/* Composition Tabs */}
              <div className="flex gap-2 overflow-x-auto border-t border-b border-[var(--color-stroke)] py-8 px-16">
                {getAvailableTabs(
                  selectedProject,
                  projectProducts.length > 0,
                  projectServices.length > 0
                ).map(tab => (
                  <FinanceTabButton
                    key={tab.id}
                    title={tab.label}
                    isActive={activeCompositionTab === tab.id}
                    onClick={() => setActiveCompositionTab(tab.id as any)}
                  />
                ))}
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-y-auto min-h-0 px-16">
                <FadeIn key={activeCompositionTab} show={true} duration="fast" easing="smooth">
                  <ProjectTabFactory
                    activeTab={activeCompositionTab}
                    selectedProject={selectedProject}
                    projectProducts={projectProducts}
                    setProjectProducts={setProjectProducts}
                    projectServices={projectServices}
                    setProjectServices={setProjectServices}
                    projectTeam={projectTeam}
                    setProjectTeam={setProjectTeam}
                    projectMaterials={projectMaterials}
                    teamMembers={teamMembers}
                  />
                </FadeIn>

              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
              Selecciona un proyecto para ver los detalles
            </div>
          )}
        </div>
      )}
    </div>
  );
}
