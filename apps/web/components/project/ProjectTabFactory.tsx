"use client";

import React from 'react';
import { Project, TeamMember, InventoryItem, ProjectModuleType } from '../../contexts/types';
import { ProjectProduct, ProjectService } from './utils/projectUtils';
import { ProjectInfoTab } from './tabs/ProjectInfoTab';
import { ProjectCatalogTab } from './tabs/ProjectCatalogTab';
import { ProjectProductTab } from './tabs/ProjectProductTab';
import { ProjectServiceTab } from './tabs/ProjectServiceTab';
import { ProjectTeamTab } from './tabs/ProjectTeamTab';
import { ProjectTimelineTab } from './tabs/ProjectTimelineTab';
import { ProjectFinancialTab } from './tabs/ProjectFinancialTab';

export type ProjectTabType = 'info' | 'catalog' | 'products' | 'services' | 'team' | 'inventory' | 'timeline' | 'financial';

/**
 * Get available tabs based on project's enabled modules
 */
export function getAvailableTabs(project: Project, hasProducts: boolean, hasServices: boolean): { id: ProjectTabType; label: string }[] {
  const enabledModules = project.modules?.enabled || ['finance'];
  const tabs: { id: ProjectTabType; label: string }[] = [];

  // Info tab is always available
  tabs.push({ id: 'info', label: 'Información' });

  // Catalog-related tabs based on module configuration
  if (enabledModules.includes('catalog')) {
    const catalogConfig = project.modules?.configuration?.catalog;

    if (catalogConfig?.enableProducts && catalogConfig?.enableServices) {
      // Both products and services enabled
      if (hasProducts && hasServices) {
        tabs.push(
          { id: 'products', label: 'Productos' },
          { id: 'services', label: 'Servicios' }
        );
      } else if (hasProducts && !hasServices) {
        tabs.push({ id: 'products', label: 'Productos' });
      } else if (!hasProducts && hasServices) {
        tabs.push({ id: 'services', label: 'Servicios' });
      } else {
        tabs.push({ id: 'catalog', label: 'Catálogo' });
      }
    } else if (catalogConfig?.enableProducts && !catalogConfig?.enableServices) {
      // Only products enabled
      tabs.push({ id: 'products', label: 'Productos' });
    } else if (!catalogConfig?.enableProducts && catalogConfig?.enableServices) {
      // Only services enabled
      tabs.push({ id: 'services', label: 'Servicios' });
    } else {
      // Generic catalog
      tabs.push({ id: 'catalog', label: 'Catálogo' });
    }
  }

  // Other module-based tabs
  if (enabledModules.includes('team')) {
    tabs.push({ id: 'team', label: 'Equipo' });
  }

  if (enabledModules.includes('inventory')) {
    tabs.push({ id: 'inventory', label: 'Materiales' });
  }

  if (enabledModules.includes('timeline')) {
    tabs.push({ id: 'timeline', label: 'Cronograma' });
  }

  // Finance tab is always available (required module)
  tabs.push({ id: 'financial', label: 'Financiero' });

  return tabs;
}

interface ProjectTabFactoryProps {
  activeTab: ProjectTabType;
  selectedProject: Project;
  projectProducts: ProjectProduct[];
  setProjectProducts: React.Dispatch<React.SetStateAction<ProjectProduct[]>>;
  projectServices: ProjectService[];
  setProjectServices: React.Dispatch<React.SetStateAction<ProjectService[]>>;
  projectTeam: {member: TeamMember, days: number, hours: number}[];
  setProjectTeam: React.Dispatch<React.SetStateAction<{member: TeamMember, days: number, hours: number}[]>>;
  projectMaterials: InventoryItem[];
  teamMembers: TeamMember[];
}

export const ProjectTabFactory: React.FC<ProjectTabFactoryProps> = ({
  activeTab,
  selectedProject,
  projectProducts,
  setProjectProducts,
  projectServices,
  setProjectServices,
  projectTeam,
  setProjectTeam,
  projectMaterials,
  teamMembers
}) => {
  switch (activeTab) {
    case 'info':
      return <ProjectInfoTab selectedProject={selectedProject} />;
    
    case 'catalog':
      return (
        <ProjectCatalogTab
          projectProducts={projectProducts}
          setProjectProducts={setProjectProducts}
        />
      );

    case 'products':
      return (
        <ProjectProductTab
          projectProducts={projectProducts}
          setProjectProducts={setProjectProducts}
        />
      );

    case 'services':
      return (
        <ProjectServiceTab
          projectServices={projectServices}
          setProjectServices={setProjectServices}
        />
      );
    
    case 'team':
      return (
        <ProjectTeamTab
          projectTeam={projectTeam}
          setProjectTeam={setProjectTeam}
        />
      );
    
    case 'timeline':
      return (
        <ProjectTimelineTab
          selectedProject={selectedProject}
          teamMembers={teamMembers}
        />
      );
    
    case 'financial':
      return (
        <ProjectFinancialTab
          projectProducts={projectProducts}
          projectServices={projectServices}
          projectTeam={projectTeam}
          projectMaterials={projectMaterials}
        />
      );
    
    case 'inventory':
      return (
        <div className="px-4 py-5">
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            <p className="text-sm">Sección de Materiales - Próximamente</p>
          </div>
        </div>
      );
    
    default:
      return (
        <div className="px-4 py-5">
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            <p className="text-sm">Pestaña no encontrada</p>
          </div>
        </div>
      );
  }
};
