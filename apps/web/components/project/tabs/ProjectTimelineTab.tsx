"use client";

import React from 'react';
import { CalendarTimeline } from '@admin/ui';
import { Project, TeamMember } from '../../../contexts/types';
import { generateProjectCalendarTasks } from '../utils/timelineUtils';
import { useProject } from '../../../contexts/ProjectContext';

interface ProjectTimelineTabProps {
  selectedProject: Project;
  teamMembers: TeamMember[];
}

export const ProjectTimelineTab: React.FC<ProjectTimelineTabProps> = ({
  selectedProject,
  teamMembers
}) => {
  const { toggleTaskCompletion } = useProject();

  const handleTaskClick = (task: any) => {
    console.log('Project calendar task clicked:', task);
    // Toggle task completion
    toggleTaskCompletion(selectedProject.id, task.id);
  };

  return (
    <div>
      <div className="modal-main-header mt-20">
        <h4 className="modal-sub-header">
          Cronograma del Proyecto
        </h4>
        {/* Calendar Timeline integrated into sub-header */}
        <div className="overflow-hidden">
          <div className="overflow-y-auto p-4">
            <CalendarTimeline
              month={new Date().toLocaleDateString('es-ES', { month: 'long' })}
              nextMonth={new Date(new Date().getFullYear(), new Date().getMonth() + 1).toLocaleDateString('es-ES', { month: 'long' })}
              daysInMonth={new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate()}
              tasks={generateProjectCalendarTasks(selectedProject, teamMembers)}
              onTaskClick={handleTaskClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
