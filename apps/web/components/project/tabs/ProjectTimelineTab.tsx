"use client";

import React, { useState } from 'react';
import { CalendarTimeline, TaskManagementModal, type TaskFormData } from '@admin/ui';
import { Project, TeamMember } from '../../../contexts/types';
import { generateProjectCalendarTasks } from '../utils/timelineUtils';
import { useProject } from '../../../contexts/ProjectContext';
import { useTeam } from '../../../contexts/TeamContext';

interface ProjectTimelineTabProps {
  selectedProject: Project;
  teamMembers: TeamMember[];
}

export const ProjectTimelineTab: React.FC<ProjectTimelineTabProps> = ({
  selectedProject,
  teamMembers
}) => {
  const { toggleTaskCompletion, addTaskToProject, updateProjectTask, removeTaskFromProject } = useProject();
  const { members: allTeamMembers } = useTeam();
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);

  const handleTaskClick = (task: any) => {
    console.log('Project calendar task clicked:', task);
    // Toggle task completion
    toggleTaskCompletion(selectedProject.id, task.id);
  };

  const handleTaskCreate = async (data: TaskFormData) => {
    await addTaskToProject(selectedProject.id, {
      name: data.name,
      description: data.description,
      startDate: data.startDate,
      endDate: data.endDate,
      assignedTo: data.assignedTo,
      completed: false,
      priority: data.priority,
      estimatedHours: data.estimatedHours,
      tags: data.tags,
    });
  };

  const handleTaskUpdate = async (taskId: string, data: Partial<TaskFormData>) => {
    await updateProjectTask(selectedProject.id, taskId, data);
  };

  const handleTaskDelete = async (taskId: string) => {
    await removeTaskFromProject(selectedProject.id, taskId);
  };

  const handleTaskToggle = async (taskId: string) => {
    await toggleTaskCompletion(selectedProject.id, taskId);
  };

  // Convert team members to the format expected by TaskManagementModal
  const modalTeamMembers = allTeamMembers.map(member => ({
    id: member.id,
    name: member.name,
    role: member.role,
    avatar: undefined // Add avatar support later if needed
  }));

  // Convert project tasks to the format expected by TaskManagementModal
  const modalTasks = (selectedProject.tasks || []).map(task => ({
    id: task.id,
    name: task.name,
    description: task.description,
    startDate: task.startDate,
    endDate: task.endDate,
    completed: task.completed,
    assignedTo: task.assignedTo,
    priority: task.priority,
    estimatedHours: task.estimatedHours,
    actualHours: task.actualHours,
    tags: task.tags,
  }));

  return (
    <div>
      <div className="modal-main-header mt-20">
        <div className="flex items-center justify-between mb-4">
          <h4 className="modal-sub-header">
            Cronograma del Proyecto
          </h4>
          <button
            onClick={() => setIsTaskModalOpen(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Gestionar Tareas
          </button>
        </div>
        {/* Calendar Timeline integrated into sub-header */}
        <div className="overflow-hidden">
          <div className="overflow-y-auto p-4">
            <CalendarTimeline
              month={new Date().toLocaleDateString('es-ES', { month: 'long' })}
              nextMonth={new Date(new Date().getFullYear(), new Date().getMonth() + 1).toLocaleDateString('es-ES', { month: 'long' })}
              daysInMonth={new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate()}
              tasks={generateProjectCalendarTasks(selectedProject, teamMembers)}
              onTaskClick={handleTaskClick}
            />
          </div>
        </div>
      </div>

      {/* Task Management Modal */}
      <TaskManagementModal
        isOpen={isTaskModalOpen}
        onClose={() => setIsTaskModalOpen(false)}
        projectId={selectedProject.id}
        projectName={selectedProject.name}
        tasks={modalTasks}
        teamMembers={modalTeamMembers}
        onTaskCreate={handleTaskCreate}
        onTaskUpdate={handleTaskUpdate}
        onTaskDelete={handleTaskDelete}
        onTaskToggle={handleTaskToggle}
      />
    </div>
  );
};
