"use client";

import React from 'react';
import {
  CatalogItem as CatalogItemComponent,
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell
} from '@admin/ui';
import { ProjectProduct, calculateCombinedBOM } from '../utils/projectUtils';

interface ProjectProductTabProps {
  projectProducts: ProjectProduct[];
  setProjectProducts: React.Dispatch<React.SetStateAction<ProjectProduct[]>>;
}

export const ProjectProductTab: React.FC<ProjectProductTabProps> = ({
  projectProducts,
  setProjectProducts
}) => {
  return (
    <div>
      <div className="modal-main-header mt-20">
        <h4 className="modal-sub-header">
          Productos del Proyecto
        </h4>

        {/* Selected Products */}
        <div className="flex flex-wrap gap-[var(--spacing-6)]">
          {projectProducts.map((product, index) => (
            <div key={index} className="w-[200px] flex-shrink-0 overflow-hidden">
              <CatalogItemComponent
                productName={product.name || 'Producto sin nombre'}
                productDescription={product.description || 'Sin descripción'}
                categoryLabel={product.category || 'Producto'}
                imageSrc={product.imageSrc}
                isActive={false}
                variant="project"
                quantity={product.quantity || 1}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Combined BOM Table */}
      {projectProducts.length > 0 && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Lista de Materiales Combinada (BOM)
          </h4>
          <Table>
            <TableHeader>
              <TableHeaderRow>
                <TableHeaderCell variant="first">Material</TableHeaderCell>
                <TableHeaderCell variant="middle">Cantidad</TableHeaderCell>
                <TableHeaderCell variant="middle">Unidad</TableHeaderCell>
                <TableHeaderCell variant="last">Costo</TableHeaderCell>
              </TableHeaderRow>
            </TableHeader>
            <TableBody>
              {calculateCombinedBOM(projectProducts).map((item, index) => {
                const isFirst = index === 0;
                const isLast = index === calculateCombinedBOM(projectProducts).length - 1;
                const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';

                return (
                  <TableBodyRow key={index}>
                    <TableBodyCell variant="first" rowPosition={rowPosition}>
                      {item.material}
                    </TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>
                      {item.quantity}
                    </TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>
                      {item.unit}
                    </TableBodyCell>
                    <TableBodyCell variant="last" rowPosition={rowPosition}>
                      ${item.cost.toLocaleString()}
                    </TableBodyCell>
                  </TableBodyRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};
