"use client";

import React from 'react';
import { LabelValue } from '@admin/ui';
import { Project } from '../../../contexts/types';
import { formatDate, calculateProgress, getStatusText } from '../utils/projectUtils';

interface ProjectInfoTabProps {
  selectedProject: Project;
}

export const ProjectInfoTab: React.FC<ProjectInfoTabProps> = ({ selectedProject }) => {
  return (
      <div className="modal-main-header mt-20">
        <h4 className="modal-sub-header">
          Resumen del Proyecto
        </h4>
        <div className="grid grid-cols-3 gap-16 p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-8)]">
          <div className="space-y-4">
            <LabelValue
              label="Cliente"
              value="Tommy Hilfiger Corp."
            />
          </div>
          <div className="space-y-4">
            <LabelValue
              label="Estado"
              value={getStatusText(selectedProject.status)}
            />
          </div>
          <div className="space-y-4">
            <LabelValue
              label="Progreso"
              value={`${Math.round(calculateProgress(selectedProject))}%`}
            />
          </div>
          <div className="space-y-4">
            <LabelValue
              label="Fecha de Inicio"
              value={formatDate(selectedProject.startDate)}
            />
          </div>
          <div className="space-y-4">
            <LabelValue
              label="Fecha de Entrega"
              value={selectedProject.endDate ? formatDate(selectedProject.endDate) : 'Sin fecha'}
            />
          </div>
          <div className="space-y-4">
            <LabelValue
              label="Presupuesto"
              value="$250,000"
            />
          </div>
        </div>
      </div>
  );
};
