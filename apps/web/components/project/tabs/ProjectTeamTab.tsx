"use client";

import React from 'react';
import {
  CalendarTimeline,
  type CalendarTimelineTask,
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell
} from '@admin/ui';
import { TeamMember } from '../../../contexts/types';
import { calculateTeamCosts } from '../utils/projectUtils';

interface ProjectTeamTabProps {
  projectTeam: {member: TeamMember, days: number, hours: number}[];
  setProjectTeam: React.Dispatch<React.SetStateAction<{member: TeamMember, days: number, hours: number}[]>>;
}

export const ProjectTeamTab: React.FC<ProjectTeamTabProps> = ({
  projectTeam
}) => {
  // Generate calendar timeline tasks from team assignments
  const generateCalendarTasks = (): CalendarTimelineTask[] => {
    // If no team members, return sample tasks for demonstration
    if (projectTeam.length === 0) {
      return [
        {
          id: 'sample-1',
          name: 'Planificación inicial',
          startDate: 2,
          endDate: 8,
          employees: [
            {
              id: 'sample-emp1',
              name: 'Director de Proyecto',
              avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
            }
          ]
        },
        {
          id: 'sample-2',
          name: 'Desarrollo y construcción',
          startDate: 10,
          endDate: 20,
          employees: [
            {
              id: 'sample-emp2',
              name: 'Equipo de Construcción',
              avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
            }
          ]
        }
      ];
    }

    // Generate tasks from actual team data with better date logic
    const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
    let currentStartDay = 2; // Start from day 2 to avoid edge cases

    return projectTeam.map((assignment) => {
      const taskDuration = Math.min(assignment.days, 10); // Limit task duration to 10 days max
      const startDay = currentStartDay;
      const endDay = Math.min(startDay + taskDuration - 1, daysInMonth);

      // Move start day for next task (with some overlap)
      currentStartDay = Math.min(startDay + Math.floor(taskDuration * 0.7), daysInMonth - 5);

      return {
        id: assignment.member.id,
        name: assignment.member.role,
        startDate: startDay,
        endDate: endDay,
        employees: [{
          id: assignment.member.id,
          name: assignment.member.name,
          avatar: assignment.member.avatar
        }]
      };
    });
  };
  return (
    <div>
      {/* Team Timeline */}
      <div className="modal-main-header mt-20">
        <h4 className="modal-sub-header">
          Cronograma del Equipo
        </h4>
        <div className="overflow-hidden">
          <div className="overflow-y-auto p-4">
            <CalendarTimeline
              month={new Date().toLocaleDateString('es-ES', { month: 'long' })}
              daysInMonth={new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate()}
              tasks={generateCalendarTasks()}
              onTaskClick={(task) => console.log('Clicked task:', task)}
            />
          </div>
        </div>
      </div>

      {/* Team Cost Summary Table */}
      {projectTeam.length > 0 && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Resumen de Costos Laborales
          </h4>
          <Table>
            <TableHeader>
              <TableHeaderRow>
                <TableHeaderCell variant="first">Miembro</TableHeaderCell>
                <TableHeaderCell variant="middle">Días</TableHeaderCell>
                <TableHeaderCell variant="middle">Horas/día</TableHeaderCell>
                <TableHeaderCell variant="middle">Tarifa/hora</TableHeaderCell>
                <TableHeaderCell variant="last">Total</TableHeaderCell>
              </TableHeaderRow>
            </TableHeader>
            <TableBody>
              {calculateTeamCosts(projectTeam).map((cost, index) => {
                const isFirst = index === 0;
                const isLast = index === calculateTeamCosts(projectTeam).length - 1;
                const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';

                return (
                  <TableBodyRow key={index}>
                    <TableBodyCell variant="first" rowPosition={rowPosition}>
                      {cost.member}
                    </TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>
                      {cost.days}
                    </TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>
                      {cost.hours}
                    </TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>
                      ${cost.rate}
                    </TableBodyCell>
                    <TableBodyCell variant="last" rowPosition={rowPosition}>
                      ${cost.total.toLocaleString()}
                    </TableBodyCell>
                  </TableBodyRow>
                );
              })}
            </TableBody>
          </Table>

          {/* Total Summary */}
          <div className="mt-4 py-4 px-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-8)]">
            <div className="flex justify-between items-center">
              <span className="font-bold text-[var(--color-text-primary)]">Total Costos Laborales</span>
              <span className="font-bold text-lg text-[var(--color-text-primary)]">
                ${calculateTeamCosts(projectTeam).reduce((sum, cost) => sum + cost.total, 0).toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
