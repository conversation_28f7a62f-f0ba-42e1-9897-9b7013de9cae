"use client";

import React from 'react';
import { CatalogItem as CatalogItemComponent } from '@admin/ui';
import { ProjectService } from '../utils/projectUtils';

interface ProjectServiceTabProps {
  projectServices: ProjectService[];
  setProjectServices: React.Dispatch<React.SetStateAction<ProjectService[]>>;
}

export const ProjectServiceTab: React.FC<ProjectServiceTabProps> = ({
  projectServices,
  setProjectServices
}) => {
  return (
    <div>
      <div className="modal-main-header mt-20">
        <h4 className="modal-sub-header">
          Servicios del Proyecto
        </h4>

        {/* Selected Services */}
        <div className="flex flex-wrap gap-[var(--spacing-6)]">
          {projectServices.map((service, index) => (
            <div key={index} className="w-[200px] flex-shrink-0 overflow-hidden">
              <CatalogItemComponent
                productName={service.name || 'Servicio sin nombre'}
                productDescription={service.description || 'Sin descripción'}
                categoryLabel={service.category || 'Servicio'}
                imageSrc={service.imageSrc}
                isActive={false}
                variant="service"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
