"use client";

import React from 'react';
import {
  LabelValue,
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell
} from '@admin/ui';
import { TeamMember, InventoryItem } from '../../../contexts/types';
import { ProjectProduct, ProjectService, calculateProjectTotal } from '../utils/projectUtils';

interface ProjectFinancialTabProps {
  projectProducts: ProjectProduct[];
  projectServices: ProjectService[];
  projectTeam: {member: TeamMember, days: number, hours: number}[];
  projectMaterials: InventoryItem[];
}

export const ProjectFinancialTab: React.FC<ProjectFinancialTabProps> = ({
  projectProducts,
  projectServices,
  projectTeam,
  projectMaterials
}) => {
  return (
    <div>
      <div className="modal-main-header mt-20">
        <h4 className="modal-sub-header">
          Resumen Financiero del Proyecto
        </h4>
        <p className="text-sm text-[var(--color-text-secondary)] leading-relaxed">
          Consolidación automática de todos los costos del proyecto
        </p>
      </div>

      {/* Financial Summary Table */}
      <div className="mb-[var(--spacing-40)]">
        <Table>
          <TableHeader>
            <TableHeaderRow>
              <TableHeaderCell variant="first">Categoría</TableHeaderCell>
              <TableHeaderCell variant="middle">Subtotal</TableHeaderCell>
              <TableHeaderCell variant="last">% del Total</TableHeaderCell>
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {(() => {
              const items = [
                { category: 'Productos', amount: 0 },
                { category: 'Servicios', amount: 0 },
                { category: 'Costos Laborales', amount: 0 },
                { category: 'Materiales', amount: 0 },
                { category: 'Costos Fijos del Negocio', amount: 0 }
              ];

              return items.map((item, index) => {
                const isFirst = index === 0;
                const isLast = index === items.length - 1;
                const rowPosition = isFirst ? 'first' : isLast ? 'last' : 'middle';

                return (
                  <TableBodyRow key={index}>
                    <TableBodyCell variant="first" rowPosition={rowPosition}>
                      {item.category}
                    </TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>
                      $0
                    </TableBodyCell>
                    <TableBodyCell variant="last" rowPosition={rowPosition}>
                      0%
                    </TableBodyCell>
                  </TableBodyRow>
                );
              });
            })()}
          </TableBody>
        </Table>

        {/* Total Summary - separated from table with secondary background */}
        <div className="mt-2 py-3 px-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-8)]">
          <div className="flex justify-between items-center">
            <span className="font-bold text-[var(--color-text-primary)]">TOTAL DEL PROYECTO</span>
            <span className="font-bold text-lg text-[var(--color-text-primary)]">
              $0
            </span>
          </div>
        </div>
      </div>

      {/* Profit Analysis */}
      <div className="grid grid-cols-2 gap-6 mb-[var(--spacing-40)]">
        <div className="border border-[var(--color-stroke)] rounded-[var(--radius-8)] p-6 bg-[var(--color-background-primary)] hover:shadow-sm transition-shadow">
          <h5 className="font-semibold text-[var(--color-text-primary)] mb-4 text-base">Análisis de Rentabilidad</h5>
          <div className="grid grid-cols-2 gap-4">
            <LabelValue
              label="Costo Total"
              value="$0"
            />
            <LabelValue
              label="Precio de Venta"
              value="$0"
            />
            <LabelValue
              label="Ganancia Estimada"
              value="$0"
              valueClassName="text-[var(--color-text-secondary)]"
            />
            <LabelValue
              label="Margen de Ganancia"
              value="0%"
              valueClassName="text-[var(--color-text-secondary)]"
            />
          </div>
        </div>

        <div className="border border-[var(--color-stroke)] rounded-[var(--radius-8)] p-6 bg-[var(--color-background-primary)] hover:shadow-sm transition-shadow">
          <h5 className="font-semibold text-[var(--color-text-primary)] mb-4 text-base">Métricas del Proyecto</h5>
          <div className="grid grid-cols-2 gap-4">
            <LabelValue
              label="Productos Incluidos"
              value="0"
            />
            <LabelValue
              label="Servicios Incluidos"
              value="0"
            />
            <LabelValue
              label="Miembros del Equipo"
              value="0"
            />
            <LabelValue
              label="Total Horas Laborales"
              value="0"
            />
          </div>
        </div>
      </div>

      {/* Cost Breakdown Chart Placeholder */}
      <div className="border border-[var(--color-stroke)] rounded-[var(--radius-8)] p-6 bg-[var(--color-background-primary)]">
        <h5 className="font-semibold text-[var(--color-text-primary)] mb-4 text-base">Distribución de Costos</h5>
        <div className="text-center py-12 text-[var(--color-text-secondary)]">
          <p className="text-sm">Gráfico de distribución de costos - Próximamente</p>
        </div>
      </div>
    </div>
  );
};
