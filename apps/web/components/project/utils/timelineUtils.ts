import { Project, TeamMember } from "../../../contexts/types";
import { CalendarTimelineTask } from "@admin/ui";

// Helper function to generate calendar timeline tasks for a project
export const generateProjectCalendarTasks = (project: Project, teamMembers: TeamMember[]): CalendarTimelineTask[] => {
  // If project has tasks, use them directly
  if (project.tasks && project.tasks.length > 0) {
    return project.tasks.map(task => ({
      id: task.id,
      name: task.name,
      startDate: task.startDate,
      endDate: task.endDate,
      completed: task.completed,
      employees: task.assignedTo.map(memberId => {
        const member = teamMembers.find(tm => tm.id === memberId);
        return {
          id: memberId,
          name: member?.name || 'Unknown',
          avatar: member?.avatar
        };
      })
    }));
  }

  // Fallback: generate sample tasks if no tasks exist
  const startDate = new Date(project.startDate);
  const endDate = project.endDate ? new Date(project.endDate) : new Date();
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

  const tasks: CalendarTimelineTask[] = [];

  // Helper function to get team member by ID
  const getTeamMember = (id: string) => teamMembers.find(member => member.id === id);

  // Helper function to create employee object from team member
  const createEmployee = (teamMemberId: string) => {
    const member = getTeamMember(teamMemberId);
    return member ? {
      id: member.id,
      name: member.name,
      avatar: member.avatar
    } : null;
  };

  // Only include tasks that overlap with current month
  if (startDate.getMonth() === currentMonth || endDate.getMonth() === currentMonth ||
      (startDate.getMonth() < currentMonth && endDate.getMonth() > currentMonth)) {

    // Planning phase
    const planningStart = startDate.getMonth() === currentMonth ? startDate.getDate() : 1;
    const planningEnd = Math.min(planningStart + 6, daysInMonth);

    tasks.push({
      id: `${project.id}-planning`,
      name: 'Planificación y Diseño',
      startDate: planningStart,
      endDate: planningEnd,
      employees: [
        createEmployee('team-2'), // Ana García - Ingeniera de Diseño
        createEmployee('team-3')  // Mark Márquez - Diseñador
      ].filter(emp => emp !== null)
    });

    // Material procurement phase
    const procurementStart = Math.max(planningEnd - 2, 1);
    const procurementEnd = Math.min(planningEnd + 3, daysInMonth);

    if (procurementStart <= daysInMonth) {
      tasks.push({
        id: `${project.id}-procurement`,
        name: 'Compra de Materiales',
        startDate: procurementStart,
        endDate: procurementEnd,
        employees: [
          createEmployee('team-6'), // Laura Fernández - Analista de Procesos
          createEmployee('team-5')  // Roberto Silva - Técnico de Mantenimiento
        ].filter(emp => emp !== null)
      });
    }

    // Construction/Fabrication phase
    const constructionStart = Math.max(planningEnd + 1, 1);
    const constructionEnd = Math.min(constructionStart + 14, daysInMonth);

    if (constructionStart <= daysInMonth) {
      tasks.push({
        id: `${project.id}-construction`,
        name: 'Fabricación y Construcción',
        startDate: constructionStart,
        endDate: constructionEnd,
        employees: [
          createEmployee('team-1'), // Franco Eduardo - Supervisor de Producción
          createEmployee('team-5'), // Roberto Silva - Técnico de Mantenimiento
          createEmployee('team-3')  // Mark Márquez - Diseñador
        ].filter(emp => emp !== null)
      });
    }

    // Quality control and finishing phase
    const finishingStart = Math.max(constructionEnd + 1, 1);
    const finishingEnd = endDate.getMonth() === currentMonth ? endDate.getDate() : daysInMonth;

    if (finishingStart <= daysInMonth && finishingStart <= finishingEnd) {
      tasks.push({
        id: `${project.id}-finishing`,
        name: 'Control de Calidad y Acabados',
        startDate: finishingStart,
        endDate: finishingEnd,
        employees: [
          createEmployee('team-4'), // María González - Coordinadora de Calidad
          createEmployee('team-2')  // Ana García - Ingeniera de Diseño
        ].filter(emp => emp !== null)
      });
    }

    // Installation and delivery phase
    const deliveryStart = Math.max(finishingEnd - 2, 1);
    const deliveryEnd = endDate.getMonth() === currentMonth ? endDate.getDate() : daysInMonth;

    if (deliveryStart <= daysInMonth && deliveryStart <= deliveryEnd && deliveryStart <= finishingEnd) {
      tasks.push({
        id: `${project.id}-delivery`,
        name: 'Instalación y Entrega',
        startDate: deliveryStart,
        endDate: deliveryEnd,
        employees: [
          createEmployee('team-1'), // Franco Eduardo - Supervisor de Producción
          createEmployee('team-6')  // Laura Fernández - Analista de Procesos
        ].filter(emp => emp !== null)
      });
    }
  }

  return tasks.filter(task => task.employees.length > 0); // Only return tasks with valid employees
};
