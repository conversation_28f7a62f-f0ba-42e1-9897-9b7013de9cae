import { Project, TeamMember, InventoryItem } from "../../../contexts/types";
import { CalendarTimelineTask } from "@admin/ui";

// Local interface for project products with BOM
export interface ProjectProduct {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  quantity?: number;
  imageSrc?: string;
  bom: {
    material: string;
    quantity: number;
    unit: string;
    cost: number;
  }[];
}

// Local interface for project services
export interface ProjectService {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  duration?: string;
  imageSrc?: string;
  serviceType?: string;
  serviceSpecifications?: Record<string, any>;
}

// Helper function to get project initials
export const getProjectInitials = (projectName: string): string => {
  return projectName
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// Helper function to format date
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const months = [
    'Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun',
    'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'
  ];
  
  const day = date.getDate().toString().padStart(2, '0');
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  
  return `${day} ${month}, ${year}`;
};

// Helper function to calculate progress percentage
export const calculateProgress = (project: Project): number => {
  if (project.status === 'completed') return 100;
  if (project.status === 'planning') return 10;
  if (project.status === 'on-hold') return 50;
  
  // For in-progress projects, calculate based on time elapsed
  const startDate = new Date(project.startDate);
  const endDate = project.endDate ? new Date(project.endDate) : new Date();
  const currentDate = new Date();
  
  if (currentDate < startDate) return 0;
  if (currentDate > endDate) return 100;
  
  const totalDuration = endDate.getTime() - startDate.getTime();
  const elapsed = currentDate.getTime() - startDate.getTime();
  
  return Math.min(Math.max((elapsed / totalDuration) * 100, 0), 100);
};

// Helper function to get status text in Spanish
export const getStatusText = (status: Project['status']): string => {
  switch (status) {
    case 'planning': return 'Planificación';
    case 'in-progress': return 'En proceso';
    case 'completed': return 'Completado';
    case 'on-hold': return 'En pausa';
    default: return 'Desconocido';
  }
};

// Helper function to calculate task progress from actual project tasks
export const getTaskProgress = (project: Project): { current: number; total: number } => {
  if (!project.tasks || project.tasks.length === 0) {
    // Fallback for projects without tasks
    return { current: 0, total: 0 };
  }

  const totalTasks = project.tasks.length;
  const completedTasks = project.tasks.filter(task => task.completed).length;

  return { current: completedTasks, total: totalTasks };
};

// Helper function to calculate combined BOM from all products
export const calculateCombinedBOM = (products: ProjectProduct[]): {material: string, quantity: number, unit: string, cost: number}[] => {
  const bomMap = new Map<string, {quantity: number, unit: string, cost: number}>();

  products.forEach(product => {
    if (product.bom) {
      product.bom.forEach(bomItem => {
        const key = bomItem.material;
        if (bomMap.has(key)) {
          const existing = bomMap.get(key)!;
          bomMap.set(key, {
            ...existing,
            quantity: existing.quantity + bomItem.quantity,
            cost: existing.cost + (bomItem.cost || 0)
          });
        } else {
          bomMap.set(key, {
            quantity: bomItem.quantity,
            unit: bomItem.unit,
            cost: bomItem.cost || 0
          });
        }
      });
    }
  });

  return Array.from(bomMap.entries()).map(([material, data]) => ({
    material,
    ...data
  }));
};

// Helper function to calculate team costs
export const calculateTeamCosts = (teamAssignments: {member: TeamMember, days: number, hours: number}[]): {member: string, days: number, hours: number, rate: number, total: number}[] => {
  return teamAssignments.map(assignment => ({
    member: assignment.member.name,
    days: assignment.days,
    hours: assignment.hours,
    rate: assignment.member.hourlyRate || 0,
    total: assignment.days * assignment.hours * (assignment.member.hourlyRate || 0)
  }));
};

// Helper function to calculate total project cost
export const calculateProjectTotal = (
  products: ProjectProduct[],
  services: ProjectService[],
  teamAssignments: {member: TeamMember, days: number, hours: number}[],
  materials: InventoryItem[]
): {
  productsCost: number,
  servicesCost: number,
  laborCost: number,
  materialsCost: number,
  fixedCosts: number,
  total: number
} => {
  const productsCost = products.reduce((sum, product) => sum + (product.price || 0), 0);
  const servicesCost = services.reduce((sum, service) => sum + (service.price || 0), 0);
  const laborCost = teamAssignments.reduce((sum, assignment) =>
    sum + (assignment.days * assignment.hours * (assignment.member.hourlyRate || 0)), 0);
  const materialsCost = materials.reduce((sum, material) => sum + (material.cost || 0), 0);
  const fixedCosts = 5000; // Mock fixed business costs

  return {
    productsCost,
    servicesCost,
    laborCost,
    materialsCost,
    fixedCosts,
    total: productsCost + servicesCost + laborCost + materialsCost + fixedCosts
  };
};
