"use client";

import React from 'react';
import { useUser, BusinessType } from '../contexts';

/**
 * Development component for testing different business type layouts
 * In production, this would be part of user settings or account setup
 */
export const BusinessTypeSwitcher: React.FC = () => {
  const { user, setBusinessType } = useUser();

  if (!user) {
    return null;
  }

  const businessTypes: { value: BusinessType; label: string }[] = [
    { value: 'fabrication', label: 'Fabricación' },
    { value: 'retail', label: 'Retail/Ventas' },
    { value: 'services', label: 'Servicios' },
    { value: 'manufacturing', label: 'Manufactura' },
    { value: 'distribution', label: 'Distribución' },
    { value: 'construction', label: 'Construcción' },
  ];

  return (
    <div className="flex items-center gap-2 opacity-50 hover:opacity-70 transition-opacity">
      <div className="text-xs text-gray-500 font-medium">
        Demo:
      </div>
      <select
        value={user.businessType}
        onChange={(e) => setBusinessType(e.target.value as BusinessType)}
        className="text-xs border-0 bg-transparent text-gray-600 font-medium focus:outline-none cursor-pointer appearance-none"
      >
        {businessTypes.map((type) => (
          <option key={type.value} value={type.value}>
            {type.label}
          </option>
        ))}
      </select>
    </div>
  );
};
