"use client";

import React, { useState, useEffect } from 'react';
import { CatalogItem } from '../../contexts/types';
import { fetchServiceTemplate, ServiceTemplate } from '../../services/serviceTemplates';
import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
  Document
} from '@admin/ui';

interface ServicesCatalogDetailProps {
  item: CatalogItem;
  modalHeaderTextColor: string;
}

/**
 * Catalog detail layout specifically designed for services
 * Shows deliverables, requirements, timeline, and service-specific information
 */
export const ServicesCatalogDetail: React.FC<ServicesCatalogDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  const [serviceTemplate, setServiceTemplate] = useState<ServiceTemplate | null>(null);
  const [templateLoading, setTemplateLoading] = useState(false);

  // Load service template if serviceType is specified
  useEffect(() => {
    if (item.details?.serviceType) {
      setTemplateLoading(true);
      fetchServiceTemplate(item.details.serviceType)
        .then(template => {
          setServiceTemplate(template);
        })
        .catch(error => {
          console.error('Failed to load service template:', error);
          setServiceTemplate(null);
        })
        .finally(() => {
          setTemplateLoading(false);
        });
    }
  }, [item.details?.serviceType]);

  if (!item.details) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary-text)]">
        No hay detalles disponibles para este servicio
      </div>
    );
  }

  return (
    <div>
      <div className="modal-main-header">
        <h3 className="text-xl font-semibold mb-4" style={{ color: modalHeaderTextColor }}>
          {item.details.title}
        </h3>

        {/* Service Type and Duration */}
        <div className="text-sm text-[var(--color-text-secondary)]">
          Tipo: {item.details.type}
        </div>
        {item.details.duration && (
          <div className="text-sm text-[var(--color-text-secondary)] mt-1">
            Duración estimada: {item.details.duration}
          </div>
        )}
      </div>

      {/* Service Specifications Section - Dynamic based on template */}
      {serviceTemplate && item.details.serviceSpecifications && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Especificaciones del Servicio
          </h4>
          {templateLoading ? (
            <div className="text-sm text-[var(--color-text-secondary)]">
              Cargando especificaciones...
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableHeaderRow>
                  {serviceTemplate.fields.map((field, index) => {
                    const value = item.details?.serviceSpecifications?.[field.name];
                    if (value === undefined || value === null) return null;

                    const totalFields = serviceTemplate.fields.filter(f =>
                      item.details?.serviceSpecifications?.[f.name] !== undefined
                    ).length;
                    const visibleIndex = serviceTemplate.fields
                      .slice(0, index)
                      .filter(f => item.details?.serviceSpecifications?.[f.name] !== undefined)
                      .length;
                    const variant = visibleIndex === 0 ? 'first' :
                      visibleIndex === totalFields - 1 ? 'last' : 'middle';

                    return (
                      <TableHeaderCell key={field.name} variant={variant}>
                        {field.name.replace(/_/g, ' ')}
                        {field.unit && (
                          <div className="text-xs text-[var(--color-text-secondary)] font-normal">
                            ({field.unit})
                          </div>
                        )}
                      </TableHeaderCell>
                    );
                  })}
                </TableHeaderRow>
              </TableHeader>
              <TableBody>
                <TableBodyRow>
                  {serviceTemplate.fields.map((field, index) => {
                    const value = item.details?.serviceSpecifications?.[field.name];
                    if (value === undefined || value === null) return null;

                    const totalFields = serviceTemplate.fields.filter(f =>
                      item.details?.serviceSpecifications?.[f.name] !== undefined
                    ).length;
                    const visibleIndex = serviceTemplate.fields
                      .slice(0, index)
                      .filter(f => item.details?.serviceSpecifications?.[f.name] !== undefined)
                      .length;
                    const variant = visibleIndex === 0 ? 'first' :
                      visibleIndex === totalFields - 1 ? 'last' : 'middle';

                    // Format value based on field type
                    let displayValue = value;
                    if (field.type === 'multiselect' && Array.isArray(value)) {
                      displayValue = value.join(', ');
                    } else if (field.type === 'boolean') {
                      displayValue = value ? 'Sí' : 'No';
                    }

                    return (
                      <TableBodyCell key={field.name} variant={variant} rowPosition="first">
                        {displayValue}
                      </TableBodyCell>
                    );
                  })}
                </TableBodyRow>
              </TableBody>
            </Table>
          )}
        </div>
      )}

      {/* Deliverables Section - Key for services */}
      {item.details.deliverables && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Entregables del Servicio
          </h4>
          <Table>
            <TableHeader>
              <TableHeaderRow>
                <TableHeaderCell variant="first">Entregable</TableHeaderCell>
                <TableHeaderCell variant="middle">Descripción</TableHeaderCell>
                <TableHeaderCell variant="last">Cronograma</TableHeaderCell>
              </TableHeaderRow>
            </TableHeader>
            <TableBody>
              {item.details.deliverables.map((deliverable, index) => {
                const totalRows = item.details?.deliverables?.length ?? 0;
                const rowPosition = index === 0 ? 'first' : index === totalRows - 1 ? 'last' : 'middle';
                return (
                  <TableBodyRow key={deliverable.id}>
                    <TableBodyCell variant="first" rowPosition={rowPosition}>{deliverable.name}</TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>{deliverable.description}</TableBodyCell>
                    <TableBodyCell variant="last" rowPosition={rowPosition}>{deliverable.timeline}</TableBodyCell>
                  </TableBodyRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Requirements Section */}
      {item.details.requirements && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Requisitos del Cliente
          </h4>
          <div className="bg-blue-50 p-3 rounded text-sm text-[var(--color-text-secondary)]">
            <ul className="space-y-2">
              {item.details.requirements.map((requirement, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-600 mt-1">•</span>
                  <span>{requirement}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Service Documentation */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Documentación del Servicio
        </h4>
        <div className="space-y-2">
          {item.details.documents.map((doc) => (
            <Document
              key={doc.id}
              name={doc.name}
              onClick={() => console.log('Opening document:', doc.name)}
            />
          ))}
        </div>
      </div>

      {/* Service Process Images */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Proceso del Servicio
        </h4>
        <div className="grid grid-cols-4 gap-2">
          {item.details.images.map((_, i) => (
            <div key={i} className="w-20 h-20 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
              {i + 1}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
