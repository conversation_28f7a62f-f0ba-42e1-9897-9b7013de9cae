"use client";

import { useState } from "react";
import { CatalogItem, SearchBarSet, FilterDropdown, FilterPills, type FilterOption, FadeIn } from "@admin/ui";
import { useCatalog, useUser } from "../../contexts";
import { CatalogDetailFactory } from "./CatalogDetailFactory";

interface CatalogSectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}

export function CatalogSection({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }: CatalogSectionProps) {
  const { items: catalogItems, selectedItem, selectItem, loading: catalogLoading } = useCatalog();
  const { user } = useUser();
  const [selectedFilters, setSelectedFilters] = useState<FilterOption[]>([]);

  // Helper function for smooth expansion
  const handleSmoothExpansion = (item: any) => {
    selectItem(item);
    // Small delay for smooth transition
    setTimeout(() => {
      setIsModalExpanded(true);
    }, 150);
  };

  // Catalog-specific filter options
  const catalogFilterOptions: FilterOption[] = [
    { id: 'materials', label: 'Materiales', value: 'materials' },
    { id: 'tools', label: 'Herramientas', value: 'tools' },
    { id: 'equipment', label: 'Equipos', value: 'equipment' },
    { id: 'consumables', label: 'Consumibles', value: 'consumables' },
    { id: 'in_stock', label: 'En Stock', value: 'in_stock' },
    { id: 'low_stock', label: 'Stock Bajo', value: 'low_stock' },
  ];

  return (
    <div className={isModalExpanded ? "flex gap-12 h-full overflow-hidden" : "h-full flex flex-col overflow-hidden"}>
      {/* Left side - Catalog Items Grid */}
      <div className={isModalExpanded ? "w-[450px] flex-shrink-0 flex flex-col overflow-hidden" : "flex flex-col h-full overflow-hidden"}>
        {/* Search and Filter Controls - Fixed at top */}
        <div className="mb-[var(--spacing-16)] flex-shrink-0">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <SearchBarSet
                placeholder="Buscar"
                exampleText="Lamina de acero al carbon"
                showExample={false}
                onSearchChange={(value: string) => console.log('Searching catalog:', value)}
              />
            </div>
            <div className="flex-shrink-0">
              <FilterDropdown
                options={catalogFilterOptions}
                selectedOptions={selectedFilters}
                onOptionsChange={setSelectedFilters}
                onFilterClick={() => console.log('Filter clicked')}
              />
            </div>
          </div>

          {/* Selected Filter Pills */}
          <FilterPills
            selectedOptions={selectedFilters}
            onRemoveOption={(optionId: string) => {
              setSelectedFilters(prev => prev.filter(option => option.id !== optionId));
            }}
          />
        </div>

        {/* Catalog Items Grid Container - Scrollable */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {catalogLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-[var(--color-text-secondary)]">Cargando catálogo...</div>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {catalogItems.map((item) => (
                <div key={item.id} onClick={() => {
                  if (!isModalExpanded) {
                    handleSmoothExpansion(item);
                  } else {
                    selectItem(item);
                  }
                }}>
                  <CatalogItem
                    productName={item.productName}
                    productDescription={item.productDescription}
                    categoryLabel={item.categoryLabel}
                    imageSrc={item.imageSrc}
                    isActive={selectedItem?.id === item.id}
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Right side - Item Details (only when expanded) */}
      {isModalExpanded && (
        <div className="flex-1 border border-[var(--color-stroke)] rounded-[var(--radius-8)] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
          <div className="flex-1 px-4 py-5 overflow-y-auto min-h-0">
            <FadeIn key={selectedItem?.id || 'no-item'} show={true} duration="fast" easing="smooth">
              {selectedItem && user ? (
                <CatalogDetailFactory
                  businessType={user.businessType}
                  item={selectedItem}
                  modalHeaderTextColor={modalHeaderTextColor}
                />
              ) : selectedItem && !user ? (
                <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                  Cargando información del usuario...
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                  Selecciona un producto para ver los detalles
                </div>
              )}
            </FadeIn>
          </div>
        </div>
      )}
    </div>
  );
}
