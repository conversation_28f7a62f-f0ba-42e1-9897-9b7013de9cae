"use client";

import React from 'react';
import { BusinessType, CatalogItem } from '../../contexts/types';
import { FabricationCatalogDetail } from './FabricationCatalogDetail';
import { RetailCatalogDetail } from './RetailCatalogDetail';
import { ServicesCatalogDetail } from './ServicesCatalogDetail';

interface CatalogDetailFactoryProps {
  businessType: BusinessType;
  item: CatalogItem;
  modalHeaderTextColor: string;
}

/**
 * Factory component that renders the appropriate catalog detail layout
 * based on the user's business type
 */
export const CatalogDetailFactory: React.FC<CatalogDetailFactoryProps> = ({
  businessType,
  item,
  modalHeaderTextColor
}) => {
  // First check if this is a service item
  if (item.details?.type === 'Service') {
    return (
      <ServicesCatalogDetail
        item={item}
        modalHeaderTextColor={modalHeaderTextColor}
      />
    );
  }

  // Then use business type for product layouts
  switch (businessType) {
    case 'fabrication':
      return (
        <FabricationCatalogDetail 
          item={item} 
          modalHeaderTextColor={modalHeaderTextColor} 
        />
      );
    
    case 'retail':
      return (
        <RetailCatalogDetail 
          item={item} 
          modalHeaderTextColor={modalHeaderTextColor} 
        />
      );
    
    case 'services':
      return (
        <ServicesCatalogDetail
          item={item}
          modalHeaderTextColor={modalHeaderTextColor}
        />
      );
    
    case 'manufacturing':
      // TODO: Create ManufacturingCatalogDetail component
      return (
        <div className="p-4 bg-blue-50 rounded">
          <h4 className="modal-sub-header">
            Manufacturing Layout
          </h4>
          <p className="text-sm text-[var(--color-text-secondary)]">
            Manufacturing-specific catalog detail layout coming soon...
          </p>
          {/* Fallback to fabrication layout for now */}
          <div className="mt-4">
            <FabricationCatalogDetail 
              item={item} 
              modalHeaderTextColor={modalHeaderTextColor} 
            />
          </div>
        </div>
      );
    
    case 'distribution':
      // TODO: Create DistributionCatalogDetail component
      return (
        <div className="p-4 bg-purple-50 rounded">
          <h4 className="modal-sub-header">
            Distribution Layout
          </h4>
          <p className="text-sm text-[var(--color-text-secondary)]">
            Distribution-specific catalog detail layout coming soon...
          </p>
          {/* Fallback to retail layout for now */}
          <div className="mt-4">
            <RetailCatalogDetail 
              item={item} 
              modalHeaderTextColor={modalHeaderTextColor} 
            />
          </div>
        </div>
      );
    
    case 'construction':
      // TODO: Create ConstructionCatalogDetail component
      return (
        <div className="p-4 bg-orange-50 rounded">
          <h4 className="modal-sub-header">
            Construction Layout
          </h4>
          <p className="text-sm text-[var(--color-text-secondary)]">
            Construction-specific catalog detail layout coming soon...
          </p>
          {/* Fallback to fabrication layout for now */}
          <div className="mt-4">
            <FabricationCatalogDetail 
              item={item} 
              modalHeaderTextColor={modalHeaderTextColor} 
            />
          </div>
        </div>
      );
    
    default:
      // Fallback to fabrication layout
      return (
        <FabricationCatalogDetail 
          item={item} 
          modalHeaderTextColor={modalHeaderTextColor} 
        />
      );
  }
};
