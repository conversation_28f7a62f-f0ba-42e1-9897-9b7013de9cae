"use client";

import React from 'react';
import { CatalogItem } from '../../contexts/types';
import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
  Document
} from '@admin/ui';

interface FabricationCatalogDetailProps {
  item: CatalogItem;
  modalHeaderTextColor: string;
}

/**
 * Catalog detail layout specifically designed for fabrication businesses
 * Shows materials, documents, and manufacturing-specific information
 */
export const FabricationCatalogDetail: React.FC<FabricationCatalogDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  if (!item.details) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary-text)]">
        No hay detalles disponibles para este producto
      </div>
    );
  }

  return (
    <div>
      <div className="modal-main-header">
        <h3 className="text-xl font-semibold mb-4" style={{ color: modalHeaderTextColor }}>
          {item.details.title}
        </h3>

        {/* Product Type and Fabrication */}
        <div className="text-sm text-[var(--color-text-secondary)]">
          Tipo: {item.details.type}
        </div>
        {item.details.fabrication && (
          <div className="text-sm text-[var(--color-text-secondary)] mt-1">
            Proceso de fabricación: {item.details.fabrication}
          </div>
        )}
      </div>

      {/* Materials Section - Only for products */}
      {item.details.materials && item.details.materials.length > 0 && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Materiales Requeridos
          </h4>
          <Table>
            <TableHeader>
              <TableHeaderRow>
                <TableHeaderCell variant="first">Nombre</TableHeaderCell>
                <TableHeaderCell variant="middle">Material</TableHeaderCell>
                <TableHeaderCell variant="middle">Cantidad</TableHeaderCell>
                <TableHeaderCell variant="last">Unidad</TableHeaderCell>
              </TableHeaderRow>
            </TableHeader>
            <TableBody>
              {item.details.materials.map((material, index) => {
                const totalRows = item.details?.materials?.length ?? 0;
                const rowPosition = index === 0 ? 'first' : index === totalRows - 1 ? 'last' : 'middle';
                return (
                  <TableBodyRow key={material.id}>
                    <TableBodyCell variant="first" rowPosition={rowPosition}>{material.name}</TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>{material.material}</TableBodyCell>
                    <TableBodyCell variant="middle" rowPosition={rowPosition}>{material.quantity}</TableBodyCell>
                    <TableBodyCell variant="last" rowPosition={rowPosition}>{material.unit}</TableBodyCell>
                  </TableBodyRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Manufacturing Specifications */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Especificaciones de Fabricación
        </h4>
        <div className="bg-gray-50 p-3 rounded text-sm text-[var(--color-text-secondary)]">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Proceso:</span> {item.details.fabrication}
            </div>
            <div>
              <span className="font-medium">Tiempo estimado:</span> 2-3 días
            </div>
            <div>
              <span className="font-medium">Herramientas:</span> Soldadora, Cortadora
            </div>
            <div>
              <span className="font-medium">Acabado:</span> Pintura anticorrosiva
            </div>
          </div>
        </div>
      </div>

      {/* Technical Documents */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Documentos Técnicos
        </h4>
        <div className="space-y-2">
          {item.details.documents.map((doc) => (
            <Document
              key={doc.id}
              name={doc.name}
              onClick={() => console.log('Opening document:', doc.name)}
            />
          ))}
        </div>
      </div>

      {/* Process Images */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Imágenes del Proceso
        </h4>
        <div className="grid grid-cols-5 gap-2">
          {item.details.images.map((_, i) => (
            <div key={i} className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
              {i + 1}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
