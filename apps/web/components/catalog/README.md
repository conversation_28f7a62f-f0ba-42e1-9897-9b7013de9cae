# Business-Type-Specific Catalog Detail Layouts

This directory contains the business-type-specific catalog detail layouts that provide tailored user experiences based on the user's business type.

## Overview

When users create an account, they specify their business type, which determines the UI layout they see for catalog item details. This approach ensures that each business type gets the most relevant information and functionality for their specific needs.

## Architecture

```
catalog/
├── CatalogSection.tsx           # Main catalog section with search and grid
├── CatalogDetailFactory.tsx     # Factory component that routes to appropriate layout
├── FabricationCatalogDetail.tsx # Layout for fabrication businesses
├── RetailCatalogDetail.tsx      # Layout for retail businesses
├── ServicesCatalogDetail.tsx    # Layout for services businesses
├── index.ts                     # Clean exports
└── README.md                    # This documentation
```

## Business Types Supported

### 1. **Fabrication** (Implemented)
- **Focus**: Manufacturing processes, materials, technical specifications
- **Key Features**:
  - Materials required with quantities and units
  - Manufacturing specifications (process, time, tools, finish)
  - Technical documents with file types
  - Process images with numbered placeholders
- **Use Case**: Metal fabrication, custom manufacturing, workshop operations

### 2. **Retail** (Implemented)
- **Focus**: Sales, pricing, inventory management
- **Key Features**:
  - Pricing information (sale price, cost, margin, max discount)
  - Inventory status (stock levels, restock dates)
  - Sales performance metrics (units sold, ratings, satisfaction)
  - Product gallery optimized for customer presentation
- **Use Case**: Stores, e-commerce, product resellers

### 3. **Services** (Implemented)
- **Focus**: Service packages, deliverables, requirements
- **Key Features**:
  - Dynamic service specifications based on service type
  - Deliverables with descriptions and timelines
  - Client requirements and documentation
  - Service process visualization
- **Use Case**: Consulting, maintenance, professional services

### 4. **Manufacturing** (Placeholder)
- **Planned Features**: Production capacity, quality metrics, supply chain
- **Use Case**: Large-scale production, industrial manufacturing

### 5. **Distribution** (Placeholder)
- **Planned Features**: Logistics, shipping, warehouse management
- **Use Case**: Wholesalers, distributors, logistics companies

### 6. **Construction** (Placeholder)
- **Planned Features**: Project timelines, safety requirements, compliance
- **Use Case**: Construction companies, contractors, builders

## Usage

### Basic Implementation

```tsx
import { CatalogSection } from '../components/catalog';

function HomePage() {
  const [isModalExpanded, setIsModalExpanded] = useState(false);

  return (
    <div>
      <CatalogSection
        isModalExpanded={isModalExpanded}
        setIsModalExpanded={setIsModalExpanded}
        modalHeaderTextColor="#333"
      />
    </div>
  );
}
```

### Direct Layout Usage

```tsx
import { FabricationCatalogDetail } from '../components/catalog';

function CustomCatalogView() {
  return (
    <FabricationCatalogDetail
      item={catalogItem}
      modalHeaderTextColor="#333"
    />
  );
}
```

## Components

### CatalogSection
Main component that handles the catalog grid view and detail view. Includes:
- Search and filter functionality
- Responsive grid layout
- Item selection and expansion
- Integration with catalog context

### CatalogDetailFactory
Factory component that routes to the appropriate detail layout based on business type.

### Detail Components
Individual components for each business type, each optimized for specific workflows and information needs.

## Design Principles

### 1. **Business-Specific Information**
Each layout shows information most relevant to that business type:
- **Fabrication**: Materials, processes, technical specs
- **Retail**: Pricing, inventory, sales metrics
- **Services**: Deliverables, requirements, timelines

### 2. **Consistent Interface**
All layouts implement the same props interface for easy integration.

### 3. **Fallback Strategy**
Unknown business types fall back to the fabrication layout to ensure the app never breaks.

### 4. **Extensible Architecture**
New business types can be added without modifying existing code.

## Benefits

- **Tailored User Experience**: Each business type gets relevant information
- **Improved Efficiency**: Users see only what they need
- **Scalable Architecture**: Easy to add new business types
- **Maintainable Code**: Separation of concerns by business type
- **Future-Proof**: Ready for business expansion and new features
