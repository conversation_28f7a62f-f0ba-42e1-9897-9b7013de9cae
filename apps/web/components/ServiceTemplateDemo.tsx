"use client";

import React, { useState, useEffect } from 'react';
import { 
  fetchServiceTemplate, 
  fetchAvailableServiceTypes, 
  ServiceTemplate 
} from '../services/serviceTemplates';

/**
 * Demo component to showcase service template functionality
 * This can be used for testing and development
 */
export const ServiceTemplateDemo: React.FC = () => {
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<string>('');
  const [template, setTemplate] = useState<ServiceTemplate | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load available service types on mount
  useEffect(() => {
    fetchAvailableServiceTypes()
      .then(types => {
        setAvailableTypes(types);
        if (types.length > 0 && types[0]) {
          setSelectedType(types[0]);
        }
      })
      .catch(err => {
        setError('Failed to load service types');
        console.error(err);
      });
  }, []);

  // Load template when type changes
  useEffect(() => {
    if (selectedType) {
      setLoading(true);
      setError(null);
      fetchServiceTemplate(selectedType)
        .then(template => {
          setTemplate(template);
        })
        .catch(err => {
          setError(`Failed to load template for ${selectedType}`);
          setTemplate(null);
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [selectedType]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Service Template Demo</h1>
      
      {/* Service Type Selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          Select Service Type:
        </label>
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value)}
          className="border rounded px-3 py-2 w-full max-w-md"
        >
          <option value="">-- Select a service --</option>
          {availableTypes.map(type => (
            <option key={type} value={type}>
              {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </option>
          ))}
        </select>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <p className="text-gray-600">Loading template...</p>
        </div>
      )}

      {/* Template Display */}
      {template && !loading && (
        <div className="space-y-6">
          {/* Template Info */}
          <div className="bg-blue-50 border border-blue-200 rounded p-4">
            <h2 className="text-xl font-semibold mb-2">{template.display_name}</h2>
            <p className="text-gray-700 mb-2">{template.description}</p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Category:</span> {template.category}
              </div>
              <div>
                <span className="font-medium">Duration:</span> {template.typical_duration}
              </div>
              <div>
                <span className="font-medium">Pricing:</span> {template.pricing_model}
              </div>
              <div>
                <span className="font-medium">Fields:</span> {template.fields.length}
              </div>
            </div>
          </div>

          {/* Fields Table */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Service Fields</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full border border-gray-200 rounded">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left border-b">Field Name</th>
                    <th className="px-4 py-2 text-left border-b">Type</th>
                    <th className="px-4 py-2 text-left border-b">Required</th>
                    <th className="px-4 py-2 text-left border-b">Unit</th>
                    <th className="px-4 py-2 text-left border-b">Options</th>
                    <th className="px-4 py-2 text-left border-b">Description</th>
                  </tr>
                </thead>
                <tbody>
                  {template.fields.map((field, index) => (
                    <tr key={field.name} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-4 py-2 border-b font-medium">
                        {field.name.replace(/_/g, ' ')}
                      </td>
                      <td className="px-4 py-2 border-b">
                        <span className={`px-2 py-1 rounded text-xs ${
                          field.type === 'select' ? 'bg-blue-100 text-blue-800' :
                          field.type === 'number' ? 'bg-green-100 text-green-800' :
                          field.type === 'multiselect' ? 'bg-purple-100 text-purple-800' :
                          field.type === 'boolean' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {field.type}
                        </span>
                      </td>
                      <td className="px-4 py-2 border-b">
                        {field.required ? (
                          <span className="text-red-600 font-medium">Yes</span>
                        ) : (
                          <span className="text-gray-500">No</span>
                        )}
                      </td>
                      <td className="px-4 py-2 border-b text-sm text-gray-600">
                        {field.unit || '-'}
                      </td>
                      <td className="px-4 py-2 border-b text-sm">
                        {field.options ? (
                          <div className="max-w-xs">
                            {field.options.slice(0, 3).join(', ')}
                            {field.options.length > 3 && ` (+${field.options.length - 3} more)`}
                          </div>
                        ) : '-'}
                      </td>
                      <td className="px-4 py-2 border-b text-sm text-gray-600 max-w-xs">
                        {field.description || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* JSON Preview */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Template JSON</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
              {JSON.stringify(template, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};
