"use client";

import React, { useState, useEffect } from 'react';
import { InventoryItem } from '../../contexts/types';
import { useInventory } from '../../contexts';
import { fetchProductTypeTemplate, type ProductTypeTemplate } from '../../services/finishedProductTemplates';
import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
} from '@admin/ui';

interface FinishedProductDetailProps {
  item: InventoryItem;
  modalHeaderTextColor: string;
}

/**
 * Finished Product detail component that shows detailed information about finished products
 * including batch information, quality status, costs, and customer details with dynamic table headers
 */
export const FinishedProductDetail: React.FC<FinishedProductDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  const { getFinishedProductBatches } = useInventory();
  const [productTemplate, setProductTemplate] = useState<ProductTypeTemplate | null>(null);
  const [batches, setBatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [batchesLoading, setBatchesLoading] = useState(true);

  // Get product type from additional fields or fallback to default
  const productType = item.additionalFields?.product_type || 'custom_structure';
  const qualityStatus = item.additionalFields?.quality_status || 'En Proceso';

  // Load product template based on product type
  useEffect(() => {
    const loadTemplate = async () => {
      setLoading(true);
      try {
        const template = await fetchProductTypeTemplate(productType);
        setProductTemplate(template);
      } catch (error) {
        console.error('Failed to load product template:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [productType]);

  // Load batch data for this specific item
  useEffect(() => {
    const loadBatches = async () => {
      setBatchesLoading(true);
      try {
        const batchData = await getFinishedProductBatches(item.id);
        setBatches(batchData);
      } catch (error) {
        console.error('Failed to load batches:', error);
      } finally {
        setBatchesLoading(false);
      }
    };

    loadBatches();
  }, [item.id, getFinishedProductBatches]);

  if (loading || batchesLoading || !productTemplate) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
        Cargando detalles del producto...
      </div>
    );
  }

  // Helper function to format cell value based on column type
  const formatCellValue = (value: any, columnType: string) => {
    // Handle null/undefined values
    if (value === null || value === undefined) {
      return 'N/A';
    }

    switch (columnType) {
      case 'currency':
        return typeof value === 'number' ? `$${value.toLocaleString()}` : 'N/A';
      case 'date':
        try {
          return new Date(value).toLocaleDateString('es-ES');
        } catch {
          return 'N/A';
        }
      case 'status':
        // Add status styling
        const statusColors: Record<string, string> = {
          'Aprobado': 'text-green-600 bg-green-50',
          'En Revisión': 'text-yellow-600 bg-yellow-50',
          'En Pruebas': 'text-blue-600 bg-blue-50',
          'Rechazado': 'text-red-600 bg-red-50',
          'En Proceso': 'text-gray-600 bg-gray-50'
        };
        const colorClass = statusColors[value] || 'text-gray-600 bg-gray-50';
        return (
          <span className={`px-2 py-1 rounded text-xs font-medium ${colorClass}`}>
            {value}
          </span>
        );
      case 'number':
        return typeof value === 'number' ? value.toString() : value?.toString() || 'N/A';
      default:
        return value?.toString() || 'N/A';
    }
  };

  return (
    <div className="space-y-[var(--spacing-24)]">
      {/* Product Header */}
      <div className="space-y-[var(--spacing-8)]">
        <h3 className={`text-[24px] font-bold ${modalHeaderTextColor}`}>
          {item.name}
        </h3>
        <p className="modal-sub-header">
          {qualityStatus} - {productTemplate.display_name}
        </p>
      </div>

      {/* Finished Product Batches Table */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Lotes de Producción
        </h4>
        {batches.length > 0 ? (
        <Table>
          <TableHeader>
            <TableHeaderRow>
              {productTemplate.columns.map((column, index) => (
                <TableHeaderCell 
                  key={column.id}
                  variant={
                    index === 0 ? 'first' : 
                    index === productTemplate.columns.length - 1 ? 'last' : 
                    'middle'
                  }
                >
                  {column.header}
                </TableHeaderCell>
              ))}
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {batches.map((batch, batchIndex) => {
              const totalRows = batches.length;
              const rowPosition = batchIndex === 0 ? 'first' : batchIndex === totalRows - 1 ? 'last' : 'middle';
              return (
                <TableBodyRow key={batch.id}>
                  {productTemplate.columns.map((column, colIndex) => (
                    <TableBodyCell 
                      key={column.id}
                      variant={
                        colIndex === 0 ? 'first' : 
                        colIndex === productTemplate.columns.length - 1 ? 'last' : 
                        'middle'
                      }
                      rowPosition={rowPosition}
                    >
                      {formatCellValue(batch[column.id as keyof typeof batch], column.type)}
                    </TableBodyCell>
                  ))}
                </TableBodyRow>
              );
            })}
          </TableBody>
        </Table>
        ) : (
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            No hay lotes disponibles para este producto
          </div>
        )}
      </div>

      {/* Product Summary */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Resumen del Producto
        </h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Stock Total:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.quantity} {item.unit}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Estado de Calidad:</span>
              <span className="font-bold text-green-600">{qualityStatus}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Ubicación:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.location || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Última Actualización:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{new Date(item.lastUpdated).toLocaleDateString('es-ES')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Information (if available) */}
      {item.additionalFields?.customer && (
        <div className="modal-section">
          <h4 className="modal-sub-header">
            Información del Cliente
          </h4>
          <div className="p-4 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-[var(--color-text-secondary)]">Cliente:</span>
                <p className="font-bold text-[var(--color-text-primary)]">{item.additionalFields.customer}</p>
              </div>
              {item.additionalFields.delivery_date && (
                <div>
                  <span className="text-sm text-[var(--color-text-secondary)]">Fecha de Entrega:</span>
                  <p className="font-bold text-[var(--color-text-primary)]">
                    {new Date(item.additionalFields.delivery_date).toLocaleDateString('es-ES')}
                  </p>
                </div>
              )}
              {item.additionalFields.warranty_period && (
                <div>
                  <span className="text-sm text-[var(--color-text-secondary)]">Garantía:</span>
                  <p className="font-bold text-[var(--color-text-primary)]">{item.additionalFields.warranty_period}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
