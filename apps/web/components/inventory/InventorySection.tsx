"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { SearchBarSet, FilterDropdown, FilterPills, InventoryCard, type FilterOption, FadeIn } from "@admin/ui";
import { useInventory, useUser } from "../../contexts";
import { InventoryTabButton } from "./InventoryTabButton";
import { RawMaterialDetail } from "./RawMaterialDetail";
import { FinishedProductDetail } from "./FinishedProductDetail";
import { ConsumableDetail } from "./ConsumableDetail";
import { EquipmentDetail } from "./EquipmentDetail";
import { InventoryItem } from "../../contexts/types";

interface InventorySectionProps {
  isModalExpanded: boolean;
  setIsModalExpanded: (expanded: boolean) => void;
  modalHeaderTextColor: string;
}

export function InventorySection({ isModalExpanded, setIsModalExpanded, modalHeaderTextColor }: InventorySectionProps) {
  const {
    items: inventoryItems,
    categories,
    categoriesLoading,
    loading: inventoryLoading,
    getItemsByCategory,
    loadCategoriesForBusinessType
  } = useInventory();

  const { user } = useUser();
  const [activeTab, setActiveTab] = useState<string>('');
  const [selectedFilters, setSelectedFilters] = useState<FilterOption[]>([]);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

  // Helper function for smooth expansion
  const handleSmoothExpansion = (item: InventoryItem) => {
    setSelectedItem(item);
    // Small delay for smooth transition
    setTimeout(() => {
      setIsModalExpanded(true);
    }, 150);
  };

  // Load categories when business type changes
  useEffect(() => {
    if (user?.businessType && categories.length === 0) {
      loadCategoriesForBusinessType(user.businessType);
    }
  }, [user?.businessType, loadCategoriesForBusinessType, categories.length]);

  // Set first category as active when categories load
  useEffect(() => {
    if (categories.length > 0 && !activeTab && categories[0]) {
      setActiveTab(categories[0].id);
    }
  }, [categories, activeTab]);

  // Get items for active category (memoized to prevent unnecessary re-computation)
  const activeItems = useMemo(() => {
    return activeTab ? getItemsByCategory(activeTab) : [];
  }, [activeTab, getItemsByCategory]);

  // Inventory-specific filter options
  const inventoryFilterOptions: FilterOption[] = [
    { id: 'low_stock', label: 'Stock Bajo', value: 'low_stock' },
    { id: 'out_of_stock', label: 'Sin Stock', value: 'out_of_stock' },
    { id: 'overstocked', label: 'Sobrestock', value: 'overstocked' },
    { id: 'expired', label: 'Vencidos', value: 'expired' },
    { id: 'needs_reorder', label: 'Requiere Pedido', value: 'needs_reorder' },
  ];

  if (categoriesLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-[var(--color-text-secondary)]">Cargando categorías...</div>
      </div>
    );
  }

  return (
    <div className={isModalExpanded ? "flex gap-12 h-full overflow-hidden" : "flex flex-col h-full overflow-hidden"}>
      {/* Left side - Inventory Grid */}
      <div className={isModalExpanded ? "w-[450px] flex-shrink-0 overflow-y-auto" : "flex flex-col h-full overflow-hidden"}>
        {/* Category Tabs */}
        {categories.length > 0 && (
          <div className="grid grid-cols-3 gap-2 mb-[var(--spacing-8)] flex-shrink-0">
            {categories.map((category) => (
              <InventoryTabButton
                key={category.id}
                label={category.display_name}
                isActive={activeTab === category.id}
                onClick={() => setActiveTab(category.id)}
              />
            ))}
          </div>
        )}

        {/* Search and Filter Controls */}
        <div className="mb-[var(--spacing-16)] flex-shrink-0">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <SearchBarSet
                placeholder="Buscar inventario"
                exampleText="Lámina de acero"
                showExample={false}
                onSearchChange={(value: string) => console.log('Searching inventory:', value)}
              />
            </div>
            <div className="flex-shrink-0">
              <FilterDropdown
                options={inventoryFilterOptions}
                selectedOptions={selectedFilters}
                onOptionsChange={setSelectedFilters}
                onFilterClick={() => console.log('Filter clicked')}
              />
            </div>
          </div>

          {/* Selected Filter Pills */}
          <FilterPills
            selectedOptions={selectedFilters}
            onRemoveOption={(optionId) => {
              setSelectedFilters(prev => prev.filter(option => option.id !== optionId));
            }}
          />
        </div>

        {inventoryLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-[var(--color-text-secondary)]">Cargando inventario...</div>
          </div>
        ) : (
          /* Inventory Items Grid - Scrollable container */
          <div className={isModalExpanded ? "grid grid-cols-1 gap-3" : "flex-1 overflow-y-auto min-h-0"}>
            <div className={isModalExpanded ? "" : "grid grid-cols-1 gap-3"}>
              {activeItems.length > 0 ? (
                activeItems.map((item) => (
                  <InventoryCard
                    key={item.id}
                    name={item.name}
                    description={item.description}
                    quantity={item.quantity}
                    unit={item.unit}
                    materialType={item.additionalFields?.material_type}
                    onCardClick={() => {
                      console.log('Selected item:', item);
                      if (!isModalExpanded) {
                        handleSmoothExpansion(item);
                      } else {
                        setSelectedItem(item);
                      }
                    }}
                  />
                ))
              ) : (
                <div className="text-center py-8 text-[var(--color-text-secondary)]">
                  {activeTab ? 'No hay elementos en esta categoría' : 'Selecciona una categoría'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Right side - Item Details (only when expanded) */}
      {isModalExpanded && (
        <div className="flex-1 border rounded-[8px] bg-[var(--color-background-primary)] overflow-hidden flex flex-col min-h-0">
          <div className="flex-1 px-16 pt-20 pb-16 overflow-y-auto min-h-0">
            <FadeIn key={selectedItem?.id || 'no-item'} show={true} duration="fast" easing="smooth">
              {selectedItem ? (
                selectedItem.categoryId === 'raw_materials' ? (
                  <RawMaterialDetail
                    item={selectedItem}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                ) : selectedItem.categoryId === 'finished_products' ? (
                  <FinishedProductDetail
                    item={selectedItem}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                ) : selectedItem.categoryId === 'consumables' ? (
                  <ConsumableDetail
                    item={selectedItem}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                ) : selectedItem.categoryId === 'tools_equipment' ? (
                  <EquipmentDetail
                    item={selectedItem}
                    modalHeaderTextColor={modalHeaderTextColor}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                    Detalles para {selectedItem.category} próximamente
                  </div>
                )
              ) : (
                <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
                  Selecciona un elemento del inventario para ver los detalles
                </div>
              )}
            </FadeIn>
          </div>
        </div>
      )}
    </div>
  );
}
