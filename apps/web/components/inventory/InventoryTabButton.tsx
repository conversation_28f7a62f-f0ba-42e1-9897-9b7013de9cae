"use client";

import * as React from "react";

interface InventoryTabButtonProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
}

export function InventoryTabButton({ label, isActive, onClick }: InventoryTabButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`
        w-full px-4 py-2 text-sm font-medium rounded-[var(--radius-8)] border transition-button hover-scale
        flex items-center justify-center text-center
        ${isActive
          ? 'bg-[var(--color-frame-primary)] text-[var(--color-text-inverse)] border-[var(--color-stroke)]'
          : 'bg-[var(--color-background-primary)] text-[var(--color-text-secondary)] border-[var(--color-stroke)] hover:bg-[var(--color-background-secondary)]'
        }
      `}
    >
      {label}
    </button>
  );
}
