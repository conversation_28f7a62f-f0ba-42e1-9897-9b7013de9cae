"use client";

import React, { useState, useEffect } from 'react';
import { InventoryItem } from '../../contexts/types';
import { useInventory } from '../../contexts';
import { fetchMaterialTypeTemplate, type MaterialTypeTemplate } from '../../services/rawMaterialTemplates';
import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
} from '@admin/ui';

interface RawMaterialDetailProps {
  item: InventoryItem;
  modalHeaderTextColor: string;
}

/**
 * Raw Material detail component that shows detailed information about raw materials
 * including batch information, dimensions, costs, and supplier details with dynamic table headers
 */
export const RawMaterialDetail: React.FC<RawMaterialDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  const { getRawMaterialBatches } = useInventory();
  const [materialTemplate, setMaterialTemplate] = useState<MaterialTypeTemplate | null>(null);
  const [batches, setBatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [batchesLoading, setBatchesLoading] = useState(true);

  // Get material type from additional fields or fallback to default
  const materialType = item.additionalFields?.material_type || 'metal_sheet';
  const materialGrade = item.additionalFields?.material_grade || 'Material';

  // Load material template based on material type
  useEffect(() => {
    const loadTemplate = async () => {
      setLoading(true);
      try {
        const template = await fetchMaterialTypeTemplate(materialType);
        setMaterialTemplate(template);
      } catch (error) {
        console.error('Failed to load material template:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [materialType]);

  // Load batch data for this specific item
  useEffect(() => {
    const loadBatches = async () => {
      setBatchesLoading(true);
      try {
        const batchData = await getRawMaterialBatches(item.id);
        setBatches(batchData);
      } catch (error) {
        console.error('Failed to load batches:', error);
      } finally {
        setBatchesLoading(false);
      }
    };

    loadBatches();
  }, [item.id, getRawMaterialBatches]);

  if (loading || batchesLoading || !materialTemplate) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
        Cargando detalles del material...
      </div>
    );
  }

  // Helper function to format cell value based on column type
  const formatCellValue = (value: any, columnType: string) => {
    // Handle null/undefined values
    if (value === null || value === undefined) {
      return 'N/A';
    }

    switch (columnType) {
      case 'currency':
        return typeof value === 'number' ? `$${value.toLocaleString()}` : 'N/A';
      case 'date':
        try {
          return new Date(value).toLocaleDateString('es-ES');
        } catch {
          return 'N/A';
        }
      case 'number':
        return typeof value === 'number' ? value.toString() : value?.toString() || 'N/A';
      default:
        return value?.toString() || 'N/A';
    }
  };

  return (
    <div className="space-y-[var(--spacing-24)]">
      {/* Material Header */}
      <div className="space-y-[var(--spacing-8)]">
        <h3 className={`text-[24px] font-bold ${modalHeaderTextColor}`}>
          {item.name}
        </h3>
        <p className="modal-sub-header">
          {materialGrade} - {materialTemplate.display_name}
        </p>
      </div>

      {/* Raw Material Batches Table */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Lotes de Material
        </h4>
        {batches.length > 0 ? (
        <Table>
          <TableHeader>
            <TableHeaderRow>
              {materialTemplate.columns.map((column, index) => (
                <TableHeaderCell
                  key={column.id}
                  variant={
                    index === 0 ? 'first' :
                    index === materialTemplate.columns.length - 1 ? 'last' :
                    'middle'
                  }
                >
                  {column.header}
                </TableHeaderCell>
              ))}
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {batches.map((batch, batchIndex) => {
              const totalRows = batches.length;
              const rowPosition = batchIndex === 0 ? 'first' : batchIndex === totalRows - 1 ? 'last' : 'middle';
              return (
                <TableBodyRow key={batch.id}>
                  {materialTemplate.columns.map((column, colIndex) => (
                    <TableBodyCell
                      key={column.id}
                      variant={
                        colIndex === 0 ? 'first' :
                        colIndex === materialTemplate.columns.length - 1 ? 'last' :
                        'middle'
                      }
                      rowPosition={rowPosition}
                    >
                      {formatCellValue(batch[column.id as keyof typeof batch], column.type)}
                    </TableBodyCell>
                  ))}
                </TableBodyRow>
              );
            })}
          </TableBody>
        </Table>
        ) : (
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            No hay lotes disponibles para este material
          </div>
        )}
      </div>

      {/* Material Summary */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Resumen del Material
        </h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Stock Total:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.quantity} {item.unit}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Stock Mínimo:</span>
              <span className="font-bold text-orange-600">{item.minStock || 'N/A'} {item.unit}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Ubicación:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.location || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Última Actualización:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{new Date(item.lastUpdated).toLocaleDateString('es-ES')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
