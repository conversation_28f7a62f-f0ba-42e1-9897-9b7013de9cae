# Admin Backend

This is the backend application for the Admin project, built with Next.js 15, Prisma, and PostgreSQL.

## Features

- **Next.js 15 with App Router**: Modern React framework with the latest app directory structure
- **Prisma ORM**: Type-safe database access with automatic migrations
- **PostgreSQL**: Robust relational database
- **TypeScript**: Full type safety across the application
- **Zod Validation**: Runtime type checking for API endpoints
- **RESTful API**: Complete CRUD operations for all entities

## Getting Started

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- pnpm (recommended) or npm

### Installation

1. **Install dependencies** (from the monorepo root):
   ```bash
   pnpm install
   ```

2. **Set up environment variables**:
   ```bash
   cp apps/backend/.env.example apps/backend/.env
   ```
   
   Update the `.env` file with your database connection string:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/admin_db?schema=public"
   ```

3. **Set up the database**:
   ```bash
   cd apps/backend
   pnpm db:generate  # Generate Prisma client
   pnpm db:push      # Push schema to database
   pnpm db:seed      # Seed with sample data
   ```

4. **Start the development server**:
   ```bash
   pnpm dev
   ```

The backend will be available at `http://localhost:3001`.

## API Endpoints

### Health Check
- `GET /api/health` - Check if the API is running

### Users
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/users/[id]` - Get a specific user
- `PUT /api/users/[id]` - Update a user
- `DELETE /api/users/[id]` - Delete a user

### Team Members
- `GET /api/team?userId={userId}` - Get team members for a user
- `POST /api/team` - Create a new team member
- `GET /api/team/[id]` - Get a specific team member
- `PUT /api/team/[id]` - Update a team member
- `DELETE /api/team/[id]` - Delete a team member

### Projects
- `GET /api/projects?userId={userId}` - Get projects for a user
- `POST /api/projects` - Create a new project
- `GET /api/projects/[id]` - Get a specific project
- `PUT /api/projects/[id]` - Update a project
- `DELETE /api/projects/[id]` - Delete a project

### Catalog
- `GET /api/catalog?userId={userId}&type={Product|Service}` - Get catalog items
- `POST /api/catalog` - Create a new catalog item
- `GET /api/catalog/[id]` - Get a specific catalog item
- `PUT /api/catalog/[id]` - Update a catalog item
- `DELETE /api/catalog/[id]` - Delete a catalog item

### Inventory
- `GET /api/inventory?userId={userId}&category={category}&lowStock={true|false}` - Get inventory items
- `POST /api/inventory` - Create a new inventory item
- `GET /api/inventory/[id]` - Get a specific inventory item
- `PUT /api/inventory/[id]` - Update an inventory item
- `DELETE /api/inventory/[id]` - Delete an inventory item

### Finance
- `GET /api/finance?userId={userId}&type={records|movements|forecasts|all}` - Get financial data
- `POST /api/finance` - Create financial records or movements

### Context Data
- `GET /api/context?userId={userId}&editType={add_team_member|add_product|add_service|add_material|add_task}&filter={category}` - Get context data for project editing

## Database Schema

The database includes the following main entities:

- **User**: Business owner/admin user
- **TeamMember**: Employees and contractors
- **Project**: Business projects with modular composition
- **CatalogItem**: Products and services offered
- **InventoryItem**: Materials and supplies
- **FinancialRecord**: Income and expense records
- **FinancialMovement**: Detailed financial transactions
- **ProjectTask**: Tasks within projects
- **ForecastEntry**: Financial forecasting data

## Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm check-types` - Type check with TypeScript
- `pnpm db:generate` - Generate Prisma client
- `pnpm db:push` - Push schema changes to database
- `pnpm db:migrate` - Create and run migrations
- `pnpm db:studio` - Open Prisma Studio
- `pnpm db:seed` - Seed database with sample data

## Frontend Integration

The backend is designed to replace the mock data in the frontend. Use the provided API client:

```typescript
import apiService from '../services/apiClient';

// Get team members
const teamMembers = await apiService.teamMembers.getAll(userId);

// Create a new project
const project = await apiService.projects.create(projectData);
```

Or use the backend data store that maintains the same interface:

```typescript
import { backendDataStore } from '../services/backendDataStore';

// Set the current user
backendDataStore.setCurrentUserId(userId);

// Use the same methods as the mock data store
const teamMembers = await backendDataStore.getTeamMembers();
const projects = await backendDataStore.getProjects();
```

## Development

### Adding New Endpoints

1. Create the route file in `app/api/[endpoint]/route.ts`
2. Add validation schemas using Zod
3. Implement CRUD operations using Prisma
4. Update the API client in the frontend
5. Add TypeScript types if needed

### Database Changes

1. Update the Prisma schema in `prisma/schema.prisma`
2. Generate a migration: `pnpm db:migrate`
3. Update the seed file if needed
4. Update TypeScript types in the UI package

### Testing

Use tools like Postman or curl to test the API endpoints:

```bash
# Health check
curl http://localhost:3001/api/health

# Get team members
curl "http://localhost:3001/api/team?userId=your-user-id"

# Create a team member
curl -X POST http://localhost:3001/api/team \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","role":"Developer","email":"<EMAIL>","userId":"your-user-id","salary":75000}'
```

## Production Deployment

1. Set up a PostgreSQL database
2. Set the `DATABASE_URL` environment variable
3. Run migrations: `pnpm db:migrate`
4. Build the application: `pnpm build`
5. Start the server: `pnpm start`

## Troubleshooting

### Database Connection Issues
- Verify your `DATABASE_URL` is correct
- Ensure PostgreSQL is running
- Check firewall settings

### Migration Issues
- Reset the database: `pnpm db:push --force-reset`
- Re-run migrations: `pnpm db:migrate`

### Type Errors
- Regenerate Prisma client: `pnpm db:generate`
- Check TypeScript configuration
- Ensure all dependencies are installed
