import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { z } from 'zod';

// Validation schema for catalog item updates
const updateCatalogItemSchema = z.object({
  productName: z.string().min(1).optional(),
  productDescription: z.string().min(1).optional(),
  categoryLabel: z.enum(['Product', 'Service']).optional(),
  imageSrc: z.string().optional(),
  details: z.object({
    title: z.string().min(1).optional(),
    type: z.enum(['Product', 'Service']).optional(),
    fabrication: z.string().optional(),
    duration: z.string().optional(),
    requirements: z.array(z.string()).optional(),
    serviceType: z.string().optional(),
    serviceSpecifications: z.record(z.any()).optional(),
    images: z.array(z.string()).optional(),
  }).optional(),
});

// GET /api/catalog/[id] - Get a specific catalog item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const catalogItem = await prisma.catalogItem.findUnique({
      where: { id: params.id },
      include: {
        details: {
          include: {
            materials: true,
            deliverables: true,
            documents: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            businessType: true,
            companyName: true,
          },
        },
      },
    });

    if (!catalogItem) {
      return NextResponse.json(
        { error: 'Catalog item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(catalogItem);
  } catch (error) {
    console.error('Error fetching catalog item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch catalog item' },
      { status: 500 }
    );
  }
}

// PUT /api/catalog/[id] - Update a catalog item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { details, ...catalogItemData } = updateCatalogItemSchema.parse(body);

    const catalogItem = await prisma.$transaction(async (tx) => {
      // Update basic catalog item data
      const updatedItem = await tx.catalogItem.update({
        where: { id: params.id },
        data: catalogItemData,
      });

      // Update details if provided
      if (details) {
        await tx.catalogItemDetails.upsert({
          where: { catalogItemId: params.id },
          update: details,
          create: {
            catalogItemId: params.id,
            ...details,
          },
        });
      }

      // Return the updated item with all relations
      return await tx.catalogItem.findUnique({
        where: { id: params.id },
        include: {
          details: {
            include: {
              materials: true,
              deliverables: true,
              documents: true,
            },
          },
        },
      });
    });

    return NextResponse.json(catalogItem);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating catalog item:', error);
    return NextResponse.json(
      { error: 'Failed to update catalog item' },
      { status: 500 }
    );
  }
}

// DELETE /api/catalog/[id] - Delete a catalog item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.catalogItem.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Catalog item deleted successfully' });
  } catch (error) {
    console.error('Error deleting catalog item:', error);
    return NextResponse.json(
      { error: 'Failed to delete catalog item' },
      { status: 500 }
    );
  }
}
