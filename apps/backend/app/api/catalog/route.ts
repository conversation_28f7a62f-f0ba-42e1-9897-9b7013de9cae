import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { z } from 'zod';

// Validation schemas
const materialSchema = z.object({
  name: z.string().min(1),
  material: z.string().min(1),
  quantity: z.number().positive(),
  unit: z.string().min(1),
});

const deliverableSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
  timeline: z.string().min(1),
});

const documentSchema = z.object({
  name: z.string().min(1),
  url: z.string().url(),
  type: z.string().min(1),
});

const catalogItemDetailsSchema = z.object({
  title: z.string().min(1),
  type: z.enum(['Product', 'Service']),
  fabrication: z.string().optional(),
  duration: z.string().optional(),
  requirements: z.array(z.string()).default([]),
  serviceType: z.string().optional(),
  serviceSpecifications: z.record(z.any()).optional(),
  images: z.array(z.string()).default([]),
  materials: z.array(materialSchema).default([]),
  deliverables: z.array(deliverableSchema).default([]),
  documents: z.array(documentSchema).default([]),
});

const createCatalogItemSchema = z.object({
  productName: z.string().min(1, 'Product name is required'),
  productDescription: z.string().min(1, 'Product description is required'),
  categoryLabel: z.enum(['Product', 'Service']),
  imageSrc: z.string().optional(),
  userId: z.string().min(1, 'User ID is required'),
  details: catalogItemDetailsSchema.optional(),
});

// GET /api/catalog - Get all catalog items
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const type = searchParams.get('type') as 'Product' | 'Service' | null;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const where: any = { userId };
    if (type) {
      where.categoryLabel = type;
    }

    const catalogItems = await prisma.catalogItem.findMany({
      where,
      include: {
        details: {
          include: {
            materials: true,
            deliverables: true,
            documents: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(catalogItems);
  } catch (error) {
    console.error('Error fetching catalog items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch catalog items' },
      { status: 500 }
    );
  }
}

// POST /api/catalog - Create a new catalog item
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { details, ...catalogItemData } = createCatalogItemSchema.parse(body);

    const catalogItem = await prisma.catalogItem.create({
      data: {
        ...catalogItemData,
        details: details ? {
          create: {
            ...details,
            materials: {
              create: details.materials,
            },
            deliverables: {
              create: details.deliverables,
            },
            documents: {
              create: details.documents,
            },
          },
        } : undefined,
      },
      include: {
        details: {
          include: {
            materials: true,
            deliverables: true,
            documents: true,
          },
        },
      },
    });

    return NextResponse.json(catalogItem, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating catalog item:', error);
    return NextResponse.json(
      { error: 'Failed to create catalog item' },
      { status: 500 }
    );
  }
}
