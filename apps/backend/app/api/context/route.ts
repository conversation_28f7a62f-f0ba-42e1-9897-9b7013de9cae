import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// Interface for selectable items (matching frontend)
interface SelectableItem {
  id: string;
  name: string;
  description?: string;
  type: 'team_member' | 'catalog_item' | 'inventory_item' | 'task_template';
  category?: string;
  metadata?: Record<string, any>;
  isSelected?: boolean;
  quantity?: number;
}

interface ContextDataResult {
  items: SelectableItem[];
  totalCount: number;
  categories?: string[];
  hasMore?: boolean;
}

// GET /api/context - Get context data for project editing
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const editType = searchParams.get('editType');
    const filter = searchParams.get('filter');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!editType) {
      return NextResponse.json(
        { error: 'Edit type is required' },
        { status: 400 }
      );
    }

    let result: ContextDataResult;

    switch (editType) {
      case 'add_team_member':
        result = await fetchTeamMembers(userId);
        break;
      
      case 'add_product':
        result = await fetchCatalogItems(userId, 'Product');
        break;
      
      case 'add_service':
        result = await fetchCatalogItems(userId, 'Service');
        break;
      
      case 'add_material':
        result = await fetchInventoryItems(userId, filter);
        break;
      
      case 'add_task':
        result = await fetchTaskTemplates();
        break;
      
      default:
        result = { items: [], totalCount: 0 };
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching context data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch context data' },
      { status: 500 }
    );
  }
}

// Fetch team members from database
async function fetchTeamMembers(userId: string): Promise<ContextDataResult> {
  const teamMembers = await prisma.teamMember.findMany({
    where: { userId, status: 'active' },
    orderBy: { name: 'asc' },
  });

  const items: SelectableItem[] = teamMembers.map((member) => ({
    id: member.id,
    name: member.name,
    description: `${member.role} - ${member.email}`,
    type: 'team_member' as const,
    category: member.role,
    metadata: {
      role: member.role,
      email: member.email,
      status: member.status,
      hourlyRate: member.hourlyRate,
      skills: member.skills || [],
      avatar: member.avatar,
    },
    isSelected: false,
  }));

  return {
    items,
    totalCount: items.length,
    categories: [...new Set(teamMembers.map(m => m.role))],
  };
}

// Fetch catalog items from database
async function fetchCatalogItems(userId: string, type?: 'Product' | 'Service'): Promise<ContextDataResult> {
  const where: any = { userId };
  if (type) {
    where.categoryLabel = type;
  }

  const catalogItems = await prisma.catalogItem.findMany({
    where,
    include: {
      details: true,
    },
    orderBy: { productName: 'asc' },
  });

  const items: SelectableItem[] = catalogItems.map((item) => ({
    id: item.id,
    name: item.productName,
    description: item.productDescription,
    type: 'catalog_item' as const,
    category: item.categoryLabel,
    metadata: {
      categoryLabel: item.categoryLabel,
      imageSrc: item.imageSrc,
      details: item.details,
    },
    isSelected: false,
    quantity: 1,
  }));

  return {
    items,
    totalCount: items.length,
    categories: ['Product', 'Service'],
  };
}

// Fetch inventory items from database
async function fetchInventoryItems(userId: string, categoryFilter?: string | null): Promise<ContextDataResult> {
  const where: any = { userId };
  if (categoryFilter) {
    where.category = categoryFilter;
  }

  const inventoryItems = await prisma.inventoryItem.findMany({
    where,
    orderBy: { name: 'asc' },
  });

  const items: SelectableItem[] = inventoryItems.map((item) => ({
    id: item.id,
    name: item.name,
    description: `${item.description} - ${item.quantity} ${item.unit} available`,
    type: 'inventory_item' as const,
    category: item.category,
    metadata: {
      currentQuantity: item.quantity,
      unit: item.unit,
      cost: item.cost,
      location: item.location,
      supplier: item.supplier,
      categoryId: item.categoryId,
      additionalFields: item.additionalFields,
    },
    isSelected: false,
    quantity: 1,
  }));

  return {
    items,
    totalCount: items.length,
    categories: [...new Set(inventoryItems.map(item => item.category))],
  };
}

// Fetch task templates (mock data for now)
async function fetchTaskTemplates(): Promise<ContextDataResult> {
  // This would typically come from a task templates table
  // For now, returning some common task templates
  const taskTemplates = [
    { id: 'task-1', name: 'Planning Phase', category: 'Planning' },
    { id: 'task-2', name: 'Design Review', category: 'Design' },
    { id: 'task-3', name: 'Material Procurement', category: 'Procurement' },
    { id: 'task-4', name: 'Fabrication', category: 'Production' },
    { id: 'task-5', name: 'Quality Control', category: 'Quality' },
    { id: 'task-6', name: 'Installation', category: 'Installation' },
    { id: 'task-7', name: 'Testing', category: 'Testing' },
    { id: 'task-8', name: 'Documentation', category: 'Documentation' },
  ];

  const items: SelectableItem[] = taskTemplates.map((template) => ({
    id: template.id,
    name: template.name,
    description: `Standard ${template.name.toLowerCase()} task`,
    type: 'task_template' as const,
    category: template.category,
    metadata: {
      isTemplate: true,
      category: template.category,
    },
    isSelected: false,
  }));

  return {
    items,
    totalCount: items.length,
    categories: [...new Set(taskTemplates.map(t => t.category))],
  };
}
