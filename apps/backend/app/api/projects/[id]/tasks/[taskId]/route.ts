import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '../../../../../../lib/prisma';

// Validation schema for task updates
const updateTaskSchema = z.object({
  name: z.string().min(1).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  assignedToId: z.string().optional(),
  completed: z.boolean().optional(),
});

// GET /api/projects/[id]/tasks/[taskId] - Get a specific task
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; taskId: string } }
) {
  try {
    const task = await prisma.projectTask.findFirst({
      where: {
        id: params.taskId,
        projectId: params.id,
      },
      include: {
        assignedTo: {
          include: {
            teamMember: {
              select: {
                id: true,
                name: true,
                role: true,
                avatar: true,
              },
            },
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    return NextResponse.json(
      { error: 'Failed to fetch task' },
      { status: 500 }
    );
  }
}

// PUT /api/projects/[id]/tasks/[taskId] - Update a task
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; taskId: string } }
) {
  try {
    const body = await request.json();
    const { assignedTo, ...taskData } = updateTaskSchema.parse(body);

    // Verify task exists and belongs to project
    const existingTask = await prisma.projectTask.findFirst({
      where: {
        id: params.taskId,
        projectId: params.id,
      },
    });

    if (!existingTask) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // If assignedTo is provided, verify team members are part of the project
    if (assignedTo && assignedTo.length > 0) {
      const projectTeamMembers = await prisma.projectTeamMember.findMany({
        where: {
          projectId: params.id,
          teamMemberId: { in: assignedTo },
        },
      });

      if (projectTeamMembers.length !== assignedTo.length) {
        return NextResponse.json(
          { error: 'Some assigned team members are not part of this project' },
          { status: 400 }
        );
      }
    }

    // Update task with assignments in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the task
      const task = await tx.projectTask.update({
        where: { id: params.taskId },
        data: taskData,
      });

      // Update assignments if provided
      if (assignedTo !== undefined) {
        // Remove existing assignments
        await tx.projectTaskAssignment.deleteMany({
          where: { taskId: params.taskId },
        });

        // Create new assignments
        if (assignedTo.length > 0) {
          await tx.projectTaskAssignment.createMany({
            data: assignedTo.map((teamMemberId) => ({
              taskId: params.taskId,
              teamMemberId,
            })),
          });
        }
      }

      // Return updated task with assignments
      return await tx.projectTask.findUnique({
        where: { id: params.taskId },
        include: {
          assignedTo: {
            include: {
              teamMember: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                  avatar: true,
                },
              },
            },
          },
        },
      });
    });

    return NextResponse.json(result);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating task:', error);
    return NextResponse.json(
      { error: 'Failed to update task' },
      { status: 500 }
    );
  }
}

// DELETE /api/projects/[id]/tasks/[taskId] - Delete a task
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; taskId: string } }
) {
  try {
    // Verify task exists and belongs to project
    const existingTask = await prisma.projectTask.findFirst({
      where: {
        id: params.taskId,
        projectId: params.id,
      },
    });

    if (!existingTask) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // Delete task (assignments will be deleted automatically due to cascade)
    await prisma.projectTask.delete({
      where: { id: params.taskId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting task:', error);
    return NextResponse.json(
      { error: 'Failed to delete task' },
      { status: 500 }
    );
  }
}
