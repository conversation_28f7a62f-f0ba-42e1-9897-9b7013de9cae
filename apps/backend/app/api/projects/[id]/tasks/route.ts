import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '../../../../../lib/prisma';

// Validation schema for task creation
const createTaskSchema = z.object({
  name: z.string().min(1, 'Task name is required'),
  startDate: z.number().min(1).max(31, 'Start date must be between 1-31'),
  endDate: z.number().min(1).max(31, 'End date must be between 1-31'),
  assignedTo: z.array(z.string()).optional().default([]), // Team member IDs
  completed: z.boolean().optional().default(false),
});

// Validation schema for task updates
const updateTaskSchema = z.object({
  name: z.string().min(1).optional(),
  startDate: z.number().min(1).max(31).optional(),
  endDate: z.number().min(1).max(31).optional(),
  assignedTo: z.array(z.string()).optional(),
  completed: z.boolean().optional(),
});

// GET /api/projects/[id]/tasks - Get all tasks for a project
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tasks = await prisma.projectTask.findMany({
      where: { projectId: params.id },
      include: {
        assignedTo: {
          include: {
            teamMember: {
              select: {
                id: true,
                name: true,
                role: true,
                avatar: true,
              },
            },
          },
        },
      },
      orderBy: { startDate: 'asc' },
    });

    return NextResponse.json(tasks);
  } catch (error) {
    console.error('Error fetching project tasks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project tasks' },
      { status: 500 }
    );
  }
}

// POST /api/projects/[id]/tasks - Create a new task for a project
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { assignedTo, ...taskData } = createTaskSchema.parse(body);

    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: params.id },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Verify all assigned team members exist and are part of the project
    if (assignedTo.length > 0) {
      const projectTeamMembers = await prisma.projectTeamMember.findMany({
        where: {
          projectId: params.id,
          teamMemberId: { in: assignedTo },
        },
      });

      if (projectTeamMembers.length !== assignedTo.length) {
        return NextResponse.json(
          { error: 'Some assigned team members are not part of this project' },
          { status: 400 }
        );
      }
    }

    // Create task with assignments in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the task
      const task = await tx.projectTask.create({
        data: {
          ...taskData,
          projectId: params.id,
        },
      });

      // Create task assignments
      if (assignedTo.length > 0) {
        await tx.projectTaskAssignment.createMany({
          data: assignedTo.map((teamMemberId) => ({
            taskId: task.id,
            teamMemberId,
          })),
        });
      }

      // Return task with assignments
      return await tx.projectTask.findUnique({
        where: { id: task.id },
        include: {
          assignedTo: {
            include: {
              teamMember: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                  avatar: true,
                },
              },
            },
          },
        },
      });
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating task:', error);
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    );
  }
}
