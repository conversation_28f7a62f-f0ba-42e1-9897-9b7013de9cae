import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { z } from 'zod';

// Validation schema for project updates
const updateProjectSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  status: z.enum(['planning', 'in_progress', 'completed', 'on_hold']).optional(),
  startDate: z.string().transform((str) => new Date(str)).optional(),
  endDate: z.string().transform((str) => new Date(str)).optional(),
  modules: z.object({
    enabled: z.array(z.enum(['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'])).optional(),
    configuration: z.record(z.any()).optional(),
    aiRecommended: z.array(z.enum(['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'])).optional(),
    userOverrides: z.array(z.enum(['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'])).optional(),
  }).optional(),
  teamMemberIds: z.array(z.string()).optional(),
});

// GET /api/projects/[id] - Get a specific project
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const project = await prisma.project.findUnique({
      where: { id: params.id },
      include: {
        modules: true,
        teamMembers: {
          include: {
            teamMember: {
              select: {
                id: true,
                name: true,
                role: true,
                email: true,
                avatar: true,
                status: true,
                hourlyRate: true,
                skills: true,
              },
            },
          },
        },
        tasks: {
          include: {
            assignedTo: {
              include: {
                teamMember: {
                  select: {
                    id: true,
                    name: true,
                    role: true,
                  },
                },
              },
            },
          },
          orderBy: { startDate: 'asc' },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            businessType: true,
            companyName: true,
          },
        },
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(project);
  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project' },
      { status: 500 }
    );
  }
}

// PUT /api/projects/[id] - Update a project
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { teamMemberIds, modules, ...projectData } = updateProjectSchema.parse(body);

    // Start a transaction to handle complex updates
    const project = await prisma.$transaction(async (tx) => {
      // Update basic project data
      const updatedProject = await tx.project.update({
        where: { id: params.id },
        data: projectData,
      });

      // Update modules if provided
      if (modules) {
        await tx.projectModules.upsert({
          where: { projectId: params.id },
          update: modules,
          create: {
            projectId: params.id,
            ...modules,
          },
        });
      }

      // Update team members if provided
      if (teamMemberIds) {
        // Remove existing team members
        await tx.projectTeamMember.deleteMany({
          where: { projectId: params.id },
        });

        // Add new team members
        if (teamMemberIds.length > 0) {
          await tx.projectTeamMember.createMany({
            data: teamMemberIds.map((teamMemberId) => ({
              projectId: params.id,
              teamMemberId,
            })),
          });
        }
      }

      // Return the updated project with all relations
      return await tx.project.findUnique({
        where: { id: params.id },
        include: {
          modules: true,
          teamMembers: {
            include: {
              teamMember: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                  email: true,
                  avatar: true,
                },
              },
            },
          },
          tasks: true,
        },
      });
    });

    return NextResponse.json(project);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating project:', error);
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    );
  }
}

// DELETE /api/projects/[id] - Delete a project
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.project.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Project deleted successfully' });
  } catch (error) {
    console.error('Error deleting project:', error);
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    );
  }
}
