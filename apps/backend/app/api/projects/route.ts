import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { z } from 'zod';

// Validation schema for project creation
const createProjectSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  status: z.enum(['planning', 'in_progress', 'completed', 'on_hold']).default('planning'),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)).optional(),
  userId: z.string().min(1, 'User ID is required'),
  modules: z.object({
    enabled: z.array(z.enum(['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'])),
    configuration: z.record(z.any()).optional(),
    aiRecommended: z.array(z.enum(['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'])).optional(),
    userOverrides: z.array(z.enum(['finance', 'catalog', 'team', 'timeline', 'inventory', 'logistics'])).optional(),
  }).optional(),
  teamMemberIds: z.array(z.string()).default([]),
});

// GET /api/projects - Get all projects
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const projects = await prisma.project.findMany({
      where: { userId },
      include: {
        modules: true,
        teamMembers: {
          include: {
            teamMember: {
              select: {
                id: true,
                name: true,
                role: true,
                email: true,
                avatar: true,
              },
            },
          },
        },
        tasks: {
          include: {
            assignedTo: {
              include: {
                teamMember: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            tasks: true,
            teamMembers: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

// POST /api/projects - Create a new project
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { teamMemberIds, modules, ...projectData } = createProjectSchema.parse(body);

    const project = await prisma.project.create({
      data: {
        ...projectData,
        modules: modules ? {
          create: modules,
        } : undefined,
        teamMembers: teamMemberIds.length > 0 ? {
          create: teamMemberIds.map((teamMemberId) => ({
            teamMemberId,
          })),
        } : undefined,
      },
      include: {
        modules: true,
        teamMembers: {
          include: {
            teamMember: {
              select: {
                id: true,
                name: true,
                role: true,
                email: true,
                avatar: true,
              },
            },
          },
        },
        tasks: true,
      },
    });

    return NextResponse.json(project, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
