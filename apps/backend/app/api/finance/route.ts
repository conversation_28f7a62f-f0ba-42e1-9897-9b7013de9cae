import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { z } from 'zod';

// Validation schemas
const recurringPaymentDetailsSchema = z.object({
  monthlyAmount: z.number().positive(),
  totalMonths: z.number().int().positive(),
  remainingMonths: z.number().int().nonnegative(),
  interestRate: z.number().nonnegative().optional(),
  startDate: z.string().transform((str) => new Date(str)),
  nextDueDate: z.string().transform((str) => new Date(str)),
});

const createFinancialMovementSchema = z.object({
  fecha: z.string().transform((str) => new Date(str)),
  concepto: z.string().min(1, 'Concept is required'),
  monto: z.number().positive('Amount must be positive'),
  tipo: z.enum(['Entrada', 'Salida']),
  asignacion: z.string().optional(),
  categoria: z.string().min(1, 'Category is required'),
  comportamiento: z.string().optional(),
  comprobante: z.string().optional(),
  paymentMethod: z.enum(['cash', 'credit_card', 'loan', 'bank_transfer', 'check']).optional(),
  isRecurring: z.boolean().default(false),
  dueDate: z.string().transform((str) => new Date(str)).optional(),
  userId: z.string().min(1, 'User ID is required'),
  recurringDetails: recurringPaymentDetailsSchema.optional(),
});

const createFinancialRecordSchema = z.object({
  type: z.enum(['income', 'expense']),
  amount: z.number().positive('Amount must be positive'),
  description: z.string().min(1, 'Description is required'),
  date: z.string().transform((str) => new Date(str)),
  category: z.string().min(1, 'Category is required'),
  userId: z.string().min(1, 'User ID is required'),
});

// GET /api/finance - Get financial data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const type = searchParams.get('type'); // 'records', 'movements', 'forecasts', or 'all'

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const result: any = {};

    if (!type || type === 'all' || type === 'records') {
      result.records = await prisma.financialRecord.findMany({
        where: { userId },
        orderBy: { date: 'desc' },
      });
    }

    if (!type || type === 'all' || type === 'movements') {
      result.movements = await prisma.financialMovement.findMany({
        where: { userId },
        include: {
          recurringDetails: true,
        },
        orderBy: { fecha: 'desc' },
      });
    }

    if (!type || type === 'all' || type === 'forecasts') {
      result.forecasts = await prisma.forecastEntry.findMany({
        where: { userId },
        include: {
          recurringPaymentInfo: true,
        },
        orderBy: { date: 'asc' },
      });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching financial data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial data' },
      { status: 500 }
    );
  }
}

// POST /api/finance - Create financial records or movements
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type: recordType, ...data } = body;

    if (recordType === 'movement') {
      const { recurringDetails, ...movementData } = createFinancialMovementSchema.parse(data);

      const movement = await prisma.financialMovement.create({
        data: {
          ...movementData,
          recurringDetails: recurringDetails ? {
            create: recurringDetails,
          } : undefined,
        },
        include: {
          recurringDetails: true,
        },
      });

      // If it's a recurring payment, create forecast entries
      if (movement.isRecurring && recurringDetails) {
        const forecastEntries = [];
        const startDate = new Date(recurringDetails.startDate);
        
        for (let i = 1; i <= recurringDetails.totalMonths; i++) {
          const forecastDate = new Date(startDate);
          forecastDate.setMonth(forecastDate.getMonth() + i);
          
          forecastEntries.push({
            date: forecastDate,
            concepto: movement.concepto,
            monto: recurringDetails.monthlyAmount,
            tipo: movement.tipo,
            categoria: movement.categoria,
            sourceType: 'recurring_payment' as const,
            sourceId: movement.id,
            isConfirmed: false,
            userId: movement.userId,
            financialMovementId: movement.id,
            recurringPaymentInfo: {
              create: {
                monthNumber: i,
                totalMonths: recurringDetails.totalMonths,
                remainingMonths: recurringDetails.totalMonths - i,
              },
            },
          });
        }

        await prisma.forecastEntry.createMany({
          data: forecastEntries.map(({ recurringPaymentInfo, ...entry }) => entry),
        });
      }

      return NextResponse.json(movement, { status: 201 });
    } else {
      const validatedData = createFinancialRecordSchema.parse(data);
      const record = await prisma.financialRecord.create({
        data: validatedData,
      });

      return NextResponse.json(record, { status: 201 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating financial data:', error);
    return NextResponse.json(
      { error: 'Failed to create financial data' },
      { status: 500 }
    );
  }
}
