import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { z } from 'zod';

// Validation schemas for updates
const updateFinancialRecordSchema = z.object({
  type: z.enum(['income', 'expense']).optional(),
  amount: z.number().positive().optional(),
  description: z.string().min(1).optional(),
  date: z.string().transform((str) => new Date(str)).optional(),
  category: z.string().min(1).optional(),
});

const updateFinancialMovementSchema = z.object({
  fecha: z.string().transform((str) => new Date(str)).optional(),
  concepto: z.string().min(1).optional(),
  monto: z.number().positive().optional(),
  tipo: z.enum(['Entrada', 'Salida']).optional(),
  asignacion: z.string().optional(),
  categoria: z.string().min(1).optional(),
  comportamiento: z.string().optional(),
  comprobante: z.string().optional(),
  paymentMethod: z.enum(['cash', 'credit_card', 'loan', 'bank_transfer', 'check']).optional(),
  isRecurring: z.boolean().optional(),
  dueDate: z.string().transform((str) => new Date(str)).optional(),
});

// GET /api/finance/[id] - Get a specific financial record or movement
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'record' or 'movement'

    if (type === 'movement') {
      const movement = await prisma.financialMovement.findUnique({
        where: { id: params.id },
        include: {
          recurringDetails: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              businessType: true,
              companyName: true,
            },
          },
        },
      });

      if (!movement) {
        return NextResponse.json(
          { error: 'Financial movement not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(movement);
    } else {
      const record = await prisma.financialRecord.findUnique({
        where: { id: params.id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              businessType: true,
              companyName: true,
            },
          },
        },
      });

      if (!record) {
        return NextResponse.json(
          { error: 'Financial record not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(record);
    }
  } catch (error) {
    console.error('Error fetching financial data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial data' },
      { status: 500 }
    );
  }
}

// PUT /api/finance/[id] - Update a financial record or movement
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { type, ...data } = body;

    if (type === 'movement') {
      const validatedData = updateFinancialMovementSchema.parse(data);
      
      const movement = await prisma.financialMovement.update({
        where: { id: params.id },
        data: validatedData,
        include: {
          recurringDetails: true,
        },
      });

      return NextResponse.json(movement);
    } else {
      const validatedData = updateFinancialRecordSchema.parse(data);
      
      const record = await prisma.financialRecord.update({
        where: { id: params.id },
        data: validatedData,
      });

      return NextResponse.json(record);
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating financial data:', error);
    return NextResponse.json(
      { error: 'Failed to update financial data' },
      { status: 500 }
    );
  }
}

// DELETE /api/finance/[id] - Delete a financial record or movement
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'record' or 'movement'

    if (type === 'movement') {
      await prisma.financialMovement.delete({
        where: { id: params.id },
      });
    } else {
      await prisma.financialRecord.delete({
        where: { id: params.id },
      });
    }

    return NextResponse.json({ message: 'Financial data deleted successfully' });
  } catch (error) {
    console.error('Error deleting financial data:', error);
    return NextResponse.json(
      { error: 'Failed to delete financial data' },
      { status: 500 }
    );
  }
}
