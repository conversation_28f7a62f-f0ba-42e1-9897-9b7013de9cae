import { NextRequest, NextResponse } from 'next/server';
import { parseCommand } from '../../../../services/openai';

export async function POST(request: NextRequest) {
  try {
    const { command } = await request.json();

    if (!command || typeof command !== 'string') {
      return NextResponse.json(
        { error: 'Command is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await parseCommand(command);
    return NextResponse.json(result);

  } catch (error) {
    console.error('Error parsing command:', error);
    return NextResponse.json(
      { error: 'Failed to parse command' },
      { status: 500 }
    );
  }
}
