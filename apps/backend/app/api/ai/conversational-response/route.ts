import { NextRequest, NextResponse } from 'next/server';
import { generateConversationalResponse, ParsedIntent, EntityType } from '../../../../services/openai';

export async function POST(request: NextRequest) {
  try {
    const { command, intent, entityType, extractedData } = await request.json();

    if (!command || typeof command !== 'string') {
      return NextResponse.json(
        { error: 'Command is required and must be a string' },
        { status: 400 }
      );
    }

    if (!entityType || typeof entityType !== 'string') {
      return NextResponse.json(
        { error: 'EntityType is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await generateConversationalResponse(
      command,
      intent as ParsedIntent,
      entityType as EntityType,
      extractedData || {}
    );

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error generating conversational response:', error);
    return NextResponse.json(
      { error: 'Failed to generate conversational response' },
      { status: 500 }
    );
  }
}
