import { NextRequest, NextResponse } from 'next/server';
import { analyzeProjectAndRecommendModules } from '../../../../services/moduleRecommendationService';

export async function POST(request: NextRequest) {
  try {
    const { projectName, projectDescription } = await request.json();

    if (!projectName || typeof projectName !== 'string') {
      return NextResponse.json(
        { error: 'ProjectName is required and must be a string' },
        { status: 400 }
      );
    }

    if (!projectDescription || typeof projectDescription !== 'string') {
      return NextResponse.json(
        { error: 'ProjectDescription is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await analyzeProjectAndRecommendModules(projectName, projectDescription);
    return NextResponse.json(result);

  } catch (error) {
    console.error('Error analyzing project for module recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to analyze project for module recommendations' },
      { status: 500 }
    );
  }
}
