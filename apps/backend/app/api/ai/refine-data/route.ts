import { NextRequest, NextResponse } from 'next/server';
import { refineData, EntityType } from '../../../../services/openai';

export async function POST(request: NextRequest) {
  try {
    const { originalData, entityType, refinementCommand } = await request.json();

    if (!originalData || typeof originalData !== 'object') {
      return NextResponse.json(
        { error: 'OriginalData is required and must be an object' },
        { status: 400 }
      );
    }

    if (!entityType || typeof entityType !== 'string') {
      return NextResponse.json(
        { error: 'EntityType is required and must be a string' },
        { status: 400 }
      );
    }

    if (!refinementCommand || typeof refinementCommand !== 'string') {
      return NextResponse.json(
        { error: 'RefinementCommand is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await refineData(
      originalData,
      entityType as EntityType,
      refinementCommand
    );

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error refining data:', error);
    return NextResponse.json(
      { error: 'Failed to refine data' },
      { status: 500 }
    );
  }
}
