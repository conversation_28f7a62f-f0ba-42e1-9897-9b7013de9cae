import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { z } from 'zod';

// Validation schema for inventory item updates
const updateInventoryItemSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  quantity: z.number().nonnegative().optional(),
  unit: z.string().min(1).optional(),
  category: z.string().min(1).optional(),
  categoryId: z.string().min(1).optional(),
  minStock: z.number().nonnegative().optional(),
  maxStock: z.number().positive().optional(),
  cost: z.number().nonnegative().optional(),
  location: z.string().optional(),
  supplier: z.string().optional(),
  additionalFields: z.record(z.any()).optional(),
});

// GET /api/inventory/[id] - Get a specific inventory item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const inventoryItem = await prisma.inventoryItem.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            businessType: true,
            companyName: true,
          },
        },
      },
    });

    if (!inventoryItem) {
      return NextResponse.json(
        { error: 'Inventory item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(inventoryItem);
  } catch (error) {
    console.error('Error fetching inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory item' },
      { status: 500 }
    );
  }
}

// PUT /api/inventory/[id] - Update an inventory item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const validatedData = updateInventoryItemSchema.parse(body);

    // Validate min/max stock relationship if both are provided
    if (validatedData.minStock !== undefined && validatedData.maxStock !== undefined) {
      if (validatedData.minStock >= validatedData.maxStock) {
        return NextResponse.json(
          { error: 'Minimum stock must be less than maximum stock' },
          { status: 400 }
        );
      }
    }

    const inventoryItem = await prisma.inventoryItem.update({
      where: { id: params.id },
      data: validatedData,
    });

    return NextResponse.json(inventoryItem);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to update inventory item' },
      { status: 500 }
    );
  }
}

// DELETE /api/inventory/[id] - Delete an inventory item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.inventoryItem.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Inventory item deleted successfully' });
  } catch (error) {
    console.error('Error deleting inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to delete inventory item' },
      { status: 500 }
    );
  }
}
