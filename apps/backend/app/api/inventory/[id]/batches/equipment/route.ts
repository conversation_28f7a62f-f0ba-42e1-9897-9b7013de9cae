import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/prisma';

// Transform camelCase to snake_case for frontend compatibility
function transformRecordToFrontend(record: any) {
  return {
    ...record,
    serial_number: record.serialNumber,
    asset_tag: record.assetTag,
    test_status: record.testStatus,
    operational_status: record.operationalStatus,
    unit_cost: record.unitCost,
    assembly_date: record.assemblyDate,
    warranty_period: record.warrantyPeriod,
    maintenance_schedule: record.maintenanceSchedule,
  };
}

// Transform snake_case to camelCase for database compatibility
function transformRecordFromFrontend(data: any) {
  const {
    serial_number, asset_tag, test_status, operational_status,
    unit_cost, assembly_date, warranty_period, maintenance_schedule, ...rest
  } = data;
  return {
    ...rest,
    serialNumber: serial_number || data.serialNumber,
    assetTag: asset_tag || data.assetTag,
    testStatus: test_status || data.testStatus,
    operationalStatus: operational_status || data.operationalStatus,
    unitCost: unit_cost || data.unitCost,
    assemblyDate: assembly_date || data.assemblyDate,
    warrantyPeriod: warranty_period || data.warrantyPeriod,
    maintenanceSchedule: maintenance_schedule || data.maintenanceSchedule,
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const records = await prisma.equipmentRecord.findMany({
      where: { inventoryItemId: id },
      orderBy: { createdAt: 'desc' },
    });

    // Transform to frontend format
    const transformedRecords = records.map(transformRecordToFrontend);
    return NextResponse.json(transformedRecords);
  } catch (error) {
    console.error('Error fetching equipment records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch equipment records' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    // Transform frontend format to database format
    const transformedData = transformRecordFromFrontend(data);

    const record = await prisma.equipmentRecord.create({
      data: {
        ...transformedData,
        inventoryItemId: id,
      },
    });

    // Transform back to frontend format
    const transformedRecord = transformRecordToFrontend(record);
    return NextResponse.json(transformedRecord, { status: 201 });
  } catch (error) {
    console.error('Error creating equipment record:', error);
    return NextResponse.json(
      { error: 'Failed to create equipment record' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { recordId, ...data } = await request.json();

    const record = await prisma.equipmentRecord.update({
      where: { 
        id: recordId,
        inventoryItemId: id 
      },
      data,
    });

    return NextResponse.json(record);
  } catch (error) {
    console.error('Error updating equipment record:', error);
    return NextResponse.json(
      { error: 'Failed to update equipment record' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const recordId = searchParams.get('recordId');

    if (!recordId) {
      return NextResponse.json(
        { error: 'Record ID is required' },
        { status: 400 }
      );
    }

    await prisma.equipmentRecord.delete({
      where: { 
        id: recordId,
        inventoryItemId: id 
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting equipment record:', error);
    return NextResponse.json(
      { error: 'Failed to delete equipment record' },
      { status: 500 }
    );
  }
}
