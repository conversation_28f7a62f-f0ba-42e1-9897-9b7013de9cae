import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/prisma';

// Transform camelCase to snake_case for frontend compatibility
function transformBatchToFrontend(batch: any) {
  return {
    ...batch,
    material_grade: batch.materialGrade,
    initial_quantity: batch.initialQuantity,
    current_quantity: batch.currentQuantity,
    usage_percentage: batch.usagePercentage,
    unit_cost: batch.unitCost,
    purchase_date: batch.purchaseDate,
    expiry_date: batch.expiryDate,
  };
}

// Transform snake_case to camelCase for database compatibility
function transformBatchFromFrontend(data: any) {
  const {
    material_grade, initial_quantity, current_quantity, usage_percentage,
    unit_cost, purchase_date, expiry_date, ...rest
  } = data;
  return {
    ...rest,
    materialGrade: material_grade || data.materialGrade,
    initialQuantity: initial_quantity || data.initialQuantity,
    currentQuantity: current_quantity || data.currentQuantity,
    usagePercentage: usage_percentage || data.usagePercentage,
    unitCost: unit_cost || data.unitCost,
    purchaseDate: purchase_date || data.purchaseDate,
    expiryDate: expiry_date || data.expiryDate,
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const batches = await prisma.consumableBatch.findMany({
      where: { inventoryItemId: id },
      orderBy: { createdAt: 'desc' },
    });

    // Transform to frontend format
    const transformedBatches = batches.map(transformBatchToFrontend);
    return NextResponse.json(transformedBatches);
  } catch (error) {
    console.error('Error fetching consumable batches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch consumable batches' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    // Transform frontend format to database format
    const transformedData = transformBatchFromFrontend(data);

    const batch = await prisma.consumableBatch.create({
      data: {
        ...transformedData,
        inventoryItemId: id,
      },
    });

    // Transform back to frontend format
    const transformedBatch = transformBatchToFrontend(batch);
    return NextResponse.json(transformedBatch, { status: 201 });
  } catch (error) {
    console.error('Error creating consumable batch:', error);
    return NextResponse.json(
      { error: 'Failed to create consumable batch' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { batchId, ...data } = await request.json();

    const batch = await prisma.consumableBatch.update({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
      data,
    });

    return NextResponse.json(batch);
  } catch (error) {
    console.error('Error updating consumable batch:', error);
    return NextResponse.json(
      { error: 'Failed to update consumable batch' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (!batchId) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    await prisma.consumableBatch.delete({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting consumable batch:', error);
    return NextResponse.json(
      { error: 'Failed to delete consumable batch' },
      { status: 500 }
    );
  }
}
