import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/prisma';

// Transform camelCase to snake_case for frontend compatibility
function transformBatchToFrontend(batch: any) {
  return {
    ...batch,
    unit_cost: batch.unitCost,
    total_cost: batch.totalCost,
    purchase_date: batch.purchaseDate,
  };
}

// Transform snake_case to camelCase for database compatibility
function transformBatchFromFrontend(data: any) {
  const { unit_cost, total_cost, purchase_date, ...rest } = data;
  return {
    ...rest,
    unitCost: unit_cost || data.unitCost,
    totalCost: total_cost || data.totalCost,
    purchaseDate: purchase_date || data.purchaseDate,
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const batches = await prisma.rawMaterialBatch.findMany({
      where: { inventoryItemId: id },
      orderBy: { createdAt: 'desc' },
    });

    // Transform to frontend format
    const transformedBatches = batches.map(transformBatchToFrontend);
    return NextResponse.json(transformedBatches);
  } catch (error) {
    console.error('Error fetching raw material batches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch raw material batches' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    // Transform frontend format to database format
    const transformedData = transformBatchFromFrontend(data);

    const batch = await prisma.rawMaterialBatch.create({
      data: {
        ...transformedData,
        inventoryItemId: id,
      },
    });

    // Transform back to frontend format
    const transformedBatch = transformBatchToFrontend(batch);
    return NextResponse.json(transformedBatch, { status: 201 });
  } catch (error) {
    console.error('Error creating raw material batch:', error);
    return NextResponse.json(
      { error: 'Failed to create raw material batch' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { batchId, ...data } = await request.json();

    const batch = await prisma.rawMaterialBatch.update({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
      data,
    });

    return NextResponse.json(batch);
  } catch (error) {
    console.error('Error updating raw material batch:', error);
    return NextResponse.json(
      { error: 'Failed to update raw material batch' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (!batchId) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    await prisma.rawMaterialBatch.delete({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting raw material batch:', error);
    return NextResponse.json(
      { error: 'Failed to delete raw material batch' },
      { status: 500 }
    );
  }
}
