import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/prisma';

// Transform camelCase to snake_case for frontend compatibility
function transformBatchToFrontend(batch: any) {
  return {
    ...batch,
    serial_number: batch.serialNumber,
    firmware_version: batch.firmwareVersion,
    material_finish: batch.materialFinish,
    power_rating: batch.powerRating,
    quality_status: batch.qualityStatus,
    unit_cost: batch.unitCost,
    sale_price: batch.salePrice,
    completion_date: batch.completionDate,
  };
}

// Transform snake_case to camelCase for database compatibility
function transformBatchFromFrontend(data: any) {
  const {
    serial_number, firmware_version, material_finish, power_rating,
    quality_status, unit_cost, sale_price, completion_date, ...rest
  } = data;
  return {
    ...rest,
    serialNumber: serial_number || data.serialNumber,
    firmwareVersion: firmware_version || data.firmwareVersion,
    materialFinish: material_finish || data.materialFinish,
    powerRating: power_rating || data.powerRating,
    qualityStatus: quality_status || data.qualityStatus,
    unitCost: unit_cost || data.unitCost,
    salePrice: sale_price || data.salePrice,
    completionDate: completion_date || data.completionDate,
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const batches = await prisma.finishedProductBatch.findMany({
      where: { inventoryItemId: id },
      orderBy: { createdAt: 'desc' },
    });

    // Transform to frontend format
    const transformedBatches = batches.map(transformBatchToFrontend);
    return NextResponse.json(transformedBatches);
  } catch (error) {
    console.error('Error fetching finished product batches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch finished product batches' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    // Transform frontend format to database format
    const transformedData = transformBatchFromFrontend(data);

    const batch = await prisma.finishedProductBatch.create({
      data: {
        ...transformedData,
        inventoryItemId: id,
      },
    });

    // Transform back to frontend format
    const transformedBatch = transformBatchToFrontend(batch);
    return NextResponse.json(transformedBatch, { status: 201 });
  } catch (error) {
    console.error('Error creating finished product batch:', error);
    return NextResponse.json(
      { error: 'Failed to create finished product batch' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { batchId, ...data } = await request.json();

    const batch = await prisma.finishedProductBatch.update({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
      data,
    });

    return NextResponse.json(batch);
  } catch (error) {
    console.error('Error updating finished product batch:', error);
    return NextResponse.json(
      { error: 'Failed to update finished product batch' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (!batchId) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    await prisma.finishedProductBatch.delete({
      where: { 
        id: batchId,
        inventoryItemId: id 
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting finished product batch:', error);
    return NextResponse.json(
      { error: 'Failed to delete finished product batch' },
      { status: 500 }
    );
  }
}
