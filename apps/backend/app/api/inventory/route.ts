import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { z } from 'zod';

// Validation schema for inventory item creation
const createInventoryItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().nonnegative('Quantity must be non-negative'),
  unit: z.string().min(1, 'Unit is required'),
  category: z.string().min(1, 'Category is required'),
  categoryId: z.string().min(1, 'Category ID is required'),
  minStock: z.number().nonnegative().optional(),
  maxStock: z.number().positive().optional(),
  cost: z.number().nonnegative('Cost must be non-negative'),
  location: z.string().optional(),
  supplier: z.string().optional(),
  additionalFields: z.record(z.any()).optional(),
  userId: z.string().min(1, 'User ID is required'),
});

// GET /api/inventory - Get all inventory items
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const category = searchParams.get('category');
    const lowStock = searchParams.get('lowStock') === 'true';

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const where: any = { userId };
    
    if (category) {
      where.category = category;
    }

    let inventoryItems = await prisma.inventoryItem.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });

    // Filter for low stock items if requested
    if (lowStock) {
      inventoryItems = inventoryItems.filter(item => 
        item.minStock !== null && item.quantity <= item.minStock
      );
    }

    return NextResponse.json(inventoryItems);
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory items' },
      { status: 500 }
    );
  }
}

// POST /api/inventory - Create a new inventory item
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createInventoryItemSchema.parse(body);

    // Validate min/max stock relationship
    if (validatedData.minStock && validatedData.maxStock && validatedData.minStock >= validatedData.maxStock) {
      return NextResponse.json(
        { error: 'Minimum stock must be less than maximum stock' },
        { status: 400 }
      );
    }

    const inventoryItem = await prisma.inventoryItem.create({
      data: validatedData,
    });

    return NextResponse.json(inventoryItem, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating inventory item:', error);
    return NextResponse.json(
      { error: 'Failed to create inventory item' },
      { status: 500 }
    );
  }
}
