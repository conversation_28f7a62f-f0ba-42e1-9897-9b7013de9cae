import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '../../../lib/prisma';

// Validation schema for task creation
const createTaskSchema = z.object({
  name: z.string().min(1, 'Task concept/description is required'),
  startDate: z.string().optional(), // ISO date string
  endDate: z.string().optional(), // ISO date string
  assignedToId: z.string().nullable().optional(), // Team member ID
  projectId: z.string().nullable().optional(), // Optional project assignment
  completed: z.boolean().optional().default(false),
}).transform((data) => ({
  ...data,
  // Convert empty strings to null for foreign key fields
  assignedToId: data.assignedToId === '' ? null : data.assignedToId,
  projectId: data.projectId === '' ? null : data.projectId,
}));

// Validation schema for task updates
const updateTaskSchema = z.object({
  name: z.string().min(1).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  assignedToId: z.string().nullable().optional(),
  projectId: z.string().nullable().optional(),
  completed: z.boolean().optional(),
}).transform((data) => ({
  ...data,
  // Convert empty strings to null for foreign key fields
  assignedToId: data.assignedToId === '' ? null : data.assignedToId,
  projectId: data.projectId === '' ? null : data.projectId,
}));

// GET /api/tasks - Get all tasks (optionally filtered by user)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const tasks = await prisma.projectTask.findMany({
      where: {
        OR: [
          // Tasks in projects owned by the user
          {
            project: {
              userId: userId
            }
          },
          // Tasks assigned to team members of the user
          {
            assignedTo: {
              userId: userId
            }
          },
          // Tasks without project but assigned to user's team members
          {
            projectId: null,
            assignedTo: {
              userId: userId
            }
          }
        ]
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            role: true,
            avatar: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(tasks);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    );
  }
}

// POST /api/tasks - Create a new task
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assignedToId, projectId, startDate, endDate, ...taskData } = createTaskSchema.parse(body);

    // Verify team member exists if assigned
    if (assignedToId) {
      const teamMember = await prisma.teamMember.findUnique({
        where: { id: assignedToId },
      });

      if (!teamMember) {
        return NextResponse.json(
          { error: 'Team member not found' },
          { status: 404 }
        );
      }
    }

    // Verify project exists if assigned
    if (projectId) {
      const project = await prisma.project.findUnique({
        where: { id: projectId },
      });

      if (!project) {
        return NextResponse.json(
          { error: 'Project not found' },
          { status: 404 }
        );
      }
    }

    // Create the task
    const task = await prisma.projectTask.create({
      data: {
        ...taskData,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        assignedToId,
        projectId,
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            role: true,
            avatar: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(task, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating task:', error);
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    );
  }
}
