/*
  Warnings:

  - The `startDate` column on the `project_tasks` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `endDate` column on the `project_tasks` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `project_task_assignments` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "project_task_assignments" DROP CONSTRAINT "project_task_assignments_taskId_fkey";

-- DropForeignKey
ALTER TABLE "project_task_assignments" DROP CONSTRAINT "project_task_assignments_teamMemberId_fkey";

-- AlterTable
ALTER TABLE "project_tasks" ADD COLUMN     "assignedToId" TEXT,
DROP COLUMN "startDate",
ADD COLUMN     "startDate" TIMESTAMP(3),
DROP COLUMN "endDate",
ADD COLUMN     "endDate" TIMESTAMP(3),
ALTER COLUMN "projectId" DROP NOT NULL;

-- DropTable
DROP TABLE "project_task_assignments";

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_assignedToId_fkey" FOREIGN KEY ("assignedToId") REFERENCES "team_members"("id") ON DELETE SET NULL ON UPDATE CASCADE;
