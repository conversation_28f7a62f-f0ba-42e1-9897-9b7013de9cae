import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create a default user with the expected ID
  const user = await prisma.user.upsert({
    where: { id: 'cmcl1e6l70000v1kcq5hf8v55' },
    update: {},
    create: {
      id: 'cmcl1e6l70000v1kcq5hf8v55',
      name: 'Admin User',
      email: '<EMAIL>',
      businessType: 'fabrication',
      companyName: 'Example Company',
    },
  });

  console.log('✅ Created user:', user.email);

  // Create some sample team members
  const teamMember1 = await prisma.teamMember.create({
    data: {
      name: '<PERSON>',
      role: 'Project Manager',
      email: '<EMAIL>',
      status: 'active',
      salary: 75000,
      currency: 'USD',
      hourlyRate: 50,
      skills: ['Project Management', 'Leadership'],
      userId: user.id,
    },
  });

  const teamMember2 = await prisma.teamMember.create({
    data: {
      name: '<PERSON>',
      role: 'Developer',
      email: '<EMAIL>',
      status: 'active',
      salary: 85000,
      currency: 'USD',
      hourlyRate: 60,
      skills: ['JavaScript', 'React', 'Node.js'],
      userId: user.id,
    },
  });

  console.log('✅ Created team members');

  // Create a sample project
  const project = await prisma.project.create({
    data: {
      name: 'Sample Project',
      description: 'A sample project for testing',
      status: 'planning',
      startDate: new Date(),
      userId: user.id,
      modules: {
        create: {
          enabled: ['finance', 'team', 'timeline'],
          configuration: {
            team: {
              trackHours: true,
              enableSkillsManagement: true,
              enableCostTracking: true,
            },
            timeline: {
              enableGanttView: true,
              enableMilestones: true,
              trackDependencies: false,
            },
          },
          aiRecommended: ['finance', 'team', 'timeline', 'catalog'],
        },
      },
      teamMembers: {
        create: [
          { teamMemberId: teamMember1.id },
          { teamMemberId: teamMember2.id },
        ],
      },
    },
  });

  console.log('✅ Created project:', project.name);

  // Create some sample catalog items
  const catalogItem = await prisma.catalogItem.create({
    data: {
      productName: 'Custom Metal Fabrication',
      productDescription: 'High-quality custom metal fabrication services',
      categoryLabel: 'Service',
      userId: user.id,
      details: {
        create: {
          title: 'Custom Metal Fabrication Service',
          type: 'Service',
          duration: '2-4 weeks',
          serviceType: 'metal_fabrication',
          requirements: ['Technical drawings', 'Material specifications'],
          images: [],
        },
      },
    },
  });

  console.log('✅ Created catalog item:', catalogItem.productName);

  // Create some sample inventory items
  const inventoryItem = await prisma.inventoryItem.create({
    data: {
      name: 'Steel Sheets',
      description: 'High-grade steel sheets for fabrication',
      quantity: 100,
      unit: 'sheets',
      category: 'Raw Materials',
      categoryId: 'raw_materials',
      minStock: 20,
      maxStock: 200,
      cost: 150.00,
      location: 'Warehouse A',
      supplier: 'Steel Supply Co.',
      userId: user.id,
    },
  });

  console.log('✅ Created inventory item:', inventoryItem.name);

  // Create some sample financial records
  const financialRecord = await prisma.financialRecord.create({
    data: {
      type: 'income',
      amount: 5000.00,
      description: 'Project payment',
      date: new Date(),
      category: 'Project Revenue',
      userId: user.id,
    },
  });

  console.log('✅ Created financial record');

  console.log('🎉 Seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
