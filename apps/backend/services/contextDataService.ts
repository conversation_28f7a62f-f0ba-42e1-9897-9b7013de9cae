import { prisma } from '../lib/prisma';
import type { TeamMember, CatalogItem, InventoryItem } from '@repo/ui/types';

// Backend implementation of context data service
export class BackendContextDataService {
  // Fetch team members for context selection
  async fetchTeamMembers(userId: string): Promise<TeamMember[]> {
    const teamMembers = await prisma.teamMember.findMany({
      where: { 
        userId,
        status: 'active',
      },
      include: {
        projects: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    return teamMembers as TeamMember[];
  }

  // Fetch catalog items for context selection
  async fetchCatalogItems(userId: string, type?: 'Product' | 'Service'): Promise<CatalogItem[]> {
    const where: any = { userId };
    if (type) {
      where.categoryLabel = type;
    }

    const catalogItems = await prisma.catalogItem.findMany({
      where,
      include: {
        details: {
          include: {
            materials: true,
            deliverables: true,
            documents: true,
          },
        },
      },
      orderBy: { productName: 'asc' },
    });

    return catalogItems as CatalogItem[];
  }

  // Fetch inventory items for context selection
  async fetchInventoryItems(userId: string, category?: string): Promise<InventoryItem[]> {
    const where: any = { userId };
    if (category) {
      where.category = category;
    }

    const inventoryItems = await prisma.inventoryItem.findMany({
      where,
      orderBy: { name: 'asc' },
    });

    return inventoryItems as InventoryItem[];
  }

  // Fetch available categories for filtering
  async fetchInventoryCategories(userId: string): Promise<string[]> {
    const categories = await prisma.inventoryItem.findMany({
      where: { userId },
      select: { category: true },
      distinct: ['category'],
    });

    return categories.map(item => item.category);
  }

  // Fetch team member skills for filtering
  async fetchTeamMemberSkills(userId: string): Promise<string[]> {
    const teamMembers = await prisma.teamMember.findMany({
      where: { userId },
      select: { skills: true },
    });

    const allSkills = teamMembers.flatMap(member => member.skills || []);
    return [...new Set(allSkills)];
  }

  // Fetch team member roles for filtering
  async fetchTeamMemberRoles(userId: string): Promise<string[]> {
    const roles = await prisma.teamMember.findMany({
      where: { userId },
      select: { role: true },
      distinct: ['role'],
    });

    return roles.map(item => item.role);
  }

  // Search functionality across all context types
  async searchContextItems(
    userId: string,
    query: string,
    types: ('team_member' | 'catalog_item' | 'inventory_item')[] = ['team_member', 'catalog_item', 'inventory_item']
  ): Promise<{
    teamMembers: TeamMember[];
    catalogItems: CatalogItem[];
    inventoryItems: InventoryItem[];
  }> {
    const results = {
      teamMembers: [] as TeamMember[],
      catalogItems: [] as CatalogItem[],
      inventoryItems: [] as InventoryItem[],
    };

    const searchQuery = query.toLowerCase();

    if (types.includes('team_member')) {
      results.teamMembers = await prisma.teamMember.findMany({
        where: {
          userId,
          OR: [
            { name: { contains: searchQuery, mode: 'insensitive' } },
            { role: { contains: searchQuery, mode: 'insensitive' } },
            { email: { contains: searchQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          projects: {
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                  status: true,
                },
              },
            },
          },
        },
        orderBy: { name: 'asc' },
      }) as TeamMember[];
    }

    if (types.includes('catalog_item')) {
      results.catalogItems = await prisma.catalogItem.findMany({
        where: {
          userId,
          OR: [
            { productName: { contains: searchQuery, mode: 'insensitive' } },
            { productDescription: { contains: searchQuery, mode: 'insensitive' } },
            { categoryLabel: { contains: searchQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          details: {
            include: {
              materials: true,
              deliverables: true,
              documents: true,
            },
          },
        },
        orderBy: { productName: 'asc' },
      }) as CatalogItem[];
    }

    if (types.includes('inventory_item')) {
      results.inventoryItems = await prisma.inventoryItem.findMany({
        where: {
          userId,
          OR: [
            { name: { contains: searchQuery, mode: 'insensitive' } },
            { description: { contains: searchQuery, mode: 'insensitive' } },
            { category: { contains: searchQuery, mode: 'insensitive' } },
            { supplier: { contains: searchQuery, mode: 'insensitive' } },
          ],
        },
        orderBy: { name: 'asc' },
      }) as InventoryItem[];
    }

    return results;
  }

  // Get low stock inventory items
  async getLowStockItems(userId: string): Promise<InventoryItem[]> {
    const lowStockItems = await prisma.inventoryItem.findMany({
      where: {
        userId,
        AND: [
          { minStock: { not: null } },
          { quantity: { lte: prisma.inventoryItem.fields.minStock } },
        ],
      },
      orderBy: { quantity: 'asc' },
    });

    return lowStockItems as InventoryItem[];
  }

  // Get team members by skill
  async getTeamMembersBySkill(userId: string, skill: string): Promise<TeamMember[]> {
    const teamMembers = await prisma.teamMember.findMany({
      where: {
        userId,
        skills: {
          has: skill,
        },
      },
      include: {
        projects: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    return teamMembers as TeamMember[];
  }

  // Get available team members (not assigned to active projects)
  async getAvailableTeamMembers(userId: string): Promise<TeamMember[]> {
    const teamMembers = await prisma.teamMember.findMany({
      where: {
        userId,
        status: 'active',
        projects: {
          none: {
            project: {
              status: 'in_progress',
            },
          },
        },
      },
      include: {
        projects: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    return teamMembers as TeamMember[];
  }
}

// Export singleton instance
export const backendContextDataService = new BackendContextDataService();
