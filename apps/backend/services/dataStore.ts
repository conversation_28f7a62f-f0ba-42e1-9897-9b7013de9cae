import { prisma } from '../lib/prisma';
import type {
  User,
  Project,
  TeamMember,
  CatalogItem,
  InventoryItem,
  FinancialRecord,
  FinancialMovement,
} from '@repo/ui/types';

// Backend implementation of the DataStore interface
export class BackendDataStore {
  // User operations
  async getUsers(): Promise<User[]> {
    const users = await prisma.user.findMany({
      include: {
        _count: {
          select: {
            projects: true,
            teamMembers: true,
            catalogItems: true,
            inventoryItems: true,
          },
        },
      },
    });
    return users as User[];
  }

  async getUserById(id: string): Promise<User | null> {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        projects: true,
        teamMembers: true,
        catalogItems: true,
        inventoryItems: true,
        financialRecords: true,
        financialMovements: true,
      },
    });
    return user as User | null;
  }

  async addUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const user = await prisma.user.create({
      data: userData,
    });
    return user as User;
  }

  // Team Member operations
  async getTeamMembers(userId: string): Promise<TeamMember[]> {
    const teamMembers = await prisma.teamMember.findMany({
      where: { userId },
      include: {
        projects: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
    return teamMembers as TeamMember[];
  }

  async addTeamMember(teamMemberData: Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<TeamMember> {
    const teamMember = await prisma.teamMember.create({
      data: teamMemberData,
    });
    return teamMember as TeamMember;
  }

  async deleteTeamMember(id: string): Promise<void> {
    await prisma.teamMember.delete({
      where: { id },
    });
  }

  // Project operations
  async getProjects(userId: string): Promise<Project[]> {
    const projects = await prisma.project.findMany({
      where: { userId },
      include: {
        modules: true,
        teamMembers: {
          include: {
            teamMember: {
              select: {
                id: true,
                name: true,
                role: true,
                email: true,
                avatar: true,
              },
            },
          },
        },
        tasks: {
          include: {
            assignedTo: {
              include: {
                teamMember: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
    return projects as Project[];
  }

  async addProject(projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {
    const { modules, teamMembers, tasks, ...basicProjectData } = projectData as any;

    const project = await prisma.project.create({
      data: {
        ...basicProjectData,
        modules: modules ? {
          create: modules,
        } : undefined,
        teamMembers: teamMembers?.length > 0 ? {
          create: teamMembers.map((tm: any) => ({
            teamMemberId: tm.teamMemberId || tm.id,
          })),
        } : undefined,
        tasks: tasks?.length > 0 ? {
          create: tasks.map((task: any) => ({
            ...task,
            assignedTo: task.assignedTo ? {
              create: task.assignedTo.map((assignment: any) => ({
                teamMemberId: assignment.teamMemberId || assignment.id,
              })),
            } : undefined,
          })),
        } : undefined,
      },
      include: {
        modules: true,
        teamMembers: {
          include: {
            teamMember: true,
          },
        },
        tasks: {
          include: {
            assignedTo: {
              include: {
                teamMember: true,
              },
            },
          },
        },
      },
    });

    return project as Project;
  }

  async deleteProject(id: string): Promise<void> {
    await prisma.project.delete({
      where: { id },
    });
  }

  // Catalog operations
  async getCatalogItems(userId: string): Promise<CatalogItem[]> {
    const catalogItems = await prisma.catalogItem.findMany({
      where: { userId },
      include: {
        details: {
          include: {
            materials: true,
            deliverables: true,
            documents: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
    return catalogItems as CatalogItem[];
  }

  async addCatalogItem(catalogItemData: Omit<CatalogItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<CatalogItem> {
    const { details, ...basicCatalogData } = catalogItemData as any;

    const catalogItem = await prisma.catalogItem.create({
      data: {
        ...basicCatalogData,
        details: details ? {
          create: {
            ...details,
            materials: details.materials ? {
              create: details.materials,
            } : undefined,
            deliverables: details.deliverables ? {
              create: details.deliverables,
            } : undefined,
            documents: details.documents ? {
              create: details.documents,
            } : undefined,
          },
        } : undefined,
      },
      include: {
        details: {
          include: {
            materials: true,
            deliverables: true,
            documents: true,
          },
        },
      },
    });

    return catalogItem as CatalogItem;
  }

  async deleteCatalogItem(id: string): Promise<void> {
    await prisma.catalogItem.delete({
      where: { id },
    });
  }

  // Inventory operations
  async getInventoryItems(userId: string): Promise<InventoryItem[]> {
    const inventoryItems = await prisma.inventoryItem.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
    return inventoryItems as InventoryItem[];
  }

  async addInventoryItem(inventoryItemData: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<InventoryItem> {
    const inventoryItem = await prisma.inventoryItem.create({
      data: inventoryItemData,
    });
    return inventoryItem as InventoryItem;
  }

  async deleteInventoryItem(id: string): Promise<void> {
    await prisma.inventoryItem.delete({
      where: { id },
    });
  }

  // Financial operations
  async getFinancialRecords(userId: string): Promise<FinancialRecord[]> {
    const financialRecords = await prisma.financialRecord.findMany({
      where: { userId },
      orderBy: { date: 'desc' },
    });
    return financialRecords as FinancialRecord[];
  }

  async addFinancialRecord(financialRecordData: Omit<FinancialRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<FinancialRecord> {
    const financialRecord = await prisma.financialRecord.create({
      data: financialRecordData,
    });
    return financialRecord as FinancialRecord;
  }

  async deleteFinancialRecord(id: string): Promise<void> {
    await prisma.financialRecord.delete({
      where: { id },
    });
  }

  async deleteFinancialMovement(id: string): Promise<void> {
    await prisma.financialMovement.delete({
      where: { id },
    });
  }

  // Additional utility methods
  async updateTeamMember(id: string, teamMemberData: Partial<TeamMember>): Promise<TeamMember> {
    const teamMember = await prisma.teamMember.update({
      where: { id },
      data: teamMemberData,
    });
    return teamMember as TeamMember;
  }

  async updateProject(id: string, projectData: Partial<Project>): Promise<Project> {
    const { modules, teamMembers, tasks, ...basicProjectData } = projectData as any;

    const project = await prisma.$transaction(async (tx) => {
      // Update basic project data
      const updatedProject = await tx.project.update({
        where: { id },
        data: basicProjectData,
      });

      // Update modules if provided
      if (modules) {
        await tx.projectModules.upsert({
          where: { projectId: id },
          update: modules,
          create: {
            projectId: id,
            ...modules,
          },
        });
      }

      // Update team members if provided
      if (teamMembers) {
        // Remove existing team members
        await tx.projectTeamMember.deleteMany({
          where: { projectId: id },
        });

        // Add new team members
        if (teamMembers.length > 0) {
          await tx.projectTeamMember.createMany({
            data: teamMembers.map((tm: any) => ({
              projectId: id,
              teamMemberId: tm.teamMemberId || tm.id,
            })),
          });
        }
      }

      // Return the updated project with all relations
      return await tx.project.findUnique({
        where: { id },
        include: {
          modules: true,
          teamMembers: {
            include: {
              teamMember: true,
            },
          },
          tasks: {
            include: {
              assignedTo: {
                include: {
                  teamMember: true,
                },
              },
            },
          },
        },
      });
    });

    return project as Project;
  }

  async updateCatalogItem(id: string, catalogItemData: Partial<CatalogItem>): Promise<CatalogItem> {
    const { details, ...basicCatalogData } = catalogItemData as any;

    const catalogItem = await prisma.$transaction(async (tx) => {
      // Update basic catalog item data
      const updatedItem = await tx.catalogItem.update({
        where: { id },
        data: basicCatalogData,
      });

      // Update details if provided
      if (details) {
        await tx.catalogItemDetails.upsert({
          where: { catalogItemId: id },
          update: details,
          create: {
            catalogItemId: id,
            ...details,
          },
        });
      }

      // Return the updated item with all relations
      return await tx.catalogItem.findUnique({
        where: { id },
        include: {
          details: {
            include: {
              materials: true,
              deliverables: true,
              documents: true,
            },
          },
        },
      });
    });

    return catalogItem as CatalogItem;
  }

  async updateInventoryItem(id: string, inventoryItemData: Partial<InventoryItem>): Promise<InventoryItem> {
    const inventoryItem = await prisma.inventoryItem.update({
      where: { id },
      data: inventoryItemData,
    });
    return inventoryItem as InventoryItem;
  }

  async updateFinancialRecord(id: string, financialRecordData: Partial<FinancialRecord>): Promise<FinancialRecord> {
    const financialRecord = await prisma.financialRecord.update({
      where: { id },
      data: financialRecordData,
    });
    return financialRecord as FinancialRecord;
  }

  async updateFinancialMovement(id: string, financialMovementData: Partial<FinancialMovement>): Promise<FinancialMovement> {
    const { recurringDetails, ...basicMovementData } = financialMovementData as any;

    const financialMovement = await prisma.$transaction(async (tx) => {
      // Update basic movement data
      const updatedMovement = await tx.financialMovement.update({
        where: { id },
        data: basicMovementData,
      });

      // Update recurring details if provided
      if (recurringDetails) {
        await tx.recurringPaymentDetails.upsert({
          where: { financialMovementId: id },
          update: recurringDetails,
          create: {
            financialMovementId: id,
            ...recurringDetails,
          },
        });
      }

      // Return the updated movement with all relations
      return await tx.financialMovement.findUnique({
        where: { id },
        include: {
          recurringDetails: true,
        },
      });
    });

    return financialMovement as FinancialMovement;
  }

  async getFinancialMovements(userId: string): Promise<FinancialMovement[]> {
    const financialMovements = await prisma.financialMovement.findMany({
      where: { userId },
      include: {
        recurringDetails: true,
      },
      orderBy: { fecha: 'desc' },
    });
    return financialMovements as FinancialMovement[];
  }

  async addFinancialMovement(financialMovementData: Omit<FinancialMovement, 'id' | 'createdAt' | 'updatedAt'>): Promise<FinancialMovement> {
    const { recurringDetails, ...basicMovementData } = financialMovementData as any;

    const financialMovement = await prisma.financialMovement.create({
      data: {
        ...basicMovementData,
        recurringDetails: recurringDetails ? {
          create: recurringDetails,
        } : undefined,
      },
      include: {
        recurringDetails: true,
      },
    });

    return financialMovement as FinancialMovement;
  }
}

// Export singleton instance
export const backendDataStore = new BackendDataStore();
