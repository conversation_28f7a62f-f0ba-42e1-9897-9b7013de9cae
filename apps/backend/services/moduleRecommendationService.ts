import { getOpenAIClient } from './openai';

// Type definitions (copied from web app to avoid cross-app imports)
export type ProjectModuleType = 'finance' | 'catalog' | 'team' | 'timeline' | 'inventory' | 'logistics';

export interface ModuleRecommendation {
  module: ProjectModuleType;
  confidence: number;
  reasoning: string;
  configuration: Record<string, any>;
}

export interface ModuleAnalysisResult {
  recommendations: ModuleRecommendation[];
  projectType: string;
  complexity: 'low' | 'medium' | 'high';
  suggestedModules: ProjectModuleType[];
}

export interface ModuleConfiguration {
  [key: string]: any;
}

/**
 * Analyze project description and recommend appropriate modules
 */
export async function analyzeProjectAndRecommendModules(
  projectName: string,
  projectDescription: string
): Promise<ModuleAnalysisResult> {
  const systemPrompt = `You are an expert project management consultant. Your task is to analyze project descriptions and recommend appropriate business modules for project management.

Available modules:
- finance: Financial tracking, budgeting, cost management (ALWAYS REQUIRED)
- catalog: Product and/or service catalog management
- team: Team member management, resource allocation, skill tracking
- timeline: Project timeline, task management, Gantt charts, milestones
- inventory: Materials management, Bill of Materials (BOM), supplier tracking
- logistics: Supply chain, shipments, deliveries, logistics coordination

Project types and typical module combinations:
- Construction/Building: finance, catalog (products), team, timeline, inventory, logistics
- Software Development: finance, catalog (services), team, timeline
- Manufacturing: finance, catalog (products), team, timeline, inventory, logistics
- Consulting/Services: finance, catalog (services), team, timeline
- Retail/E-commerce: finance, catalog (products), team, inventory, logistics
- Event Planning: finance, catalog (services), team, timeline, logistics
- Research Projects: finance, team, timeline
- Product Design: finance, catalog (products), team, timeline, inventory

For each recommended module, provide:
1. Confidence score (0-1)
2. Reasoning for recommendation
3. Suggested configuration if applicable

Return ONLY a valid JSON object with this structure:
{
  "recommendations": [
    {
      "module": "finance",
      "confidence": 1.0,
      "reasoning": "Financial tracking is essential for all projects",
      "configuration": {}
    }
  ],
  "projectType": "construction",
  "complexity": "medium",
  "suggestedModules": ["finance", "catalog", "team", "timeline"]
}`;

  const userPrompt = `Project Name: "${projectName}"
Project Description: "${projectDescription}"

Analyze this project and recommend the most appropriate modules. Consider:
1. What type of project this is
2. What business processes would be involved
3. What resources and tracking would be needed
4. The complexity level of the project

Provide specific reasoning for each module recommendation.`;

  try {
    const response = await getOpenAIClient().chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 1000
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    let cleanContent = content.trim();
    const firstBrace = cleanContent.indexOf('{');
    const lastBrace = cleanContent.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      cleanContent = cleanContent.substring(firstBrace, lastBrace + 1);
    }

    try {
      const parsed = JSON.parse(cleanContent) as ModuleAnalysisResult;
      
      // Validate and ensure finance module is always included
      if (!parsed.recommendations.find(r => r.module === 'finance')) {
        parsed.recommendations.unshift({
          module: 'finance',
          confidence: 1.0,
          reasoning: 'Financial tracking is essential for all projects',
          configuration: {}
        });
      }

      if (!parsed.suggestedModules.includes('finance')) {
        parsed.suggestedModules.unshift('finance');
      }

      return parsed;
    } catch (jsonError) {
      console.warn('Failed to parse AI module recommendation response:', cleanContent);
      // Return fallback recommendation
      return getDefaultModuleRecommendation(projectName, projectDescription);
    }

  } catch (error) {
    console.error('Error analyzing project for module recommendations:', error);
    // Return fallback recommendation
    return getDefaultModuleRecommendation(projectName, projectDescription);
  }
}

/**
 * Get default module recommendation when AI analysis fails
 */
function getDefaultModuleRecommendation(
  projectName: string,
  projectDescription: string
): ModuleAnalysisResult {
  const recommendations: ModuleRecommendation[] = [
    {
      module: 'finance',
      confidence: 1.0,
      reasoning: 'Financial tracking is essential for all projects',
      configuration: {}
    }
  ];

  // Simple keyword-based analysis for fallback
  const description = projectDescription.toLowerCase();
  const name = projectName.toLowerCase();
  const combined = `${name} ${description}`;

  let projectType = 'general';
  let complexity: 'low' | 'medium' | 'high' = 'medium';

  // Determine project type based on keywords
  if (/construction|building|renovation|infrastructure|civil|architecture/.test(combined)) {
    projectType = 'construction';
    complexity = 'high';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.9,
        reasoning: 'Construction projects typically require product catalog management',
        configuration: { enableProducts: true, enableServices: false }
      },
      {
        module: 'team',
        confidence: 0.9,
        reasoning: 'Construction projects require team coordination',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.95,
        reasoning: 'Timeline management is critical for construction projects',
        configuration: {}
      },
      {
        module: 'inventory',
        confidence: 0.85,
        reasoning: 'Material management is important for construction',
        configuration: {}
      },
      {
        module: 'logistics',
        confidence: 0.8,
        reasoning: 'Supply chain coordination is often needed',
        configuration: {}
      }
    );
  } else if (/software|development|app|web|programming|coding|tech/.test(combined)) {
    projectType = 'software';
    complexity = 'medium';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.7,
        reasoning: 'Software projects may offer services or products',
        configuration: { enableProducts: false, enableServices: true }
      },
      {
        module: 'team',
        confidence: 0.9,
        reasoning: 'Software development requires team collaboration',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.9,
        reasoning: 'Sprint planning and milestone tracking are important',
        configuration: {}
      }
    );
  } else if (/manufacturing|production|factory|assembly|fabrication/.test(combined)) {
    projectType = 'manufacturing';
    complexity = 'high';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.9,
        reasoning: 'Manufacturing projects need product catalog management',
        configuration: { enableProducts: true, enableServices: false }
      },
      {
        module: 'team',
        confidence: 0.8,
        reasoning: 'Manufacturing requires workforce management',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.85,
        reasoning: 'Production scheduling is critical',
        configuration: {}
      },
      {
        module: 'inventory',
        confidence: 0.95,
        reasoning: 'Raw materials and inventory management is essential',
        configuration: {}
      },
      {
        module: 'logistics',
        confidence: 0.8,
        reasoning: 'Supply chain management is important',
        configuration: {}
      }
    );
  } else if (/consulting|service|advisory|support|training/.test(combined)) {
    projectType = 'services';
    complexity = 'low';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.8,
        reasoning: 'Service-based projects benefit from service catalog management',
        configuration: { enableProducts: false, enableServices: true }
      },
      {
        module: 'team',
        confidence: 0.7,
        reasoning: 'Team management helps with resource allocation',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.8,
        reasoning: 'Project timeline tracking is important for deliverables',
        configuration: {}
      }
    );
  } else {
    // Default recommendation for general projects
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.6,
        reasoning: 'Most projects benefit from catalog management',
        configuration: {}
      },
      {
        module: 'team',
        confidence: 0.7,
        reasoning: 'Team management is useful for most projects',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.8,
        reasoning: 'Timeline tracking helps with project organization',
        configuration: {}
      }
    );
  }

  return {
    recommendations,
    projectType,
    complexity,
    suggestedModules: recommendations.map(r => r.module)
  };
}
