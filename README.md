# Admin Monorepo

A modern admin application built with a monorepo architecture using Turborepo, featuring a shared component library, design tokens, and Next.js web application.

## 🏗️ Architecture

This monorepo includes the following packages and applications:

### Applications
- **`@admin/web`** - Next.js web application with Tailwind CSS

### Packages
- **`@admin/design-tokens`** - Centralized design system with colors, spacing, typography, and other design tokens
- **`@admin/ui`** - Reusable React component library with Storybook documentation
- **`@admin/eslint-config`** - Shared ESLint configurations
- **`@admin/typescript-config`** - Shared TypeScript configurations

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd Admin
```

2. Install dependencies:
```bash
npm install
```

3. Build all packages:
```bash
npm run build
```

### Development

Start the development servers:

```bash
# Start all development servers
npm run dev

# Or start individual packages
npm run dev --workspace=apps/web          # Next.js app on http://localhost:3000
npm run storybook --workspace=packages/ui # Storybook on http://localhost:6006
```

## 📦 Package Details

### Design Tokens (`@admin/design-tokens`)

Centralized design system providing:
- **Colors**: Primary, secondary, success, warning, error, and neutral color palettes
- **Spacing**: Consistent spacing scale from 0 to 96
- **Typography**: Font families, sizes, weights, and line heights
- **Shadows**: Box shadows and drop shadows
- **Border Radius**: Consistent border radius values

The tokens are exported as:
- TypeScript objects for programmatic use
- CSS custom properties for styling
- Tailwind CSS configuration

### UI Components (`@admin/ui`)

React component library featuring:
- **Button**: Multiple variants (default, secondary, outline, ghost, link, destructive) and sizes
- **Card**: Flexible card components with header, content, and footer sections
- **Utilities**: Helper functions for className merging

All components are:
- Built with TypeScript for type safety
- Styled with Tailwind CSS using design tokens
- Documented in Storybook with interactive examples
- Designed to be "dumb" presentational components

### Web Application (`@admin/web`)

Next.js application showcasing:
- Modern admin dashboard interface
- Integration with the component library
- Tailwind CSS styling with design tokens
- TypeScript throughout
- Responsive design

## 🛠️ Available Scripts

### Root Level
```bash
npm run build          # Build all packages and applications
npm run dev           # Start all development servers
npm run lint          # Lint all packages
npm run check-types   # Type check all packages
npm run storybook     # Start Storybook for UI components
```

### Package Level
```bash
# Design Tokens
npm run build --workspace=packages/design-tokens
npm run dev --workspace=packages/design-tokens

# UI Components
npm run build --workspace=packages/ui
npm run storybook --workspace=packages/ui

# Web Application
npm run build --workspace=apps/web
npm run dev --workspace=apps/web
```

## 🎨 Design System

The design system is built around the concept of design tokens, providing:

1. **Consistent Visual Language**: All components use the same color palette, spacing, and typography
2. **Scalability**: Easy to add new tokens or modify existing ones
3. **Framework Agnostic**: Tokens can be used in any CSS framework or vanilla CSS
4. **Type Safety**: Full TypeScript support for all design tokens

## 📚 Storybook

Interactive component documentation is available at `http://localhost:6006` when running:

```bash
npm run storybook --workspace=packages/ui
```

Storybook provides:
- Interactive component playground
- Documentation for all component props
- Visual testing of different component states
- Accessibility testing tools

## 🏗️ Build System

The monorepo uses Turborepo for:
- **Parallel Execution**: Build and test packages in parallel
- **Smart Caching**: Cache build outputs to speed up subsequent builds
- **Dependency Management**: Automatically handle package dependencies
- **Pipeline Orchestration**: Run tasks in the correct order

## 🔧 Development Workflow

1. **Make Changes**: Edit components, tokens, or applications
2. **Hot Reload**: Changes are automatically reflected in development servers
3. **Type Check**: TypeScript provides immediate feedback
4. **Test in Storybook**: Verify component behavior in isolation
5. **Build**: Ensure everything builds correctly before committing

## 📁 Project Structure

```
Admin/
├── apps/
│   └── web/                 # Next.js web application
├── packages/
│   ├── design-tokens/       # Design system tokens
│   ├── ui/                  # React component library
│   ├── eslint-config/       # Shared ESLint config
│   └── typescript-config/   # Shared TypeScript config
├── package.json             # Root package.json with workspaces
├── turbo.json              # Turborepo configuration
└── README.md               # This file
```

## 🤝 Contributing

1. Create a new branch for your feature
2. Make your changes
3. Ensure all packages build successfully
4. Test components in Storybook
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
