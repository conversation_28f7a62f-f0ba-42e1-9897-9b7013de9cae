// Test script for task assignment command parsing
const testCommands = [
  "assign task to <PERSON> in Sample Project",
  "assign a new task to <PERSON>",
  "create task for <PERSON> in project ABC",
  "assign installation task to <PERSON>",
  "add task to <PERSON> project",
  "create new employee named <PERSON>",
  "add employee <PERSON>",
  "assign task to <PERSON> in Kitchen Renovation project"
];

async function testCommand(command) {
  try {
    const response = await fetch('http://localhost:3001/api/ai/parse-command', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ command })
    });

    const result = await response.json();
    
    console.log(`\n🧪 Testing: "${command}"`);
    console.log('📊 Result:', JSON.stringify(result, null, 2));
    
    // Analysis
    if (result.action && result.entityType) {
      console.log(`✅ Action: ${result.action}`);
      console.log(`✅ Entity: ${result.entityType}`);
      if (result.editType) console.log(`✅ Edit Type: ${result.editType}`);
      if (result.extractedData) console.log(`✅ Extracted: ${JSON.stringify(result.extractedData)}`);
      console.log(`✅ Confidence: ${result.confidence}`);
    } else {
      console.log('❌ Failed to parse command properly');
    }
    
    return result;
  } catch (error) {
    console.error(`❌ Error testing "${command}":`, error.message);
    return null;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Task Assignment Command Tests\n');
  
  for (const command of testCommands) {
    await testCommand(command);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }
  
  console.log('\n✅ All tests completed!');
}

// Run the tests
runAllTests().catch(console.error);
