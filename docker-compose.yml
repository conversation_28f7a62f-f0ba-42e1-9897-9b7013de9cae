version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: admin-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: admin_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - admin-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    container_name: admin-backend
    restart: unless-stopped
    environment:
      DATABASE_URL: "********************************************/admin_db?schema=public"
      NEXTAUTH_SECRET: "your-secret-key-here"
      NEXTAUTH_URL: "http://localhost:3001"
      NODE_ENV: "development"
      API_BASE_URL: "http://localhost:3001/api"
      FRONTEND_URL: "http://localhost:3000"
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - admin-network
    command: npm run dev

  # Frontend Web App
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    container_name: admin-web
    restart: unless-stopped
    environment:
      NEXT_PUBLIC_API_URL: "http://localhost:3001/api"
      NODE_ENV: "development"
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./apps/web:/app
      - /app/node_modules
      - /app/.next
    networks:
      - admin-network
    command: npm run dev

  # Prisma Studio (Database GUI)
  prisma-studio:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    container_name: admin-prisma-studio
    restart: unless-stopped
    environment:
      DATABASE_URL: "********************************************/admin_db?schema=public"
    ports:
      - "5555:5555"
    depends_on:
      - postgres
    volumes:
      - ./apps/backend:/app
    networks:
      - admin-network
    command: npx prisma studio --port 5555 --hostname 0.0.0.0
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local

networks:
  admin-network:
    driver: bridge
