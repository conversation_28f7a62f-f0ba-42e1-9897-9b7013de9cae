{"name": "admin-monorepo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "storybook": "turbo run storybook", "build-storybook": "turbo run build-storybook", "db:generate": "cd apps/backend && npx prisma generate", "db:push": "cd apps/backend && npx prisma db push", "db:migrate": "cd apps/backend && npx prisma migrate dev", "db:studio": "cd apps/backend && npx prisma studio", "db:seed": "cd apps/backend && npx prisma db seed", "backend:dev": "cd apps/backend && npm run dev", "web:dev": "cd apps/web && npm run dev"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "engines": {"node": ">=18"}, "packageManager": "npm@11.4.0", "workspaces": ["apps/*", "packages/*"], "dependencies": {"openai": "^5.6.0"}}