// Test script to check available projects
async function testProjectLookup() {
  try {
    // Test the frontend command parser directly
    const response = await fetch('http://localhost:3000/api/ai/parse-command', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        command: "assign task to <PERSON> in Sample Project"
      })
    });

    const parseResult = await response.json();
    console.log('🧪 Parse Result:', JSON.stringify(parseResult, null, 2));

    // Now test the full command processing
    const processResponse = await fetch('http://localhost:3000/api/ai/process-command', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        command: "assign task to <PERSON> in Sample Project"
      })
    });

    const processResult = await processResponse.json();
    console.log('🚀 Process Result:', JSON.stringify(processResult, null, 2));

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testProjectLookup();
