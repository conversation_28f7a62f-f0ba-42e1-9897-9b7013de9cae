# Backend Migration Guide

This guide will help you transition from mock data to the real backend with PostgreSQL database.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
./setup-backend.sh
```

### Option 2: Docker Setup
```bash
docker-compose up -d
```

### Option 3: Manual Setup
Follow the detailed steps below.

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL 15+ (or Docker)
- npm or pnpm

## 🔧 Manual Setup Steps

### 1. Install PostgreSQL

**macOS (Homebrew):**
```bash
brew install postgresql@15
brew services start postgresql@15
```

**Ubuntu/Debian:**
```bash
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql
```

**Windows:**
Download from https://www.postgresql.org/download/windows/

### 2. Create Database
```bash
createdb admin_db
# OR connect to PostgreSQL and run: CREATE DATABASE admin_db;
```

### 3. Configure Environment Variables

Update `apps/backend/.env`:
```env
DATABASE_URL="postgresql://username:password@localhost:5432/admin_db?schema=public"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3001"
NODE_ENV="development"
API_BASE_URL="http://localhost:3001/api"
FRONTEND_URL="http://localhost:3000"
```

Update `apps/web/.env.local`:
```env
NEXT_PUBLIC_API_URL="http://localhost:3001/api"
NODE_ENV="development"
```

### 4. Install Dependencies
```bash
npm install
```

### 5. Setup Database Schema
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed with sample data
npm run db:seed
```

## 🔄 Migration Process

### Step 1: Update Frontend Contexts

Replace mock data imports in your React contexts:

**Before:**
```typescript
import { dataStore } from '../services/dataStore';
import { contextDataService } from '../services/contextDataService';
```

**After:**
```typescript
import { backendDataStore } from '../services/backendDataStore';
import { backendContextDataService } from '../services/backendContextDataService';
```

### Step 2: Update Context Providers

In your context files (e.g., `TeamContext.tsx`, `CatalogContext.tsx`):

**Before:**
```typescript
const teamMembers = await dataStore.getTeamMembers();
```

**After:**
```typescript
// Set the current user ID first
backendDataStore.setCurrentUserId(currentUserId);
const teamMembers = await backendDataStore.getTeamMembers();
```

### Step 3: Update Service Calls

Replace direct API calls with the backend service:

**Before:**
```typescript
import { dataStore } from '../services/dataStore';
const projects = await dataStore.getProjects();
```

**After:**
```typescript
import { backendDataStore } from '../services/backendDataStore';
backendDataStore.setCurrentUserId(userId);
const projects = await backendDataStore.getProjects();
```

## 🏃‍♂️ Running the Application

### Development Mode
```bash
# Terminal 1: Start backend
npm run backend:dev

# Terminal 2: Start frontend  
npm run web:dev
```

### Using Turbo (Recommended)
```bash
# Start both frontend and backend
npm run dev
```

### Docker Mode
```bash
# Start all services
docker-compose up

# Start with Prisma Studio
docker-compose --profile tools up
```

## 🔍 Verification

### 1. Check Backend Health
```bash
curl http://localhost:3001/api/health
```

### 2. Test API Endpoints
```bash
# Get users
curl http://localhost:3001/api/users

# Get team members (replace USER_ID)
curl "http://localhost:3001/api/team?userId=USER_ID"
```

### 3. Database Management
```bash
# Open Prisma Studio
npm run db:studio

# View database in browser: http://localhost:5555
```

## 🛠 Troubleshooting

### Database Connection Issues
1. Verify PostgreSQL is running: `pg_isready`
2. Check DATABASE_URL in `.env`
3. Ensure database exists: `psql -l`

### Migration Errors
```bash
# Reset database
npm run db:push --force-reset

# Regenerate client
npm run db:generate
```

### Port Conflicts
- Backend: Change port in `apps/backend/package.json`
- Frontend: Change port in `apps/web/package.json`
- Database: Change port in `docker-compose.yml`

## 📊 Database Schema

The backend includes these main entities:
- **Users**: Business owners/admins
- **TeamMembers**: Employees and contractors
- **Projects**: Business projects with modules
- **CatalogItems**: Products and services
- **InventoryItems**: Materials and supplies
- **FinancialRecords**: Income/expense records
- **FinancialMovements**: Detailed transactions

## 🔐 Security Notes

- Change default passwords in production
- Use environment variables for secrets
- Enable SSL for database connections in production
- Implement proper authentication/authorization

## 📈 Performance Tips

- Use database indexes for frequently queried fields
- Implement pagination for large datasets
- Use connection pooling in production
- Monitor database performance with Prisma metrics

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Run database migrations: `npm run db:migrate`
3. Build application: `npm run build`
4. Start production server: `npm start`

### Frontend Deployment
1. Update `NEXT_PUBLIC_API_URL` to production backend URL
2. Build application: `npm run build`
3. Deploy static files or use Next.js deployment

## 📞 Support

If you encounter issues:
1. Check the logs: `docker-compose logs` or terminal output
2. Verify environment variables are set correctly
3. Ensure all dependencies are installed
4. Check database connectivity

## 🎯 Next Steps

After successful migration:
1. Implement user authentication
2. Add data validation and error handling
3. Set up monitoring and logging
4. Implement backup strategies
5. Add API rate limiting
6. Set up CI/CD pipelines
