#!/bin/bash

# Backend Setup Script for Admin Project
# This script helps set up the PostgreSQL database and backend services

echo "🚀 Setting up Admin Backend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if PostgreSQL is installed
check_postgresql() {
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL is installed"
        return 0
    else
        print_warning "PostgreSQL is not installed"
        return 1
    fi
}

# Install PostgreSQL (macOS)
install_postgresql_mac() {
    if command -v brew &> /dev/null; then
        echo "Installing PostgreSQL via Homebrew..."
        brew install postgresql@15
        brew services start postgresql@15
        print_status "PostgreSQL installed and started"
    else
        print_error "Homebrew not found. Please install PostgreSQL manually:"
        echo "1. Install Homebrew: https://brew.sh/"
        echo "2. Run: brew install postgresql@15"
        echo "3. Run: brew services start postgresql@15"
        exit 1
    fi
}

# Create database
create_database() {
    echo "Creating database 'admin_db'..."
    
    # Try to create database
    if psql -U postgres -c "CREATE DATABASE admin_db;" 2>/dev/null; then
        print_status "Database 'admin_db' created successfully"
    elif psql -c "CREATE DATABASE admin_db;" 2>/dev/null; then
        print_status "Database 'admin_db' created successfully"
    else
        print_warning "Database might already exist or user needs to be created"
        echo "Please run manually:"
        echo "  createdb admin_db"
        echo "Or connect to PostgreSQL and run:"
        echo "  CREATE DATABASE admin_db;"
    fi
}

# Setup environment variables
setup_env() {
    echo "Setting up environment variables..."
    
    # Backend .env
    if [ ! -f "apps/backend/.env" ]; then
        cp apps/backend/.env apps/backend/.env.backup 2>/dev/null || true
        
        # Get PostgreSQL connection details
        echo "Please provide PostgreSQL connection details:"
        read -p "PostgreSQL host (default: localhost): " PG_HOST
        PG_HOST=${PG_HOST:-localhost}
        
        read -p "PostgreSQL port (default: 5432): " PG_PORT
        PG_PORT=${PG_PORT:-5432}
        
        read -p "PostgreSQL username (default: postgres): " PG_USER
        PG_USER=${PG_USER:-postgres}
        
        read -s -p "PostgreSQL password: " PG_PASSWORD
        echo
        
        read -p "Database name (default: admin_db): " PG_DB
        PG_DB=${PG_DB:-admin_db}
        
        # Create .env file
        cat > apps/backend/.env << EOF
# Database
DATABASE_URL="postgresql://${PG_USER}:${PG_PASSWORD}@${PG_HOST}:${PG_PORT}/${PG_DB}?schema=public"

# Next.js
NEXTAUTH_SECRET="$(openssl rand -base64 32)"
NEXTAUTH_URL="http://localhost:3001"

# Development
NODE_ENV="development"

# API Configuration
API_BASE_URL="http://localhost:3001/api"
FRONTEND_URL="http://localhost:3000"
EOF
        
        print_status "Backend .env file created"
    else
        print_status "Backend .env file already exists"
    fi
}

# Install dependencies
install_dependencies() {
    echo "Installing dependencies..."
    npm install
    print_status "Dependencies installed"
}

# Generate Prisma client
generate_prisma() {
    echo "Generating Prisma client..."
    cd apps/backend
    npx prisma generate
    print_status "Prisma client generated"
    cd ../..
}

# Push database schema
push_schema() {
    echo "Pushing database schema..."
    cd apps/backend
    npx prisma db push
    print_status "Database schema pushed"
    cd ../..
}

# Seed database
seed_database() {
    echo "Seeding database with sample data..."
    cd apps/backend
    npx prisma db seed
    print_status "Database seeded"
    cd ../..
}

# Main setup process
main() {
    echo "Starting backend setup process..."
    echo
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "apps/backend" ]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Install dependencies first
    install_dependencies
    
    # Check PostgreSQL
    if ! check_postgresql; then
        echo
        echo "PostgreSQL installation options:"
        echo "1. Install via Homebrew (macOS)"
        echo "2. Install manually"
        echo "3. Use Docker"
        echo "4. Skip database setup"
        echo
        read -p "Choose option (1-4): " choice
        
        case $choice in
            1)
                install_postgresql_mac
                ;;
            2)
                print_warning "Please install PostgreSQL manually and run this script again"
                echo "Installation guides:"
                echo "- macOS: https://postgresapp.com/"
                echo "- Ubuntu: sudo apt-get install postgresql postgresql-contrib"
                echo "- Windows: https://www.postgresql.org/download/windows/"
                exit 1
                ;;
            3)
                print_warning "Docker setup not implemented in this script"
                echo "To use Docker, create a docker-compose.yml file with PostgreSQL"
                exit 1
                ;;
            4)
                print_warning "Skipping database setup"
                echo "You'll need to set up PostgreSQL manually later"
                ;;
        esac
    fi
    
    # Setup environment
    setup_env
    
    # Generate Prisma client
    generate_prisma
    
    # Only proceed with database operations if PostgreSQL is available
    if check_postgresql; then
        # Create database
        create_database
        
        # Push schema
        push_schema
        
        # Seed database
        seed_database
    fi
    
    echo
    print_status "Backend setup complete!"
    echo
    echo "Next steps:"
    echo "1. Start the backend: npm run backend:dev"
    echo "2. Start the frontend: npm run web:dev"
    echo "3. Open http://localhost:3000 for the frontend"
    echo "4. Open http://localhost:3001/api/health for backend health check"
    echo
    echo "Database management:"
    echo "- View database: npm run db:studio"
    echo "- Reset database: npm run db:push --force-reset"
    echo "- Create migration: npm run db:migrate"
}

# Run main function
main "$@"
